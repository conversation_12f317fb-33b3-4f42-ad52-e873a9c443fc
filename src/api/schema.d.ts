/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export type paths = {
  '/': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get: operations['root']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/accountBankNumberSanityCheck': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    post: operations['accountBankNumberSanityCheck']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/addAccountMapping': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Create account mapping for open, parentless account. Return mapping of the parent account */
    post: operations['addAccountMapping']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/addAnalysisResultOption': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Add a new Analysis Result Option */
    post: operations['addAnalysisResultOption']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/addBankOptions': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Add a new Bank Option */
    post: operations['addBankOptions']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/addIndexRate': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Add a new Index Rate */
    post: operations['addIndexRate']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/addService': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Adds a new Service */
    post: operations['addService']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/addServiceCategory': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Adds a new Service Category */
    post: operations['addServiceCategory']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/addStatementMessage': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Add a new Statement Message */
    post: operations['addStatementMessage']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/addUserFieldConfiguration': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Add new user field configuration */
    post: operations['createUserFieldConfiguration']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/analyze': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    post: operations['analyze']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/auth': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get: operations['auth']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/changeLogLevel': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Change Logging level at root */
    post: operations['changeLogLevel']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/checkAddressCodeAvailability': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Check availability of an address code and suggest next available number if needed */
    post: operations['checkAddressCodeAvailability']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/createAccountType': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Create account type */
    post: operations['createAccountType']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/createAccountTypeOverride': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Create an account type override */
    post: operations['createCompositeAccountTypeOverride']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/createAddress': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Create an app generated address */
    post: operations['createAddress']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/createBranch': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Create Branch */
    post: operations['createBranch']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/createCompositeAccount': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Create a composite account */
    post: operations['createCompositeAccount']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/createCycleDefinition': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Add a new Cycle Definition */
    post: operations['createCycleDefinition']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/createDemographicPriceList': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Create a new demographic price list */
    post: operations['createDemographicPriceList']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/createEarningsCreditDefinition': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Create a Earnings Credit Definition */
    post: operations['createEarningsCreditDefinition']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/createInvestableBalanceDefinition': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Create an Investable Balance Definition */
    post: operations['createInvestableBalanceDefinition']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/createOfficer': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Create Officer */
    post: operations['createOfficer']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/createRequiredBalanceDefinition': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Create a Required Balance Definition */
    post: operations['createRequiredBalanceDefinition']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/createReserveRequirementDefinition': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Add a new Reserve Requirement definition */
    post: operations['createReserveRequirementDefinition']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/createServiceOverride': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Create a new service override */
    post: operations['createServiceOverride']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/createStatementFormatPlan': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Create a new Statement Format Plan */
    post: operations['createStatementFormatPlan']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/createUserFieldSelections': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Create user field selections */
    post: operations['createUserFieldSelections']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/deleteServiceOverride': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** edit eligible service override from a service if they exist */
    post: operations['deleteServiceOverride']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/editServiceOverride': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** edit an existing service override */
    post: operations['editServiceOverride']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getAccount': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get account details by account code */
    post: operations['getAccountWithKey']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getAccountMappings': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get account mappings as of a specific date */
    post: operations['getMappingsOfKeyAccountAsOf']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getAccountTimeline': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get unique dates of account updates */
    post: operations['getAccountTimeline']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getAccountType': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get account type */
    post: operations['getAccountType']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getAccountTypeOverride': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get account type overrides */
    post: operations['getAccountTypeOverride']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getAccountTypeTimeline': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get timeline values for an account type */
    post: operations['getAccountType_1']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getAccountTypes': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get account types */
    post: operations['getAccountTypes']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getAccounts': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get list of accounts by effective date */
    post: operations['getAccounts']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getAccountsByCodes': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get list of accounts through account codes */
    post: operations['getAccountsByCodes']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getAddress': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get an address */
    post: operations['getAddress']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getAddresses': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get addresses for an account */
    post: operations['getAddresses']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getAnalysisResultOptionsByCode': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get list of analysis result options through a code */
    post: operations['getAnalysisResultOptionsByCode']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getBankOptionsByCode': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get a Bank Option with code */
    post: operations['getBankOptionsByCode']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getBranch': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get branch by effective date and code */
    post: operations['getBranch']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getBranchTimeline': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get branch timeline */
    post: operations['retrieveBranchTimeline']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getBranches': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get branches by effective date */
    post: operations['getBranches']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getCycleDefinition': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get Cycle Definition by code and an effective date */
    post: operations['getCycleDefinition']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getCycleDefinitionByCode': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get list of cycle definition versions by code */
    post: operations['getCycleDefinitionByCode']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getCycleDefinitions': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** List all Cycle Definition by an effective date */
    post: operations['getCycleDefinitions']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getDemographicCriteria': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get a demographic criteria as of a specific date */
    post: operations['getDemographicCriteria']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getDemographicPriceList': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get a demographic price list as of a specific date */
    post: operations['getDemographicPriceList']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getDemographicPriceListRanking': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get a demographic price list ranking as of a specific date */
    post: operations['getDemographicPriceListRanking']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getDemographicPriceListVersions': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get the versions of a demographic price list */
    post: operations['getDemographicPriceListVersions']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getDemographicPriceLists': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get list of demographic price list for list of codes as of a specific date */
    post: operations['getDemographicPriceLists']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getDepositAccounts': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get list of deposit accounts by effective date */
    post: operations['getDepositAccounts']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getEarningsCreditDefinition': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get a Earnings Credit Definition by code and effective date */
    post: operations['getEarningsCreditDefinition']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getEarningsCreditDefinitions': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get Earnings Credit Definitions by effective date */
    post: operations['getEarningsCreditDefinitions']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getEarningsCreditDefinitionsByCode': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get Earnings Credit Definitions by code */
    post: operations['getEarningsCreditDefinitionsByCode']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getEarningsCreditForAccount': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    post: operations['getEarningsCreditForAccount']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getIndexRatesByCode': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get list of index rates through an index rate code */
    post: operations['getIndexRatesByCode']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getInvestableBalanceDefinition': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get an Investable Balance Definition by an effective date and code */
    post: operations['getInvestableBalanceDefinition']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getInvestableBalanceDefinitionByCode': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get an Investable Balance Definition by code */
    post: operations['getInvestableBalanceDefinitionByCode']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getInvestableBalanceDefinitions': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** List all Investable Balance Definitions by an effective date */
    post: operations['getInvestableBalanceDefinitions']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getLeadCompositeAccountCode': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Return the lead composite account for a given account code */
    post: operations['getLeadCompositeAccountCode']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getNextAvailableAccountNumber': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get next available account number */
    post: operations['getNextAvailableAccountNumber']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getOfficer': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get officer by effective date and code */
    post: operations['retrieveOfficer']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getOfficerTimeline': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get officer timeline */
    post: operations['retrieveOfficerTimeline']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getOfficers': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get officers by effective date */
    post: operations['getOfficers']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getParentlessOpenAccounts': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get parentless and open accounts */
    post: operations['getParentlessOpenAccounts']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getPromotionalPriceList': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get a promotional price list as of a specific date */
    post: operations['getPromotionalPriceList']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getRequiredBalanceDefinition': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get a Required Balance Definition by effective date */
    post: operations['getRequiredBalanceDefinition']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getRequiredBalanceDefinitionByCode': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get a Required Balance Definition by code */
    post: operations['getRequiredBalanceDefinitionByCode']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getRequiredBalanceDefinitions': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** List all Required Balance Definitions by an effective date */
    post: operations['getRequiredBalanceDefinitions']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getReserveRequirementDefinitions': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** List all reserve requirement definitions by an effective date */
    post: operations['getReserveRequirementDefinitions']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getReserveRequirementsByCode': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** List all reserve requirement definitions by code */
    post: operations['getReserveRequirementsByCode']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getServiceCatalogMapping': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Gets service catalog mappings */
    post: operations['getServiceCatalogMapping']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getServiceDetailsAsOf': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Gets service details given code and effective date */
    post: operations['getServiceDetailsAsOf']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getServiceOverridesByAccount': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Gets service override details given account code and effective date */
    post: operations['getServiceOverrideDetailsForAccount']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getServicePricesAsOf': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Gets filtered list of service prices */
    post: operations['getServicePricesAsOf']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getServiceTimeline': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Gets unique dates of service updates */
    post: operations['getServiceTimeline']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getSettlementProcessingOptions': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** get settlement and processing options */
    post: operations['getSettlementProcessingOptions']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getStatementFormatPlans': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get Statement Format Plans by effective date */
    post: operations['getStatementFormatPlans']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getStatementFormatPlansByCode': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get Statement Format Plans by code */
    post: operations['getStatementFormatPlanByCode']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getStatementMessageByCode': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get all Statement Message versions by code */
    post: operations['getStatementMessageByCode']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getUserFieldSelections': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Retrieve user field selections for a given account */
    post: operations['getUserFieldSelections']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/getUserFieldsConfigurations': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Retrieve all user field configurations */
    post: operations['getUserFieldConfigurations']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/grabAccountMappings': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    post: operations['grabAccountMappings']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/grabAllAccounts': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    post: operations['grabAllAccounts']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/health': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get: operations['healthCheck']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/listAllKeyAccountMappings': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Get all key account mappings */
    post: operations['listAllKeyAccountMappings']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/listAnalysisResultOptionsByEffectiveDate': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** List all Analysis Result Option by an effective date */
    post: operations['listAnalysisResultOptionsByEffectiveDate']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/listBankOptionsByEffectiveDate': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** List Bank Options by an effective date */
    post: operations['getBankOptions']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/listDemographicPriceLists': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** List all demographic price lists as of a specific date */
    post: operations['listDemographicPriceLists']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/listIndexRatesByEffectiveDate': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** List all index rates by an effective date */
    post: operations['listIndexRatesByEffectiveDate']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/listMetrics': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get: operations['listMetrics']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/listPromotionalPriceLists': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** List all promotional price lists as of a specific date */
    post: operations['listPromotionalPriceLists']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/listStatementMessagesByEffectiveDate': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** List all Statement Message by an effective date */
    post: operations['listStatementMessageByEffectiveDate']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/listStatementPackages': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** List statement packages */
    post: operations['listStatementPackages']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/populateAccountMappings': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    post: operations['populateAccountMappings']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/populateBankNumber': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    post: operations['populateBankNumber']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/removeAccountMappings': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Remove account mappings as of a specific date */
    post: operations['removeMappingsOfAccountAsOf']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/removeKeyAccount': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Given a parent account code, remove the key account associated with it */
    post: operations['removeKeyAccountFromParent']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/removeKeyAccountMapping': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Remove key account mapping */
    post: operations['removeKeyAccountMapping']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/setDemographicPriceListRanking': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Set a demographic price list ranking for a specific date */
    post: operations['setDemographicPriceListRanking']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/setKeyAccount': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Given a parent and a child (child can be multiple relations down)set the child to the parent as a key account */
    post: operations['setKeyAccount']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/tagServiceOverride': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** add service override to a service if they don't exist */
    post: operations['tagServiceOverride']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/testGet': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get: operations['testGet']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/testGetDailyBalanceHistories': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    post: operations['testGetDailyBalanceHistories']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/testGetDemoUpdateRecords': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    post: operations['testGetDemoUpdateRecords']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/testPost': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    post: operations['testPost']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/updateAccount': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Update an existing account */
    post: operations['updateAccount']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/updateAccountMappings': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Update list of accounts to point to new parent */
    post: operations['updateAccountMappings']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/updateAccountType': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Update account type */
    post: operations['updateAccountType']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/updateAddress': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Update an address */
    post: operations['updateAddress']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/updateAnalysisResultOption': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Update an existing Analysis Result Option */
    post: operations['updateAnalysisResultOption']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/updateBankOptions': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Update a Bank Option */
    post: operations['updateBankOptions']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/updateCycleDefinition': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Update an existing Cycle Definition */
    post: operations['updateCycleDefinition']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/updateDemographicPriceList': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Update an existing demographic price list */
    post: operations['updateDemographicPriceList']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/updateEarningsCreditDefinition': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Update Earnings Credit Definition */
    post: operations['updateEarningsCreditDefinition']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/updateIndexRate': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Update an existing Index Rate */
    post: operations['updateIndexRate']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/updateInvestableBalanceDefinition': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Update an Investable Balance Definition by code */
    post: operations['updateInvestableBalanceDefinition']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/updateRequiredBalanceDefinition': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Update a Required Balance Definition by code */
    post: operations['updateRequiredBalanceDefinition']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/updateReserveRequirementDefinition': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Update an existing Reserve Requirement definition */
    post: operations['updateReserveRequirementDefinition']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/updateService': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Updates an existing Service */
    post: operations['updateService']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/updateServiceCategory': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Updates an existing Service Category */
    post: operations['updateServiceCategory']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/updateSettlementProcessingOptions': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Update settlement and processing options */
    post: operations['updateSettlementProcessingOptions']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/updateStatementFormatPlan': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Update Statement Format Plan */
    post: operations['updateStatementFormatPlan']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/updateStatementMessage': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Update an existing Statement Message */
    post: operations['updateStatementMessage']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/updateStatementPackage': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Create a statement package */
    post: operations['updateStatementPackage']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/updateUserFieldConfiguration': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Update an existing user field configuration */
    post: operations['updateUserFieldConfiguration']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/upsertKeyAccountMapping': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    get?: never
    put?: never
    /** Upsert a key account mapping */
    post: operations['upsertKeyAccountMapping']
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
}
export type webhooks = Record<string, never>
export type components = {
  schemas: {
    Account: {
      accountNumber: string
      /** @enum {string} */
      accountStatus?: 'Z' | 'B' | 'C' | 'P' | 'X'
      analysisAccountTypeCode: string
      /** @enum {string} */
      applicationId: 'C' | 'D'
      bankNumber: string
      branchCode: string
      /** Format: date */
      closeDate?: string
      costCenter: string
      currencyCode: string
      /** @description The customer-specific pricing can not be true for deposit accounts and must be present for composite accounts */
      customerSpecificPricingIndicator?: boolean
      depositAccountTypeCode?: string
      /** @enum {string} */
      depositCategory?:
        | 'A'
        | 'C'
        | 'D'
        | 'L'
        | 'M'
        | 'N'
        | 'R'
        | 'S'
        | 'P'
        | 'T'
      /** Format: date */
      effectiveDate: string
      /** Format: date */
      openDate: string
      primaryOfficerCode: string
      /** @enum {string} */
      processingIndicator?: 'A' | 'B'
      secondaryOfficerCode?: string
      shortName: string
      treasuryOfficerCode?: string
    }
    AccountCode: {
      accountNumber: string
      /** @enum {string} */
      applicationId: 'C' | 'D'
      bankNumber: string
    }
    AccountCodeWithEffectivedateReq: {
      accountCode: components['schemas']['AccountCode']
      /** Format: date */
      effectiveDate: string
    }
    AccountCodesDateLeadAccountRequest: {
      accountCodes: components['schemas']['AccountCode'][]
      /** Format: date */
      effectiveDate: string
      leadAccount: components['schemas']['AccountCode']
    }
    AccountCodesRetrieveRequest: {
      accountCodes: components['schemas']['AccountCode'][]
      /** Format: date */
      effectiveDate: string
    }
    AccountEffectiveDateRequest: {
      accountNumber: string
      /** @enum {string} */
      applicationId: 'C' | 'D'
      bankNumber: string
      /** Format: date */
      effectiveDate: string
    }
    AccountEntity: {
      accountNumber: string
      /** @enum {string} */
      accountStatus?: 'Z' | 'B' | 'C' | 'P' | 'X'
      analysisAccountTypeCode: string
      /** @enum {string} */
      applicationId: 'C' | 'D'
      bankNumber: string
      branchCode: string
      /** Format: date */
      closeDate?: string
      code?: string
      costCenter: string
      currencyCode: string
      customerSpecificPricingIndicator?: boolean
      depositAccountTypeCode?: string
      /** @enum {string} */
      depositCategory?:
        | 'A'
        | 'C'
        | 'D'
        | 'L'
        | 'M'
        | 'N'
        | 'R'
        | 'S'
        | 'P'
        | 'T'
      /** Format: date */
      effectiveDate: string
      /** Format: date */
      openDate: string
      primaryOfficerCode: string
      /** @enum {string} */
      processingIndicator?: 'A' | 'B'
      secondaryOfficerCode?: string
      shortName?: string
      treasuryOfficerCode?: string
    }
    AccountMappingCreateRequest: {
      childAccountCode: components['schemas']['AccountCode']
      /** Format: date */
      effectiveDate: string
      parentAccountCode: components['schemas']['AccountCode']
    }
    AccountMappingEntity: {
      code?: string
      /** Format: date */
      effectiveDate: string
      /** Format: date */
      endDate?: string
      parentCode?: string
    }
    AccountMappingUpdateRequest: {
      childAccountCodes: components['schemas']['AccountCode'][]
      /** Format: date */
      effectiveDate: string
      parentAccountCode: components['schemas']['AccountCode']
    }
    AccountType: {
      accountTypeCode: string
      analysisResultOptionsPlanCode: string
      balanceRequirementDefinitionCode: string
      description: string
      earningsCreditDefinitionCode?: string
      /** Format: date */
      effectiveDate: string
      interestRequirementDefinitionCode?: string
      investableBalanceDefinitionCode: string
      reserveRequirementDefinitionCode?: string
      settlementCyclePlanCode: string
      statementCyclePlanCode: string
      statementFormatPlanCode: string
      statementMessagePlanCode?: string
    }
    AccountTypeOverride: {
      accountCode: components['schemas']['AccountCode']
      analysisResultOptionsPlanCode?: string
      /** Format: date */
      analysisResultOptionsPlanCodeExpiry?: string
      balanceRequirementDefinitionCode?: string
      /** Format: date */
      balanceRequirementDefinitionCodeExpiry?: string
      chargeAccountCode?: string
      earningsCreditDefinitionCode?: string
      /** Format: date */
      earningsCreditDefinitionCodeExpiry?: string
      /** Format: date */
      effectiveDate: string
      interestRequirementDefinitionCode?: string
      /** Format: date */
      interestRequirementDefinitionCodeExpiry?: string
      investableBalanceDefinitionCode?: string
      /** Format: date */
      investableBalanceDefinitionCodeExpiry?: string
      isOverrideAsSettlementAccount: boolean
      reserveRequirementDefinitionCode?: string
      /** Format: date */
      reserveRequirementDefinitionCodeExpiry?: string
      settlementCyclePlanCode?: string
      /** Format: date */
      settlementCyclePlanCodeExpiry?: string
      statementCyclePlanCode?: string
      /** Format: date */
      statementCyclePlanCodeExpiry?: string
      statementFormatPlanCode?: string
      /** Format: date */
      statementFormatPlanCodeExpiry?: string
      statementMessagePlanCode?: string
      /** Format: date */
      statementMessagePlanCodeExpiry?: string
    }
    AccountWithKey: {
      accountNumber: string
      /** @enum {string} */
      accountStatus?: 'Z' | 'B' | 'C' | 'P' | 'X'
      analysisAccountTypeCode: string
      /** @enum {string} */
      applicationId: 'C' | 'D'
      bankNumber: string
      branchCode: string
      /** Format: date */
      closeDate?: string
      costCenter: string
      currencyCode: string
      /** @description The customer-specific pricing can not be true for deposit accounts and must be present for composite accounts */
      customerSpecificPricingIndicator?: boolean
      depositAccountTypeCode?: string
      /** @enum {string} */
      depositCategory?:
        | 'A'
        | 'C'
        | 'D'
        | 'L'
        | 'M'
        | 'N'
        | 'R'
        | 'S'
        | 'P'
        | 'T'
      /** Format: date */
      effectiveDate: string
      isKeyAccount?: boolean
      keyAccountCode?: components['schemas']['AccountCode']
      /** Format: date */
      openDate: string
      primaryOfficerCode: string
      /** @enum {string} */
      processingIndicator?: 'A' | 'B'
      secondaryOfficerCode?: string
      shortName: string
      treasuryOfficerCode?: string
    }
    Address: {
      accountCode: components['schemas']['AccountCode']
      addressLine2?: string
      addressLine3?: string
      addressLine4?: string
      addressLine5?: string
      addressLine6?: string
      addressLine7?: string
      /** Format: int32 */
      addressNumber: number
      /** @enum {string} */
      applicationId: 'A' | 'D'
      /** Format: date */
      effectiveDate: string
      name?: string
    }
    AddressCode: {
      accountCode: components['schemas']['AccountCode']
      /** Format: int32 */
      addressNumber: number
      /** @enum {string} */
      applicationId: 'A' | 'D'
    }
    AddressCodeAvailabilityRequest: {
      /** @enum {string} */
      accountApplicationId: 'C' | 'D'
      accountNumber: string
      /** @enum {string} */
      addressApplicationId: 'A' | 'D'
      /** Format: int32 */
      addressNumber: number
      bankNumber: string
    }
    AddressCodeAvailabilityResponse: {
      available: boolean
      /** Format: int32 */
      suggestedAddressNumber: number
    }
    AddressRetrieveRequest: {
      /** @enum {string} */
      accountApplicationId: 'C' | 'D'
      accountCode?: components['schemas']['AccountCode']
      accountNumber: string
      /** @enum {string} */
      addressApplicationId: 'A' | 'D'
      /** Format: int32 */
      addressNumber: number
      bankNumber: string
      /** Format: date */
      effectiveDate: string
    }
    AnalysisResultOption: {
      /** @enum {string} */
      analysisChargeType: 'DIRECT_DEBIT' | 'WAIVE'
      analysisDirectDebitTrailer?: string
      code: string
      /** Format: int32 */
      daysAfter?: number
      delay?: number
      /** Format: date */
      effectiveDate: string
      /** @enum {string} */
      excessCredits: 'WAIVE'
      /** @enum {string} */
      hardCharge: 'DIRECT_DEBIT' | 'WAIVE'
      hardDirectDebitTrailer?: string
      markdownRate?: number
      markdownStatementLabel?: string
      markupRate?: number
      markupStatementLabel?: string
      maxChargeWaiveAmount?: number
      minChargeWaiveAmount?: number
      name: string
      /** Format: int32 */
      settlementOverrideDateEachMonth?: number
      /** @enum {string} */
      settlementOverrideType:
        | 'NO_OVERRIDE'
        | 'SAME_DATE_EACH_MONTH'
        | 'SPECIFIC_DAYS_AFTER_PRELIM_ANALYSIS'
      /** Format: int32 */
      waiveCycle?: number
    }
    BalanceRequirementDefinition: {
      addOtherBalanceLabel?: string
      /** @enum {string} */
      baseBalanceType:
        | 'AVERAGE_COLLECTED'
        | 'AVERAGE_LEDGER'
        | 'AVERAGE_NEGATIVE_COLLECTED'
        | 'AVERAGE_NEGATIVE_LEDGER'
        | 'AVERAGE_POSITIVE_COLLECTED'
        | 'AVERAGE_POSITIVE_LEDGER'
        | 'AVERAGE_UNCOLLECTED_FUNDS'
        | 'COMPENSATING_BALANCE'
        | 'END_OF_MONTH_LEDGER'
        | 'INVESTABLE_BALANCE'
        | 'AVERAGE_FLOAT'
        | 'AVERAGE_CLEARINGHOUSE_FLOAT'
        | 'REQUIRED_BALANCE'
      code: string
      /** Format: date */
      effectiveDate: string
      name: string
      subtractCompensatingBalance: boolean
      subtractInterestPaidMonthToDate: boolean
      subtractOtherBalanceLabel?: string
    }
    BalanceTier: {
      indexAdjustmentRate?: number
      indexRate?: number
      maxTierExclusive: number
      minTierInclusive: number
    }
    BankOptions: {
      /** Format: int32 */
      accountNumberDigits: number
      accountNumberMasking: boolean
      accountNumberPattern: string
      assignDefaultStatementPackageToDepositAccount: boolean
      automaticPrelimAnalysis: boolean
      /** @enum {string} */
      balanceCycleDays: 'ACTUAL_DAYS_IN_MONTH' | 'OPEN_DAYS_IN_MONTH'
      /** @enum {string} */
      calculatingBalanceFeeBasis: '360' | '365' | '366'
      /** @enum {string} */
      calculatingEarningsCreditBasis: '360' | '365' | '366'
      code: string
      copyAccountTypeOfKeyAccount: boolean
      copyUserFieldsFromKeyAccount: boolean
      /** @enum {string} */
      defaultCurrency: 'US_DOLLARS'
      /** Format: int32 */
      defaultTermInMonthsForPromoAndOverrides: number
      /** @enum {string} */
      earningsCycleDays:
        | 'ACTUAL_DAYS_IN_MONTH'
        | 'OPEN_DAYS_IN_MONTH'
        | 'THIRTY'
      /** Format: date */
      effectiveDate: string
      /** @enum {string} */
      finalAnalysis: 'SAME_DATE_EACH_MONTH' | 'SPECIFIC_DAYS_POST_PRELIM'
      /** Format: int32 */
      finalAnalysisDays: number
      name: string
      /** @enum {string} */
      prelimAnalysis:
        | 'LastBusinessDayPriorMonth'
        | '1'
        | '2'
        | '3'
        | '4'
        | '5'
        | '6'
        | '7'
        | '8'
        | '9'
        | '10'
        | '11'
        | '12'
        | '13'
        | '14'
        | '15'
        | '16'
        | '17'
        | '18'
        | '19'
        | '20'
        | '21'
        | '22'
        | '23'
        | '24'
        | '25'
        | '26'
        | '27'
      /** Format: int32 */
      retentionMonths: number
      /** @enum {string} */
      statementArchivingFrequency:
        | 'AT_FINAL_AND_REANALYSIS'
        | 'AT_FINAL'
        | 'NOT_ARCHIVED'
    }
    Branch: {
      abaNumbers: string[]
      addressLine1: string
      addressLine2: string
      addressLine3: string
      bankNumber: string
      branchName: string
      code: string
      correspondentAccountNumber?: string
      /** Format: date */
      effectiveDate: string
      phoneNumber: string
    }
    CodeRequestBody: {
      code: string
    }
    CodesAndEffectiveDateRequestBody: {
      codes: string[]
      /** Format: date */
      effectiveDate: string
    }
    CompositeAccountCreateRequest: {
      account: components['schemas']['Account']
      accountTypeOverride?: components['schemas']['AccountTypeOverride']
      addresses?: components['schemas']['Address'][]
      childAccountCodes: components['schemas']['AccountCode'][]
      keyChildAccountCode?: components['schemas']['AccountCode']
      statementPackages?: components['schemas']['StatementPackageCreateUpdateRequest'][]
      userFieldSelections?: components['schemas']['UserFieldSelection'][]
    }
    CreateOrUpdateService: {
      service: components['schemas']['Service']
      serviceCategoryCode?: string
      serviceCodes?: string[]
      servicePrice: components['schemas']['ServicePrice'][]
    }
    CreateServiceCategory: {
      /** @description Provide null if service category has no parent Service Category */
      parentServiceCategoryCode?: string
      serviceCategory: components['schemas']['ServiceCategory']
    }
    CycleDefinition: {
      code: string
      /** @enum {string} */
      cycleType: 'STATEMENT' | 'SETTLEMENT'
      description: string
      /** Format: date */
      effectiveDate: string
      includedMonths?: (
        | 'JANUARY'
        | 'FEBRUARY'
        | 'MARCH'
        | 'APRIL'
        | 'MAY'
        | 'JUNE'
        | 'JULY'
        | 'AUGUST'
        | 'SEPTEMBER'
        | 'OCTOBER'
        | 'NOVEMBER'
        | 'DECEMBER'
      )[]
      isAprilSelected?: boolean
      isAugustSelected?: boolean
      isDecemberSelected?: boolean
      isFebruarySelected?: boolean
      isJanuarySelected?: boolean
      isJulySelected?: boolean
      isJuneSelected?: boolean
      isMarchSelected?: boolean
      isMaySelected?: boolean
      isNovemberSelected?: boolean
      isOctoberSelected?: boolean
      isSeptemberSelected?: boolean
    }
    DailyBalanceHistory: {
      accountNumber: string
      /** @enum {string} */
      applicationId: 'C' | 'D'
      bankNumber: string
      clearingHouseFundsFloat: number
      floatBalance: number
      isBusinessDay: boolean
      ledgerBalance: number
      /** Format: date */
      processDate: string
      todaysAccruedInterest: number
      todaysPostedInterest: number
    }
    DemographicCriteria: {
      booleanValue?: boolean
      code: string
      codeKey?: string
      codeValue?: string
      criteriaCode: string
      demographicCriteriaId: components['schemas']['DemographicCriteriaId']
      /** Format: date */
      effectiveDate: string
      /** @enum {string} */
      keyType: 'USERFIELD' | 'ACCOUNTFIELD'
      priceListCode: string
      stringKey?: string
      stringValue?: string
      valueCode: string
    }
    DemographicCriteriaId: {
      codeKey?: string
      criteriaCode: string
      /** @enum {string} */
      keyType: 'USERFIELD' | 'ACCOUNTFIELD'
      priceListCode: string
      stringKey?: string
      valueCode: string
    }
    DemographicPriceList: {
      code: string
      currency: string
      /** Format: date */
      effectiveDate: string
      isLeadPriceList: boolean
      name?: string
    }
    DemographicPriceListRanking: {
      /** Format: date */
      effectiveDate: string
      ranking: string
    }
    DemographicPriceListWithCount: {
      code: string
      currency: string
      /** Format: date */
      effectiveDate: string
      isLeadPriceList: boolean
      name?: string
      /** Format: int32 */
      serviceCount: number
    }
    DemographicUpdateRecordEntity: {
      aasAccountType?: string
      accountNumber?: string
      accountStatus?: string
      alternatePrimaryOfficer?: string
      alternateSecondaryOfficer?: string
      alternateTreasuryOfficer?: string
      applicationId?: string
      bankNumber?: string
      blendOverrideIndicator?: string
      branchNumber?: string
      /** Format: date */
      closeDate?: string
      control?: string
      costCenter?: string
      cpsAasIndicator?: string
      /** Format: date-time */
      createdAt?: string
      currencyCode?: string
      customerPricingIndicator?: string
      dailyOffsetIndicator?: string
      depositAccountType?: string
      depositCategory?: string
      /** Format: date */
      effectiveDate?: string
      /** @enum {string} */
      fileType?:
        | 'CORE_UNPACKED'
        | 'IBS_CORE_PACKED'
        | 'ARPPA'
        | 'ACH_TRACKER'
        | 'CASH_MANAGER'
        | 'D1B'
        | 'DIRECT_LINK_MERCHANT'
        | 'EWIRE'
      /** Format: uuid */
      id?: string
      /** @enum {string} */
      inboundRecordStatus?:
        | 'PARSED'
        | 'CONVERSION_ERROR'
        | 'VALIDATION_ERROR'
        | 'PERSIST_ERROR'
        | 'PROCESSED_CREATE'
        | 'PROCESSED_UPDATE'
        | 'PROCESSED_SKIP'
      /** Format: date */
      openDate?: string
      /** Format: date */
      postingDate?: string
      primaryOfficer?: string
      processingIndicator?: string
      secondaryOfficer?: string
      settleAtAccountIndicator?: string
      shortName?: string
      source?: string
      sourceFilePath?: string
      treasuryOfficer?: string
      /** Format: date-time */
      updatedAt?: string
    }
    EarningsCreditDefinition: {
      balanceTiers: components['schemas']['BalanceTier'][]
      /** @enum {string} */
      baseBalanceType:
        | 'AVERAGE_COLLECTED'
        | 'AVERAGE_LEDGER'
        | 'AVERAGE_NEGATIVE_COLLECTED'
        | 'AVERAGE_NEGATIVE_LEDGER'
        | 'AVERAGE_POSITIVE_COLLECTED'
        | 'AVERAGE_POSITIVE_LEDGER'
        | 'AVERAGE_UNCOLLECTED_FUNDS'
        | 'COMPENSATING_BALANCE'
        | 'END_OF_MONTH_LEDGER'
        | 'INVESTABLE_BALANCE'
        | 'AVERAGE_FLOAT'
        | 'AVERAGE_CLEARINGHOUSE_FLOAT'
        | 'REQUIRED_BALANCE'
      code: string
      description: string
      /** Format: date */
      effectiveDate: string
      indexRateCode?: string
      maxRateInclusive?: number
      minRateInclusive?: number
      /** @enum {string} */
      rateSource: 'INDEX_RATE' | 'MANUAL'
      /** @enum {string} */
      tierMethod: 'THRESHOLD' | 'PARTITIONED'
    }
    EffectiveDateRequestBody: {
      /** Format: date */
      effectiveDate: string
    }
    GetDailyBalanceHistoryRequest: {
      bankNumber: string
      /** Format: date */
      endDateExclusive: string
      /** Format: date */
      startDateInclusive: string
    }
    /** @description List of parent-child account mappings. The parent is null for root child */
    HydratedAccountWithKeyAccountMapping: {
      child: components['schemas']['AccountWithKey']
      parent?: components['schemas']['AccountWithKey']
    }
    HydratedCategoryToCategoryMapping: {
      child: components['schemas']['ServiceCategory']
      parent?: components['schemas']['ServiceCategory']
    }
    HydratedDemographicPriceList: {
      demographicCriteria: components['schemas']['DemographicCriteria'][]
      demographicPriceList: components['schemas']['DemographicPriceList']
      ranking: components['schemas']['DemographicPriceListRanking']
      servicePricing: components['schemas']['ServicePrice'][]
    }
    HydratedServiceCatalogMapping: {
      categoryToCategoryMappings: components['schemas']['HydratedCategoryToCategoryMapping'][]
      serviceToCategoryMappings: components['schemas']['HydratedServiceToCategoryMapping'][]
    }
    HydratedServicePrice: {
      cycleDefinition?: components['schemas']['CycleDefinition']
      indexRate?: components['schemas']['IndexRate']
      servicePrice?: components['schemas']['ServicePrice']
    }
    HydratedServiceToCategoryMapping: {
      child: components['schemas']['Service']
      parent?: components['schemas']['ServiceCategory']
    }
    HydratedStatementPackage: {
      address: components['schemas']['Address']
      selectedAccounts: components['schemas']['AccountCode'][]
      statementPackage: components['schemas']['StatementPackage']
    }
    HydratedUserFieldConfiguration: {
      availableForPriceList: boolean
      /** Format: int64 */
      code: number
      /** Format: date */
      createdDate?: string
      /** @enum {string} */
      fieldType: 'FREEFORM' | 'DROPDOWN' | 'BOOLEAN'
      /** Format: date */
      lastUpdatedDate?: string
      name: string
      newDropdownOptions?: components['schemas']['UserFieldDropdownOptionCreate'][]
      updatedDropdownOptions?: components['schemas']['UserFieldDropdownOptionUpdate'][]
    }
    HydratedUserFieldConfigurationCreate: {
      availableForPriceList: boolean
      /** @enum {string} */
      fieldType: 'FREEFORM' | 'DROPDOWN' | 'BOOLEAN'
      name: string
      newDropdownOptions?: components['schemas']['UserFieldDropdownOptionCreate'][]
    }
    HydratedUserFieldConfigurationUpdate: {
      availableForPriceList: boolean
      /** Format: int64 */
      code: number
      /** @enum {string} */
      fieldType: 'FREEFORM' | 'DROPDOWN' | 'BOOLEAN'
      name: string
      newDropdownOptions?: components['schemas']['UserFieldDropdownOptionCreate'][]
      updatedDropdownOptions?: components['schemas']['UserFieldDropdownOptionUpdate'][]
    }
    IndexRate: {
      code: string
      /** Format: date */
      effectiveDate: string
      indexRate: number
      name: string
    }
    InvestableBalanceDefinition: {
      addOtherBalanceLabel?: string
      /** @enum {string} */
      baseBalanceType:
        | 'AVERAGE_COLLECTED'
        | 'AVERAGE_LEDGER'
        | 'AVERAGE_NEGATIVE_COLLECTED'
        | 'AVERAGE_NEGATIVE_LEDGER'
        | 'AVERAGE_POSITIVE_COLLECTED'
        | 'AVERAGE_POSITIVE_LEDGER'
        | 'AVERAGE_UNCOLLECTED_FUNDS'
        | 'COMPENSATING_BALANCE'
        | 'END_OF_MONTH_LEDGER'
        | 'INVESTABLE_BALANCE'
        | 'AVERAGE_FLOAT'
        | 'AVERAGE_CLEARINGHOUSE_FLOAT'
        | 'REQUIRED_BALANCE'
      code: string
      /** Format: date */
      effectiveDate: string
      name: string
      subtractCompensatingBalance: boolean
      subtractFederalReserveRequirement: boolean
      subtractInterestPaidMonthToDate: boolean
      subtractOtherBalanceLabel?: string
    }
    KeyAccountMapping: {
      accountCode: components['schemas']['AccountCode']
      childAccountCode: components['schemas']['AccountCode']
    }
    NextAvailableAccountNumberResponse: {
      nextAvailableAccountNumber: string
    }
    Officer: {
      bankNumber: string
      code: string
      /** Format: date */
      effectiveDate: string
      name: string
      phone: string
    }
    PromotionalPriceList: {
      code: string
      currency: string
      /** Format: date */
      effectiveDate: string
      /** Format: date */
      expirationDate: string
      name?: string
      /** Format: int32 */
      term: number
    }
    ReserveRequirementDefinition: {
      /** @enum {string} */
      baseBalanceType: 'AVERAGE_COLLECTED' | 'AVERAGE_POSITIVE_COLLECTED'
      /** @enum {string} */
      calculationMethodType: 'INDEXED' | 'PERCENTAGE'
      ceiling?: number
      code: string
      /** Format: date */
      effectiveDate: string
      floor?: number
      indexAdjustment?: number
      indexRateCode?: string
      name: string
      reserveRate?: number
    }
    Service: {
      code: string
      description?: string
      domesticAfpCode?: string
      /** Format: date */
      effectiveDate: string
      globalAfpCode?: string
      internalNote?: string
      /** @enum {string} */
      serviceType:
        | 'VOLUME_BASED'
        | 'RECURRING'
        | 'BALANCE_BASED'
        | 'PRE_PRICED'
        | 'SERVICE_SET'
    }
    ServiceCategory: {
      code: string
      /** Format: date */
      effectiveDate: string
      name: string
    }
    ServiceDetails: {
      service: components['schemas']['Service']
      serviceCategory?: components['schemas']['ServiceCategory']
      servicePrice: components['schemas']['HydratedServicePrice'][]
      servicesInServiceSet?: components['schemas']['Service'][]
    }
    ServicePrice: {
      /** @enum {string} */
      applyServiceTo?: 'SELECT_DEPOSIT_ACCOUNTS' | 'ALL_DEPOSIT_ACCOUNTS'
      /** Format: int32 */
      balanceDivisor?: number
      /** @enum {string} */
      balanceType?:
        | 'AVERAGE_COLLECTED'
        | 'AVERAGE_LEDGER'
        | 'AVERAGE_NEGATIVE_COLLECTED'
        | 'AVERAGE_NEGATIVE_LEDGER'
        | 'AVERAGE_POSITIVE_COLLECTED'
        | 'AVERAGE_POSITIVE_LEDGER'
        | 'AVERAGE_UNCOLLECTED_FUNDS'
        | 'COMPENSATING_BALANCE'
        | 'END_OF_MONTH_LEDGER'
        | 'INVESTABLE_BALANCE'
        | 'AVERAGE_FLOAT'
        | 'AVERAGE_CLEARINGHOUSE_FLOAT'
        | 'REQUIRED_BALANCE'
      baseFee?: number
      /** @enum {string} */
      basisDays?: '360' | '365' | '366'
      /** @enum {string} */
      costType?: 'NO_COST' | 'UNIT_COST' | 'FLAT_COST'
      costValue?: number
      currency?: string
      cycleDefinitionCode?: string
      /** @enum {string} */
      disposition?: 'ANALYSED' | 'HARD_CHARGE' | 'IMMEDIATE_CHARGE' | 'WAIVED'
      /** Format: date */
      effectiveDate: string
      /** Format: date */
      expirationDate?: string
      includeReferenceInformationOnStatements?: boolean
      indexAdjustment?: number
      indexMultiplier?: number
      indexRateCode?: string
      /** Format: date */
      lastFinalsDate?: string
      maximumFee?: number
      minimumFee?: number
      /** @enum {string} */
      priceType?:
        | 'NOT_PRICED'
        | 'UNIT_PRICED'
        | 'FLAT_FEE'
        | 'THRESHOLD_TIER'
        | 'PARTITIONED_TIER'
        | 'PERCENTAGE'
        | 'OUTSIDE_PRICE'
        | 'INDEXED'
      priceValue?: number
      pricingHierarchyEntryCode: string
      /** @enum {string} */
      pricingHierarchyEntryType: 'STANDARD' | 'PRICE_LIST' | 'OVERRIDE'
      serviceCode: string
      subjectToDiscountOrPremium?: boolean
      tierMaxBalanceExclusive?: number
      /** Format: int32 */
      tierMaxVolumeExclusive?: number
      tierMinBalanceInclusive?: number
      /** Format: int32 */
      tierMinVolumeInclusive?: number
      /** Format: int32 */
      tierNumber: number
      /** @enum {string} */
      tierPriceType?: 'UNIT_PRICED' | 'FLAT_FEE' | 'PERCENTAGE' | 'INDEXED'
      /** Format: int32 */
      units?: number
    }
    ServicePriceRequest: {
      /** Format: date */
      asOfDate: string
      pricingHierarchyEntryCode?: string
      /** @enum {string} */
      pricingHierarchyEntryType?: 'STANDARD' | 'PRICE_LIST' | 'OVERRIDE'
      serviceCodes?: string[]
    }
    SettlementProcessingOptionsResponseObj: {
      accountCode: components['schemas']['AccountCode']
      accountTypeOverride?: components['schemas']['AccountTypeOverride']
      chargeAccountCode?: string
      /** Format: date */
      effectiveDate: string
      overrideAsSettlementAccount?: boolean
    }
    StatementFormatPlan: {
      afpServiceCode?: boolean
      balanceSummary?: boolean
      balanceSummaryLabel?: string
      balanceSummaryLocation?: string
      /** @enum {string} */
      balanceSummarySize?: 'FULL' | 'HALF'
      boldServiceCategoryLabel?: boolean
      boldServiceCategorySubtotalLabel?: boolean
      code: string
      /** @enum {string} */
      dailyBalanceSummary: 'NO' | 'YES_ALL_ACCOUNTS' | 'YES_COMPOSITE_ONLY'
      dailyBalanceSummaryLabel?: string
      dailyBalanceSummaryLocation?: string
      description: string
      /** @enum {string} */
      earningsCreditRate: 'EARNINGS_CREDIT_DEFINITION' | 'CALCULATED_RATE'
      /** Format: date */
      effectiveDate: string
      enableServiceCategory?: boolean
      headerLogoFileName: string
      historicalSummaryLabel?: string
      historicalSummaryLocation?: string
      /** @enum {string} */
      historicalSummaryType: 'ROLLING_12_MONTHS' | 'YTD' | 'NO'
      includeOfficerName?: boolean
      includeOfficerPhone?: boolean
      /** @enum {string} */
      printOfficer:
        | 'PRIMARY_OFFICER'
        | 'SECONDARY_OFFICER'
        | 'TREASURY_OFFICER'
        | 'NO_PRINT'
      relationshipSummary?: boolean
      relationshipSummaryLabel?: string
      relationshipSummaryLocation?: string
      requiredBalance?: boolean
      requiredBalanceMultiplier?: boolean
      resultsSummaryLabel: string
      resultsSummaryLocation: string
      resultsSummarySize: string
      returnAddress?: string
      sectionBackgroundColor: string
      sectionBorderColor: string
      sectionTextColor: string
      serviceCategoryBackgroundColor?: string
      /** @enum {string} */
      serviceCategoryLevel?: 'ONE' | 'TWO' | 'THREE'
      serviceCategoryPieChart?: boolean
      serviceCategoryPieChartLabel?: string
      serviceCategoryPieChartLocation?: string
      /** @enum {string} */
      serviceCategorySort?:
        | 'USER_DEFINED'
        | 'ALPHABETICAL_BY_CATEGORY_NAME'
        | 'BY_LOWEST_TO_HIGHEST_SERVICE_CODE'
      serviceCategorySubtotal?: boolean
      serviceCategorySubtotalBackgroundColor?: string
      serviceChargesDueBarChart?: boolean
      serviceChargesDueBarChartLabel?: string
      serviceChargesDueBarChartLocation?: string
      serviceCode?: boolean
      serviceDetailLabel: string
      serviceDetailLocation?: string
      /** @enum {string} */
      sortServicesType:
        | 'USER_DEFINED'
        | 'ALPHABETICAL_BY_CATEGORY_NAME'
        | 'BY_LOWEST_TO_HIGHEST_SERVICE_CODE'
      statementImageLocations: string[]
      statementMessage?: boolean
      statementMessageImages: string[]
      statementMessageLocation?: string
    }
    StatementMessage: {
      code: string
      /** Format: date */
      effectiveDate: string
      message: string
      name: string
    }
    StatementPackage: {
      accountCode: components['schemas']['AccountCode']
      addressCode: components['schemas']['AddressCode']
      /** Format: date */
      effectiveDate: string
      /** @enum {string} */
      packageDelivery:
        | 'ELECTRONIC'
        | 'ELECTRONIC_AND_PRINT'
        | 'PRINT'
        | 'NO_STATEMENT'
      /** @enum {string} */
      packageType:
        | 'ALL_ACCOUNTS'
        | 'COMPOSITE_ACCOUNTS'
        | 'DEPOSIT_ACCOUNTS'
        | 'SELECTED_ACCOUNTS'
      /** Format: int32 */
      statementPackageNumber: number
    }
    StatementPackageCreateUpdateRequest: {
      /** @enum {string} */
      accountApplicationId: 'C' | 'D'
      accountNumber: string
      /** @enum {string} */
      addressAccountApplicationId: 'C' | 'D'
      addressAccountNumber: string
      /** @enum {string} */
      addressApplicationId: 'A' | 'D'
      /** Format: int32 */
      addressNumber: number
      bankNumber: string
      /** Format: date */
      effectiveDate: string
      /** @enum {string} */
      packageDelivery:
        | 'ELECTRONIC'
        | 'ELECTRONIC_AND_PRINT'
        | 'PRINT'
        | 'NO_STATEMENT'
      /** @enum {string} */
      packageType:
        | 'ALL_ACCOUNTS'
        | 'COMPOSITE_ACCOUNTS'
        | 'DEPOSIT_ACCOUNTS'
        | 'SELECTED_ACCOUNTS'
      selectedAccounts: components['schemas']['AccountCode'][]
      /** Format: int32 */
      statementPackageNumber: number
    }
    UserFieldDropdownOptionCreate: {
      value: string
    }
    UserFieldDropdownOptionUpdate: {
      /** Format: int64 */
      code: number
      value: string
    }
    UserFieldSelection: {
      accountNumber: string
      /** @enum {string} */
      applicationId: 'C' | 'D'
      applyToChildAccounts: boolean
      bankNumber: string
      booleanValue?: boolean
      /** Format: int64 */
      dropdownOptionCode?: number
      /** Format: date */
      effectiveDate: string
      /** Format: date */
      expiry?: string
      freeformValue?: string
      isUnset: boolean
      /** Format: int64 */
      userFieldCode: number
    }
    VersionableEntityId: {
      code: string
      /** Format: date */
      effectiveDate: string
    }
  }
  responses: never
  parameters: never
  requestBodies: never
  headers: never
  pathItems: never
}
export type $defs = Record<string, never>
export interface operations {
  root: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  accountBankNumberSanityCheck: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': components['schemas']['AccountEntity'][]
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  addAccountMapping: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['AccountMappingCreateRequest']
      }
    }
    responses: {
      /** @description Created the account mapping */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['HydratedAccountWithKeyAccountMapping'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  addAnalysisResultOption: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['AnalysisResultOption']
      }
    }
    responses: {
      /** @description New Analysis Result Option added */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['AnalysisResultOption']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  addBankOptions: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['BankOptions']
      }
    }
    responses: {
      /** @description New Bank options added */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['BankOptions']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  addIndexRate: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['IndexRate']
      }
    }
    responses: {
      /** @description New Index Rate added */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['IndexRate']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  addService: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['CreateOrUpdateService']
      }
    }
    responses: {
      /** @description New Service added */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['Service']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  addServiceCategory: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['CreateServiceCategory']
      }
    }
    responses: {
      /** @description New Service Category added */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['ServiceCategory']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  addStatementMessage: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['StatementMessage']
      }
    }
    responses: {
      /** @description New Statement Message added */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['StatementMessage']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  createUserFieldConfiguration: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['HydratedUserFieldConfigurationCreate']
      }
    }
    responses: {
      /** @description User field configuration added */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['HydratedUserFieldConfiguration']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  analyze: {
    parameters: {
      query: {
        bankIdentifier: string
        startDateInclusive: string
        endDateExclusive: string
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  auth: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  changeLogLevel: {
    parameters: {
      query: {
        level: 'TRACE' | 'DEBUG' | 'INFO' | 'WARN' | 'ERROR'
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  checkAddressCodeAvailability: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['AddressCodeAvailabilityRequest']
      }
    }
    responses: {
      /** @description Address code availability checked */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['AddressCodeAvailabilityResponse']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  createAccountType: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['AccountType']
      }
    }
    responses: {
      /** @description Created */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['AccountType']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  createCompositeAccountTypeOverride: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['AccountTypeOverride']
      }
    }
    responses: {
      /** @description Created the override for the account */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['AccountTypeOverride']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  createAddress: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['Address']
      }
    }
    responses: {
      /** @description Address created */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['Address']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  createBranch: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['Branch']
      }
    }
    responses: {
      /** @description Created */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['Branch']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  createCompositeAccount: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['CompositeAccountCreateRequest']
      }
    }
    responses: {
      /** @description Created the composite account */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['AccountWithKey']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  createCycleDefinition: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['CycleDefinition']
      }
    }
    responses: {
      /** @description New Cycle Definition added */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['CycleDefinition']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  createDemographicPriceList: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['HydratedDemographicPriceList']
      }
    }
    responses: {
      /** @description Successfully created demographic price list */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['DemographicPriceList']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  createEarningsCreditDefinition: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['EarningsCreditDefinition']
      }
    }
    responses: {
      /** @description Earnings Credit Definition created */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['EarningsCreditDefinition']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  createInvestableBalanceDefinition: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['InvestableBalanceDefinition']
      }
    }
    responses: {
      /** @description Investable Balance Definition created */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['InvestableBalanceDefinition']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  createOfficer: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['Officer']
      }
    }
    responses: {
      /** @description Created */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['Officer']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  createRequiredBalanceDefinition: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['BalanceRequirementDefinition']
      }
    }
    responses: {
      /** @description Required Balance Definition created */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['BalanceRequirementDefinition']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  createReserveRequirementDefinition: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['ReserveRequirementDefinition']
      }
    }
    responses: {
      /** @description Reserve requirement was successfully created */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['ReserveRequirementDefinition']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  createServiceOverride: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['ServicePrice']
      }
    }
    responses: {
      /** @description Service override created */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': unknown
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  createStatementFormatPlan: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['StatementFormatPlan']
      }
    }
    responses: {
      /** @description New Statement Format Plan created */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['StatementFormatPlan']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  createUserFieldSelections: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['UserFieldSelection'][]
      }
    }
    responses: {
      /** @description Created the user field selections */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['UserFieldSelection'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  deleteServiceOverride: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['ServicePrice']
      }
    }
    responses: {
      /** @description Service override deleted */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': unknown
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  editServiceOverride: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['ServicePrice']
      }
    }
    responses: {
      /** @description Service override updated/edited */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': unknown
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getAccountWithKey: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['AccountEffectiveDateRequest']
      }
    }
    responses: {
      /** @description Account was successfully returned */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['AccountWithKey']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getMappingsOfKeyAccountAsOf: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['VersionableEntityId']
      }
    }
    responses: {
      /** @description Found the mappings */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['HydratedAccountWithKeyAccountMapping'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getAccountTimeline: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['AccountCode']
      }
    }
    responses: {
      /** @description Account timelines retrieved */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': string[]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getAccountType: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['VersionableEntityId']
      }
    }
    responses: {
      /** @description Found the account type */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['AccountType']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getAccountTypeOverride: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['AccountEffectiveDateRequest']
      }
    }
    responses: {
      /** @description Found overrides for the account */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['AccountTypeOverride']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getAccountType_1: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['CodeRequestBody']
      }
    }
    responses: {
      /** @description Got timeline values for the account type */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['AccountType'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getAccountTypes: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['EffectiveDateRequestBody']
      }
    }
    responses: {
      /** @description Found the account types */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['AccountType'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getAccounts: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['EffectiveDateRequestBody']
      }
    }
    responses: {
      /** @description Accounts were successfully returned */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['Account'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getAccountsByCodes: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['AccountCodesRetrieveRequest']
      }
    }
    responses: {
      /** @description Accounts were successfully returned */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['Account'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getAddress: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['AddressRetrieveRequest']
      }
    }
    responses: {
      /** @description Address found */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['Address']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getAddresses: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['AccountEffectiveDateRequest']
      }
    }
    responses: {
      /** @description Addresses found */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['Address'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getAnalysisResultOptionsByCode: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['CodeRequestBody']
      }
    }
    responses: {
      /** @description Analysis Result Options were successfully returned */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['AnalysisResultOption'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getBankOptionsByCode: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['CodeRequestBody']
      }
    }
    responses: {
      /** @description All Bank Options retrieved for the given code */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['BankOptions'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getBranch: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['VersionableEntityId']
      }
    }
    responses: {
      /** @description Get branch by effective date and code */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['Branch']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  retrieveBranchTimeline: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['CodeRequestBody']
      }
    }
    responses: {
      /** @description Got branch timeline */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['Branch'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getBranches: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['EffectiveDateRequestBody']
      }
    }
    responses: {
      /** @description Got branches by effective date */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['Branch'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getCycleDefinition: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['VersionableEntityId']
      }
    }
    responses: {
      /** @description Cycle Definition successfully returned */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['CycleDefinition']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getCycleDefinitionByCode: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['CodeRequestBody']
      }
    }
    responses: {
      /** @description Cycle definitions list were successfully returned */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['CycleDefinition'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getCycleDefinitions: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['EffectiveDateRequestBody']
      }
    }
    responses: {
      /** @description List of Cycle Definition successfully returned */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['CycleDefinition'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getDemographicCriteria: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['VersionableEntityId']
      }
    }
    responses: {
      /** @description Found the demographic criteria */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['DemographicCriteria'][]
        }
      }
      /** @description Demographic criteria not found */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getDemographicPriceList: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['VersionableEntityId']
      }
    }
    responses: {
      /** @description Found the price list */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['DemographicPriceList']
        }
      }
      /** @description Price list not found */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getDemographicPriceListRanking: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['EffectiveDateRequestBody']
      }
    }
    responses: {
      /** @description Found the price list ranking */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['DemographicPriceListRanking']
        }
      }
      /** @description Price list ranking not found */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getDemographicPriceListVersions: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['CodeRequestBody']
      }
    }
    responses: {
      /** @description Found the price list */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': string[]
        }
      }
      /** @description Price list not found */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getDemographicPriceLists: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['CodesAndEffectiveDateRequestBody']
      }
    }
    responses: {
      /** @description Found the price lists */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['DemographicPriceList'][]
        }
      }
      /** @description Price list not found */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getDepositAccounts: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['EffectiveDateRequestBody']
      }
    }
    responses: {
      /** @description Accounts were successfully returned */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': unknown
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getEarningsCreditDefinition: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['VersionableEntityId']
      }
    }
    responses: {
      /** @description Earnings Credit Definition retrieved */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['EarningsCreditDefinition']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getEarningsCreditDefinitions: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['EffectiveDateRequestBody']
      }
    }
    responses: {
      /** @description Earnings Credit Definition retrieved */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['EarningsCreditDefinition'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getEarningsCreditDefinitionsByCode: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['CodeRequestBody']
      }
    }
    responses: {
      /** @description Earnings Credit Definition retrieved */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['EarningsCreditDefinition'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getEarningsCreditForAccount: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['AccountCode']
      }
    }
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': components['schemas']['EarningsCreditDefinition']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getIndexRatesByCode: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['CodeRequestBody']
      }
    }
    responses: {
      /** @description Index rates were successfully returned */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['IndexRate'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getInvestableBalanceDefinition: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['VersionableEntityId']
      }
    }
    responses: {
      /** @description Investable Balance Definition successfully returned */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['InvestableBalanceDefinition']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getInvestableBalanceDefinitionByCode: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['CodeRequestBody']
      }
    }
    responses: {
      /** @description Investable Balance Definition successfully returned */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['InvestableBalanceDefinition'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getInvestableBalanceDefinitions: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['EffectiveDateRequestBody']
      }
    }
    responses: {
      /** @description List of Investable Balance Definitions successfully returned */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['InvestableBalanceDefinition'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getLeadCompositeAccountCode: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['VersionableEntityId']
      }
    }
    responses: {
      /** @description Returning lead comp account code */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['AccountCode']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getNextAvailableAccountNumber: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['EffectiveDateRequestBody']
      }
    }
    responses: {
      /** @description Next available account number successfully returned */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['NextAvailableAccountNumberResponse']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  retrieveOfficer: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['VersionableEntityId']
      }
    }
    responses: {
      /** @description Get officer by effective date and code */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['Officer']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  retrieveOfficerTimeline: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['CodeRequestBody']
      }
    }
    responses: {
      /** @description Got officer timeline */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['Officer'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getOfficers: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['EffectiveDateRequestBody']
      }
    }
    responses: {
      /** @description Got officers by effective date */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['Officer'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getParentlessOpenAccounts: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['EffectiveDateRequestBody']
      }
    }
    responses: {
      /** @description Found the accounts */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['Account'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getPromotionalPriceList: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['VersionableEntityId']
      }
    }
    responses: {
      /** @description Found the price list */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['PromotionalPriceList']
        }
      }
      /** @description Price list not found */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getRequiredBalanceDefinition: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['VersionableEntityId']
      }
    }
    responses: {
      /** @description Required Balance Definition successfully returned */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['BalanceRequirementDefinition']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getRequiredBalanceDefinitionByCode: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['CodeRequestBody']
      }
    }
    responses: {
      /** @description Required Balance Definition successfully returned */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['BalanceRequirementDefinition'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getRequiredBalanceDefinitions: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['EffectiveDateRequestBody']
      }
    }
    responses: {
      /** @description List of Required Balance Definitions successfully returned */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['BalanceRequirementDefinition'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getReserveRequirementDefinitions: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['EffectiveDateRequestBody']
      }
    }
    responses: {
      /** @description List of reserve requirements successfully returned */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['ReserveRequirementDefinition'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getReserveRequirementsByCode: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['CodeRequestBody']
      }
    }
    responses: {
      /** @description List of reserve requirements successfully returned */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['ReserveRequirementDefinition'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getServiceCatalogMapping: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['EffectiveDateRequestBody']
      }
    }
    responses: {
      /** @description Service catalog mapping retrieved */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['HydratedServiceCatalogMapping']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getServiceDetailsAsOf: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['VersionableEntityId']
      }
    }
    responses: {
      /** @description Service details retrieved */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['ServiceDetails']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getServiceOverrideDetailsForAccount: {
    parameters: {
      query?: {
        filterLevel?: string
      }
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['VersionableEntityId']
      }
    }
    responses: {
      /** @description Service override details retrieved */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['ServiceDetails'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getServicePricesAsOf: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['ServicePriceRequest']
      }
    }
    responses: {
      /** @description Service Prices retrieved */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['ServicePrice'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getServiceTimeline: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['CodeRequestBody']
      }
    }
    responses: {
      /** @description Service timelines retrieved */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': string[]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getSettlementProcessingOptions: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['AccountCodeWithEffectivedateReq']
      }
    }
    responses: {
      /** @description get account and related override details */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['SettlementProcessingOptionsResponseObj']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getStatementFormatPlans: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['EffectiveDateRequestBody']
      }
    }
    responses: {
      /** @description List of Statement Format Plans retrieved by effective date. */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['StatementFormatPlan'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getStatementFormatPlanByCode: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['CodeRequestBody']
      }
    }
    responses: {
      /** @description Statement Format Plan is retrieved by code. */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['StatementFormatPlan'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getStatementMessageByCode: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['CodeRequestBody']
      }
    }
    responses: {
      /** @description List of Statement Message versions successfully returned */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['StatementMessage'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getUserFieldSelections: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['AccountEffectiveDateRequest']
      }
    }
    responses: {
      /** @description Found the user field selections */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['UserFieldSelection'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getUserFieldConfigurations: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Found the user field configurations */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['HydratedUserFieldConfiguration'][]
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  grabAccountMappings: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': components['schemas']['AccountMappingEntity'][]
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  grabAllAccounts: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': components['schemas']['AccountEntity'][]
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  healthCheck: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  listAllKeyAccountMappings: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description All key account mappings returned */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['KeyAccountMapping'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  listAnalysisResultOptionsByEffectiveDate: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['EffectiveDateRequestBody']
      }
    }
    responses: {
      /** @description List of Analysis Result Option successfully returned */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['AnalysisResultOption'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  getBankOptions: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['EffectiveDateRequestBody']
      }
    }
    responses: {
      /** @description New Bank options added */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['BankOptions'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  listDemographicPriceLists: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['EffectiveDateRequestBody']
      }
    }
    responses: {
      /** @description Found the price lists */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['DemographicPriceListWithCount'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  listIndexRatesByEffectiveDate: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['EffectiveDateRequestBody']
      }
    }
    responses: {
      /** @description List of index rates successfully returned */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['IndexRate'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  listMetrics: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string[]
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  listPromotionalPriceLists: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['EffectiveDateRequestBody']
      }
    }
    responses: {
      /** @description Found the price lists */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['PromotionalPriceList'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  listStatementMessageByEffectiveDate: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['EffectiveDateRequestBody']
      }
    }
    responses: {
      /** @description List of Statement Messages successfully returned */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['StatementMessage'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  listStatementPackages: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['AccountEffectiveDateRequest']
      }
    }
    responses: {
      /** @description List statement packages */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['HydratedStatementPackage'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  populateAccountMappings: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': components['schemas']['DemographicPriceListRanking']
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  populateBankNumber: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  removeMappingsOfAccountAsOf: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['AccountCodesDateLeadAccountRequest']
      }
    }
    responses: {
      /** @description Remove mappings */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['HydratedAccountWithKeyAccountMapping'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  removeKeyAccountFromParent: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['AccountCode']
      }
    }
    responses: {
      /** @description The key account code */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': unknown
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  removeKeyAccountMapping: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['AccountCode']
      }
    }
    responses: {
      /** @description Removed key account mapping */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['AccountCode'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  setDemographicPriceListRanking: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['DemographicPriceListRanking']
      }
    }
    responses: {
      /** @description Successfully saved the price list ranking */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['DemographicPriceListRanking']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  setKeyAccount: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['AccountMappingCreateRequest']
      }
    }
    responses: {
      /** @description The key account code */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': unknown
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  tagServiceOverride: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': {
          requestBody?: components['schemas']['VersionableEntityId']
          serviceOverride?: components['schemas']['ServicePrice']
        }
      }
    }
    responses: {
      /** @description Service override Added */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': unknown
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  testGet: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  testGetDailyBalanceHistories: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['GetDailyBalanceHistoryRequest']
      }
    }
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': components['schemas']['DailyBalanceHistory'][]
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  testGetDemoUpdateRecords: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['EffectiveDateRequestBody']
      }
    }
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': components['schemas']['DemographicUpdateRecordEntity'][]
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  testPost: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  updateAccount: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['Account']
      }
    }
    responses: {
      /** @description Account timelines retrieved */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['Account']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  updateAccountMappings: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['AccountMappingUpdateRequest']
      }
    }
    responses: {
      /** @description Updated the account mappings */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['HydratedAccountWithKeyAccountMapping'][]
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  updateAccountType: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['AccountType']
      }
    }
    responses: {
      /** @description Updated the account type */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['AccountType']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  updateAddress: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['Address']
      }
    }
    responses: {
      /** @description Address updated */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['Address']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  updateAnalysisResultOption: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['AnalysisResultOption']
      }
    }
    responses: {
      /** @description Analysis Result Option updated */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['AnalysisResultOption']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  updateBankOptions: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['BankOptions']
      }
    }
    responses: {
      /** @description Bank Options updated for given code */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['BankOptions']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  updateCycleDefinition: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['CycleDefinition']
      }
    }
    responses: {
      /** @description Cycle Definition updated */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['CycleDefinition']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  updateDemographicPriceList: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['HydratedDemographicPriceList']
      }
    }
    responses: {
      /** @description Successfully updated demographic price list */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['DemographicPriceList']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  updateEarningsCreditDefinition: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['EarningsCreditDefinition']
      }
    }
    responses: {
      /** @description Earnings Credit Definition updated */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['EarningsCreditDefinition']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  updateIndexRate: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['IndexRate']
      }
    }
    responses: {
      /** @description Index Rate updated */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['IndexRate']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  updateInvestableBalanceDefinition: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['InvestableBalanceDefinition']
      }
    }
    responses: {
      /** @description Investable Balance Definition successfully returned */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['InvestableBalanceDefinition']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  updateRequiredBalanceDefinition: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['BalanceRequirementDefinition']
      }
    }
    responses: {
      /** @description Required Balance Definition successfully returned */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['BalanceRequirementDefinition']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  updateReserveRequirementDefinition: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['ReserveRequirementDefinition']
      }
    }
    responses: {
      /** @description Reserve requirement was successfully updated */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['ReserveRequirementDefinition']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  updateService: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['CreateOrUpdateService']
      }
    }
    responses: {
      /** @description Service updated */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['Service']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  updateServiceCategory: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['ServiceCategory']
      }
    }
    responses: {
      /** @description Service Category updated */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['ServiceCategory']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  updateSettlementProcessingOptions: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['AccountTypeOverride']
      }
    }
    responses: {
      /** @description Updated account and account type override */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': unknown
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  updateStatementFormatPlan: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['StatementFormatPlan']
      }
    }
    responses: {
      /** @description Update a Statement Format Plan. */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['StatementFormatPlan']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  updateStatementMessage: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['StatementMessage']
      }
    }
    responses: {
      /** @description Statement Message updated */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['StatementMessage']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  updateStatementPackage: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['StatementPackageCreateUpdateRequest']
      }
    }
    responses: {
      /** @description Address updated */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['Address']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  updateUserFieldConfiguration: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['HydratedUserFieldConfigurationUpdate']
      }
    }
    responses: {
      /** @description User field configuration updated */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['HydratedUserFieldConfiguration']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
  upsertKeyAccountMapping: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody: {
      content: {
        'application/json': components['schemas']['KeyAccountMapping']
      }
    }
    responses: {
      /** @description Upserted key account mapping */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['KeyAccountMapping']
        }
      }
      /** @description Invalid input */
      400: {
        headers: {
          [name: string]: unknown
        }
        content?: never
      }
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown
        }
        content: {
          '*/*': string
        }
      }
    }
  }
}
