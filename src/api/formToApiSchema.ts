// This file is generated via the `npm run schemagen` command.
//
// That command runs the `./generateZodSchemas.ts` script.
// Edit that file and not this one.

import { z } from 'zod'

const nullToUndefined = <T>(v: T) => (v === null ? undefined : v)

const accountValidator = z.object({
  accountNumber: z.string(),
  accountStatus: z.enum(['Z', 'B', 'C', 'P', 'X']).nullable(),
  analysisAccountTypeCode: z.string(),
  applicationId: z.enum(['C', 'D']),
  bankNumber: z.string(),
  branchCode: z.string(),
  closeDate: z.string().date().nullable(),
  costCenter: z.string(),
  currencyCode: z.string(),
  customerSpecificPricingIndicator: z.boolean().nullable(),
  depositAccountTypeCode: z.string().nullable(),
  depositCategory: z
    .enum(['A', 'C', 'D', 'L', 'M', 'N', 'R', 'S', 'P', 'T'])
    .nullable(),
  effectiveDate: z.string().date(),
  openDate: z.string().date(),
  primaryOfficerCode: z.string(),
  processingIndicator: z.enum(['A', 'B']).nullable(),
  secondaryOfficerCode: z.string().nullable(),
  shortName: z.string(),
  treasuryOfficerCode: z.string().nullable(),
})
const accountTransformer = z.object({
  ...accountValidator.shape,
  accountStatus:
    accountValidator.shape.accountStatus.transform(nullToUndefined),
  closeDate: accountValidator.shape.closeDate.transform(nullToUndefined),
  customerSpecificPricingIndicator:
    accountValidator.shape.customerSpecificPricingIndicator.transform(
      nullToUndefined,
    ),
  depositAccountTypeCode:
    accountValidator.shape.depositAccountTypeCode.transform(nullToUndefined),
  depositCategory:
    accountValidator.shape.depositCategory.transform(nullToUndefined),
  processingIndicator:
    accountValidator.shape.processingIndicator.transform(nullToUndefined),
  secondaryOfficerCode:
    accountValidator.shape.secondaryOfficerCode.transform(nullToUndefined),
  treasuryOfficerCode:
    accountValidator.shape.treasuryOfficerCode.transform(nullToUndefined),
})
export type AccountForm = z.input<typeof accountTransformer>
export type Account = z.infer<typeof accountTransformer>
const accountDeprecated = z.object({
  accountNumber: z.string(),
  accountStatus: z.enum(['Z', 'B', 'C', 'P', 'X']).nullable(),
  analysisAccountTypeCode: z.string(),
  applicationId: z.enum(['C', 'D']),
  bankNumber: z.string(),
  branchCode: z.string(),
  closeDate: z.string().date().nullable(),
  costCenter: z.string(),
  currencyCode: z.string(),
  customerSpecificPricingIndicator: z.boolean().nullable(),
  depositAccountTypeCode: z.string().nullable(),
  depositCategory: z
    .enum(['A', 'C', 'D', 'L', 'M', 'N', 'R', 'S', 'P', 'T'])
    .nullable(),
  effectiveDate: z.string().date(),
  openDate: z.string().date(),
  primaryOfficerCode: z.string(),
  processingIndicator: z.enum(['A', 'B']).nullable(),
  secondaryOfficerCode: z.string().nullable(),
  shortName: z.string(),
  treasuryOfficerCode: z.string().nullable(),
})
export type DefaultAccountForm = Omit<AccountForm, 'applicationId'>
const defaultAccount: DefaultAccountForm = {
  accountNumber: '',
  accountStatus: null,
  analysisAccountTypeCode: '',
  bankNumber: '',
  branchCode: '',
  closeDate: '',
  costCenter: '',
  currencyCode: '',
  customerSpecificPricingIndicator: null,
  depositAccountTypeCode: '',
  depositCategory: null,
  effectiveDate: '',
  openDate: '',
  primaryOfficerCode: '',
  processingIndicator: null,
  secondaryOfficerCode: '',
  shortName: '',
  treasuryOfficerCode: '',
}

const accountCodeValidator = z.object({
  accountNumber: z.string(),
  applicationId: z.enum(['C', 'D']),
  bankNumber: z.string(),
})
const accountCodeTransformer = accountCodeValidator
export type AccountCodeForm = z.input<typeof accountCodeTransformer>
export type AccountCode = z.infer<typeof accountCodeTransformer>
const accountCodeDeprecated = z.object({
  accountNumber: z.string(),
  applicationId: z.enum(['C', 'D']),
  bankNumber: z.string(),
})
export type DefaultAccountCodeForm = Omit<AccountCodeForm, 'applicationId'>
const defaultAccountCode: DefaultAccountCodeForm = {
  accountNumber: '',
  bankNumber: '',
}

const accountCodeWithEffectivedateReqValidator = z.object({
  accountCode: accountCodeValidator,
  effectiveDate: z.string().date(),
})
const accountCodeWithEffectivedateReqTransformer = z.object({
  ...accountCodeWithEffectivedateReqValidator.shape,
  accountCode: accountCodeTransformer,
})
export type AccountCodeWithEffectivedateReqForm = z.input<
  typeof accountCodeWithEffectivedateReqTransformer
>
export type AccountCodeWithEffectivedateReq = z.infer<
  typeof accountCodeWithEffectivedateReqTransformer
>
const accountCodeWithEffectivedateReqDeprecated = z.object({
  accountCode: accountCodeDeprecated,
  effectiveDate: z.string().date(),
})
export type DefaultAccountCodeWithEffectivedateReqForm = Omit<
  AccountCodeWithEffectivedateReqForm,
  'accountCode'
> & {
  accountCode: DefaultAccountCodeForm
}

const defaultAccountCodeWithEffectivedateReq: DefaultAccountCodeWithEffectivedateReqForm =
  {
    accountCode: defaultAccountCode,
    effectiveDate: '',
  }

const accountCodesDateLeadAccountRequestValidator = z.object({
  accountCodes: z.array(accountCodeValidator),
  effectiveDate: z.string().date(),
  leadAccount: accountCodeValidator,
})
const accountCodesDateLeadAccountRequestTransformer = z.object({
  ...accountCodesDateLeadAccountRequestValidator.shape,
  accountCodes: z.array(accountCodeTransformer),
  leadAccount: accountCodeTransformer,
})
export type AccountCodesDateLeadAccountRequestForm = z.input<
  typeof accountCodesDateLeadAccountRequestTransformer
>
export type AccountCodesDateLeadAccountRequest = z.infer<
  typeof accountCodesDateLeadAccountRequestTransformer
>
const accountCodesDateLeadAccountRequestDeprecated = z.object({
  accountCodes: z.array(accountCodeDeprecated),
  effectiveDate: z.string().date(),
  leadAccount: accountCodeDeprecated,
})
export type DefaultAccountCodesDateLeadAccountRequestForm = Omit<
  AccountCodesDateLeadAccountRequestForm,
  'leadAccount'
> & {
  leadAccount: DefaultAccountCodeForm
}

const defaultAccountCodesDateLeadAccountRequest: DefaultAccountCodesDateLeadAccountRequestForm =
  {
    accountCodes: [],
    effectiveDate: '',
    leadAccount: defaultAccountCode,
  }

const accountCodesRetrieveRequestValidator = z.object({
  accountCodes: z.array(accountCodeValidator),
  effectiveDate: z.string().date(),
})
const accountCodesRetrieveRequestTransformer = z.object({
  ...accountCodesRetrieveRequestValidator.shape,
  accountCodes: z.array(accountCodeTransformer),
})
export type AccountCodesRetrieveRequestForm = z.input<
  typeof accountCodesRetrieveRequestTransformer
>
export type AccountCodesRetrieveRequest = z.infer<
  typeof accountCodesRetrieveRequestTransformer
>
const accountCodesRetrieveRequestDeprecated = z.object({
  accountCodes: z.array(accountCodeDeprecated),
  effectiveDate: z.string().date(),
})
const defaultAccountCodesRetrieveRequest: AccountCodesRetrieveRequestForm = {
  accountCodes: [],
  effectiveDate: '',
}

const accountEffectiveDateRequestValidator = z.object({
  accountNumber: z.string(),
  applicationId: z.enum(['C', 'D']),
  bankNumber: z.string(),
  effectiveDate: z.string().date(),
})
const accountEffectiveDateRequestTransformer =
  accountEffectiveDateRequestValidator
export type AccountEffectiveDateRequestForm = z.input<
  typeof accountEffectiveDateRequestTransformer
>
export type AccountEffectiveDateRequest = z.infer<
  typeof accountEffectiveDateRequestTransformer
>
const accountEffectiveDateRequestDeprecated = z.object({
  accountNumber: z.string(),
  applicationId: z.enum(['C', 'D']),
  bankNumber: z.string(),
  effectiveDate: z.string().date(),
})
export type DefaultAccountEffectiveDateRequestForm = Omit<
  AccountEffectiveDateRequestForm,
  'applicationId'
>
const defaultAccountEffectiveDateRequest: DefaultAccountEffectiveDateRequestForm =
  {
    accountNumber: '',
    bankNumber: '',
    effectiveDate: '',
  }

const accountEntityValidator = z.object({
  accountNumber: z.string(),
  accountStatus: z.enum(['Z', 'B', 'C', 'P', 'X']).nullable(),
  analysisAccountTypeCode: z.string(),
  applicationId: z.enum(['C', 'D']),
  bankNumber: z.string(),
  branchCode: z.string(),
  closeDate: z.string().date().nullable(),
  code: z.string().nullable(),
  costCenter: z.string(),
  currencyCode: z.string(),
  customerSpecificPricingIndicator: z.boolean().nullable(),
  depositAccountTypeCode: z.string().nullable(),
  depositCategory: z
    .enum(['A', 'C', 'D', 'L', 'M', 'N', 'R', 'S', 'P', 'T'])
    .nullable(),
  effectiveDate: z.string().date(),
  openDate: z.string().date(),
  primaryOfficerCode: z.string(),
  processingIndicator: z.enum(['A', 'B']).nullable(),
  secondaryOfficerCode: z.string().nullable(),
  shortName: z.string().nullable(),
  treasuryOfficerCode: z.string().nullable(),
})
const accountEntityTransformer = z.object({
  ...accountEntityValidator.shape,
  accountStatus:
    accountEntityValidator.shape.accountStatus.transform(nullToUndefined),
  closeDate: accountEntityValidator.shape.closeDate.transform(nullToUndefined),
  code: accountEntityValidator.shape.code.transform(nullToUndefined),
  customerSpecificPricingIndicator:
    accountEntityValidator.shape.customerSpecificPricingIndicator.transform(
      nullToUndefined,
    ),
  depositAccountTypeCode:
    accountEntityValidator.shape.depositAccountTypeCode.transform(
      nullToUndefined,
    ),
  depositCategory:
    accountEntityValidator.shape.depositCategory.transform(nullToUndefined),
  processingIndicator:
    accountEntityValidator.shape.processingIndicator.transform(nullToUndefined),
  secondaryOfficerCode:
    accountEntityValidator.shape.secondaryOfficerCode.transform(
      nullToUndefined,
    ),
  shortName: accountEntityValidator.shape.shortName.transform(nullToUndefined),
  treasuryOfficerCode:
    accountEntityValidator.shape.treasuryOfficerCode.transform(nullToUndefined),
})
export type AccountEntityForm = z.input<typeof accountEntityTransformer>
export type AccountEntity = z.infer<typeof accountEntityTransformer>
const accountEntityDeprecated = z.object({
  accountNumber: z.string(),
  accountStatus: z.enum(['Z', 'B', 'C', 'P', 'X']).nullable(),
  analysisAccountTypeCode: z.string(),
  applicationId: z.enum(['C', 'D']),
  bankNumber: z.string(),
  branchCode: z.string(),
  closeDate: z.string().date().nullable(),
  code: z.string().nullable(),
  costCenter: z.string(),
  currencyCode: z.string(),
  customerSpecificPricingIndicator: z.boolean().nullable(),
  depositAccountTypeCode: z.string().nullable(),
  depositCategory: z
    .enum(['A', 'C', 'D', 'L', 'M', 'N', 'R', 'S', 'P', 'T'])
    .nullable(),
  effectiveDate: z.string().date(),
  openDate: z.string().date(),
  primaryOfficerCode: z.string(),
  processingIndicator: z.enum(['A', 'B']).nullable(),
  secondaryOfficerCode: z.string().nullable(),
  shortName: z.string().nullable(),
  treasuryOfficerCode: z.string().nullable(),
})
export type DefaultAccountEntityForm = Omit<AccountEntityForm, 'applicationId'>
const defaultAccountEntity: DefaultAccountEntityForm = {
  accountNumber: '',
  accountStatus: null,
  analysisAccountTypeCode: '',
  bankNumber: '',
  branchCode: '',
  closeDate: '',
  code: '',
  costCenter: '',
  currencyCode: '',
  customerSpecificPricingIndicator: null,
  depositAccountTypeCode: '',
  depositCategory: null,
  effectiveDate: '',
  openDate: '',
  primaryOfficerCode: '',
  processingIndicator: null,
  secondaryOfficerCode: '',
  shortName: '',
  treasuryOfficerCode: '',
}

const accountMappingCreateRequestValidator = z.object({
  childAccountCode: accountCodeValidator,
  effectiveDate: z.string().date(),
  parentAccountCode: accountCodeValidator,
})
const accountMappingCreateRequestTransformer = z.object({
  ...accountMappingCreateRequestValidator.shape,
  childAccountCode: accountCodeTransformer,
  parentAccountCode: accountCodeTransformer,
})
export type AccountMappingCreateRequestForm = z.input<
  typeof accountMappingCreateRequestTransformer
>
export type AccountMappingCreateRequest = z.infer<
  typeof accountMappingCreateRequestTransformer
>
const accountMappingCreateRequestDeprecated = z.object({
  childAccountCode: accountCodeDeprecated,
  effectiveDate: z.string().date(),
  parentAccountCode: accountCodeDeprecated,
})
export type DefaultAccountMappingCreateRequestForm = Omit<
  AccountMappingCreateRequestForm,
  'childAccountCode' | 'parentAccountCode'
> & {
  childAccountCode: DefaultAccountCodeForm
  parentAccountCode: DefaultAccountCodeForm
}

const defaultAccountMappingCreateRequest: DefaultAccountMappingCreateRequestForm =
  {
    childAccountCode: defaultAccountCode,
    effectiveDate: '',
    parentAccountCode: defaultAccountCode,
  }

const accountMappingEntityValidator = z.object({
  code: z.string().nullable(),
  effectiveDate: z.string().date(),
  endDate: z.string().date().nullable(),
  parentCode: z.string().nullable(),
})
const accountMappingEntityTransformer = z.object({
  ...accountMappingEntityValidator.shape,
  code: accountMappingEntityValidator.shape.code.transform(nullToUndefined),
  endDate:
    accountMappingEntityValidator.shape.endDate.transform(nullToUndefined),
  parentCode:
    accountMappingEntityValidator.shape.parentCode.transform(nullToUndefined),
})
export type AccountMappingEntityForm = z.input<
  typeof accountMappingEntityTransformer
>
export type AccountMappingEntity = z.infer<
  typeof accountMappingEntityTransformer
>
const accountMappingEntityDeprecated = z.object({
  code: z.string().nullable(),
  effectiveDate: z.string().date(),
  endDate: z.string().date().nullable(),
  parentCode: z.string().nullable(),
})
const defaultAccountMappingEntity: AccountMappingEntityForm = {
  code: '',
  effectiveDate: '',
  endDate: '',
  parentCode: '',
}

const accountMappingUpdateRequestValidator = z.object({
  childAccountCodes: z.array(accountCodeValidator),
  effectiveDate: z.string().date(),
  parentAccountCode: accountCodeValidator,
})
const accountMappingUpdateRequestTransformer = z.object({
  ...accountMappingUpdateRequestValidator.shape,
  childAccountCodes: z.array(accountCodeTransformer),
  parentAccountCode: accountCodeTransformer,
})
export type AccountMappingUpdateRequestForm = z.input<
  typeof accountMappingUpdateRequestTransformer
>
export type AccountMappingUpdateRequest = z.infer<
  typeof accountMappingUpdateRequestTransformer
>
const accountMappingUpdateRequestDeprecated = z.object({
  childAccountCodes: z.array(accountCodeDeprecated),
  effectiveDate: z.string().date(),
  parentAccountCode: accountCodeDeprecated,
})
export type DefaultAccountMappingUpdateRequestForm = Omit<
  AccountMappingUpdateRequestForm,
  'parentAccountCode'
> & {
  parentAccountCode: DefaultAccountCodeForm
}

const defaultAccountMappingUpdateRequest: DefaultAccountMappingUpdateRequestForm =
  {
    childAccountCodes: [],
    effectiveDate: '',
    parentAccountCode: defaultAccountCode,
  }

const accountTypeValidator = z.object({
  accountTypeCode: z.string(),
  analysisResultOptionsPlanCode: z.string(),
  balanceRequirementDefinitionCode: z.string(),
  description: z.string(),
  earningsCreditDefinitionCode: z.string().nullable(),
  effectiveDate: z.string().date(),
  interestRequirementDefinitionCode: z.string().nullable(),
  investableBalanceDefinitionCode: z.string(),
  reserveRequirementDefinitionCode: z.string().nullable(),
  settlementCyclePlanCode: z.string(),
  statementCyclePlanCode: z.string(),
  statementFormatPlanCode: z.string(),
  statementMessagePlanCode: z.string().nullable(),
})
const accountTypeTransformer = z.object({
  ...accountTypeValidator.shape,
  earningsCreditDefinitionCode:
    accountTypeValidator.shape.earningsCreditDefinitionCode.transform(
      nullToUndefined,
    ),
  interestRequirementDefinitionCode:
    accountTypeValidator.shape.interestRequirementDefinitionCode.transform(
      nullToUndefined,
    ),
  reserveRequirementDefinitionCode:
    accountTypeValidator.shape.reserveRequirementDefinitionCode.transform(
      nullToUndefined,
    ),
  statementMessagePlanCode:
    accountTypeValidator.shape.statementMessagePlanCode.transform(
      nullToUndefined,
    ),
})
export type AccountTypeForm = z.input<typeof accountTypeTransformer>
export type AccountType = z.infer<typeof accountTypeTransformer>
const accountTypeDeprecated = z.object({
  accountTypeCode: z.string(),
  analysisResultOptionsPlanCode: z.string(),
  balanceRequirementDefinitionCode: z.string(),
  description: z.string(),
  earningsCreditDefinitionCode: z.string().nullable(),
  effectiveDate: z.string().date(),
  interestRequirementDefinitionCode: z.string().nullable(),
  investableBalanceDefinitionCode: z.string(),
  reserveRequirementDefinitionCode: z.string().nullable(),
  settlementCyclePlanCode: z.string(),
  statementCyclePlanCode: z.string(),
  statementFormatPlanCode: z.string(),
  statementMessagePlanCode: z.string().nullable(),
})
const defaultAccountType: AccountTypeForm = {
  accountTypeCode: '',
  analysisResultOptionsPlanCode: '',
  balanceRequirementDefinitionCode: '',
  description: '',
  earningsCreditDefinitionCode: '',
  effectiveDate: '',
  interestRequirementDefinitionCode: '',
  investableBalanceDefinitionCode: '',
  reserveRequirementDefinitionCode: '',
  settlementCyclePlanCode: '',
  statementCyclePlanCode: '',
  statementFormatPlanCode: '',
  statementMessagePlanCode: '',
}

const accountTypeOverrideValidator = z.object({
  accountCode: accountCodeValidator,
  analysisResultOptionsPlanCode: z.string().nullable(),
  analysisResultOptionsPlanCodeExpiry: z.string().date().nullable(),
  balanceRequirementDefinitionCode: z.string().nullable(),
  balanceRequirementDefinitionCodeExpiry: z.string().date().nullable(),
  chargeAccountCode: z.string().nullable(),
  earningsCreditDefinitionCode: z.string().nullable(),
  earningsCreditDefinitionCodeExpiry: z.string().date().nullable(),
  effectiveDate: z.string().date(),
  interestRequirementDefinitionCode: z.string().nullable(),
  interestRequirementDefinitionCodeExpiry: z.string().date().nullable(),
  investableBalanceDefinitionCode: z.string().nullable(),
  investableBalanceDefinitionCodeExpiry: z.string().date().nullable(),
  isOverrideAsSettlementAccount: z.boolean(),
  reserveRequirementDefinitionCode: z.string().nullable(),
  reserveRequirementDefinitionCodeExpiry: z.string().date().nullable(),
  settlementCyclePlanCode: z.string().nullable(),
  settlementCyclePlanCodeExpiry: z.string().date().nullable(),
  statementCyclePlanCode: z.string().nullable(),
  statementCyclePlanCodeExpiry: z.string().date().nullable(),
  statementFormatPlanCode: z.string().nullable(),
  statementFormatPlanCodeExpiry: z.string().date().nullable(),
  statementMessagePlanCode: z.string().nullable(),
  statementMessagePlanCodeExpiry: z.string().date().nullable(),
})
const accountTypeOverrideTransformer = z.object({
  ...accountTypeOverrideValidator.shape,
  accountCode: accountCodeTransformer,
  analysisResultOptionsPlanCode:
    accountTypeOverrideValidator.shape.analysisResultOptionsPlanCode.transform(
      nullToUndefined,
    ),
  analysisResultOptionsPlanCodeExpiry:
    accountTypeOverrideValidator.shape.analysisResultOptionsPlanCodeExpiry.transform(
      nullToUndefined,
    ),
  balanceRequirementDefinitionCode:
    accountTypeOverrideValidator.shape.balanceRequirementDefinitionCode.transform(
      nullToUndefined,
    ),
  balanceRequirementDefinitionCodeExpiry:
    accountTypeOverrideValidator.shape.balanceRequirementDefinitionCodeExpiry.transform(
      nullToUndefined,
    ),
  chargeAccountCode:
    accountTypeOverrideValidator.shape.chargeAccountCode.transform(
      nullToUndefined,
    ),
  earningsCreditDefinitionCode:
    accountTypeOverrideValidator.shape.earningsCreditDefinitionCode.transform(
      nullToUndefined,
    ),
  earningsCreditDefinitionCodeExpiry:
    accountTypeOverrideValidator.shape.earningsCreditDefinitionCodeExpiry.transform(
      nullToUndefined,
    ),
  interestRequirementDefinitionCode:
    accountTypeOverrideValidator.shape.interestRequirementDefinitionCode.transform(
      nullToUndefined,
    ),
  interestRequirementDefinitionCodeExpiry:
    accountTypeOverrideValidator.shape.interestRequirementDefinitionCodeExpiry.transform(
      nullToUndefined,
    ),
  investableBalanceDefinitionCode:
    accountTypeOverrideValidator.shape.investableBalanceDefinitionCode.transform(
      nullToUndefined,
    ),
  investableBalanceDefinitionCodeExpiry:
    accountTypeOverrideValidator.shape.investableBalanceDefinitionCodeExpiry.transform(
      nullToUndefined,
    ),
  reserveRequirementDefinitionCode:
    accountTypeOverrideValidator.shape.reserveRequirementDefinitionCode.transform(
      nullToUndefined,
    ),
  reserveRequirementDefinitionCodeExpiry:
    accountTypeOverrideValidator.shape.reserveRequirementDefinitionCodeExpiry.transform(
      nullToUndefined,
    ),
  settlementCyclePlanCode:
    accountTypeOverrideValidator.shape.settlementCyclePlanCode.transform(
      nullToUndefined,
    ),
  settlementCyclePlanCodeExpiry:
    accountTypeOverrideValidator.shape.settlementCyclePlanCodeExpiry.transform(
      nullToUndefined,
    ),
  statementCyclePlanCode:
    accountTypeOverrideValidator.shape.statementCyclePlanCode.transform(
      nullToUndefined,
    ),
  statementCyclePlanCodeExpiry:
    accountTypeOverrideValidator.shape.statementCyclePlanCodeExpiry.transform(
      nullToUndefined,
    ),
  statementFormatPlanCode:
    accountTypeOverrideValidator.shape.statementFormatPlanCode.transform(
      nullToUndefined,
    ),
  statementFormatPlanCodeExpiry:
    accountTypeOverrideValidator.shape.statementFormatPlanCodeExpiry.transform(
      nullToUndefined,
    ),
  statementMessagePlanCode:
    accountTypeOverrideValidator.shape.statementMessagePlanCode.transform(
      nullToUndefined,
    ),
  statementMessagePlanCodeExpiry:
    accountTypeOverrideValidator.shape.statementMessagePlanCodeExpiry.transform(
      nullToUndefined,
    ),
})
export type AccountTypeOverrideForm = z.input<
  typeof accountTypeOverrideTransformer
>
export type AccountTypeOverride = z.infer<typeof accountTypeOverrideTransformer>
const accountTypeOverrideDeprecated = z.object({
  accountCode: accountCodeDeprecated,
  analysisResultOptionsPlanCode: z.string().nullable(),
  analysisResultOptionsPlanCodeExpiry: z.string().date().nullable(),
  balanceRequirementDefinitionCode: z.string().nullable(),
  balanceRequirementDefinitionCodeExpiry: z.string().date().nullable(),
  chargeAccountCode: z.string().nullable(),
  earningsCreditDefinitionCode: z.string().nullable(),
  earningsCreditDefinitionCodeExpiry: z.string().date().nullable(),
  effectiveDate: z.string().date(),
  interestRequirementDefinitionCode: z.string().nullable(),
  interestRequirementDefinitionCodeExpiry: z.string().date().nullable(),
  investableBalanceDefinitionCode: z.string().nullable(),
  investableBalanceDefinitionCodeExpiry: z.string().date().nullable(),
  isOverrideAsSettlementAccount: z.boolean(),
  reserveRequirementDefinitionCode: z.string().nullable(),
  reserveRequirementDefinitionCodeExpiry: z.string().date().nullable(),
  settlementCyclePlanCode: z.string().nullable(),
  settlementCyclePlanCodeExpiry: z.string().date().nullable(),
  statementCyclePlanCode: z.string().nullable(),
  statementCyclePlanCodeExpiry: z.string().date().nullable(),
  statementFormatPlanCode: z.string().nullable(),
  statementFormatPlanCodeExpiry: z.string().date().nullable(),
  statementMessagePlanCode: z.string().nullable(),
  statementMessagePlanCodeExpiry: z.string().date().nullable(),
})
export type DefaultAccountTypeOverrideForm = Omit<
  AccountTypeOverrideForm,
  'isOverrideAsSettlementAccount' | 'accountCode'
> & {
  accountCode: DefaultAccountCodeForm
}

const defaultAccountTypeOverride: DefaultAccountTypeOverrideForm = {
  accountCode: defaultAccountCode,
  analysisResultOptionsPlanCode: '',
  analysisResultOptionsPlanCodeExpiry: '',
  balanceRequirementDefinitionCode: '',
  balanceRequirementDefinitionCodeExpiry: '',
  chargeAccountCode: '',
  earningsCreditDefinitionCode: '',
  earningsCreditDefinitionCodeExpiry: '',
  effectiveDate: '',
  interestRequirementDefinitionCode: '',
  interestRequirementDefinitionCodeExpiry: '',
  investableBalanceDefinitionCode: '',
  investableBalanceDefinitionCodeExpiry: '',
  reserveRequirementDefinitionCode: '',
  reserveRequirementDefinitionCodeExpiry: '',
  settlementCyclePlanCode: '',
  settlementCyclePlanCodeExpiry: '',
  statementCyclePlanCode: '',
  statementCyclePlanCodeExpiry: '',
  statementFormatPlanCode: '',
  statementFormatPlanCodeExpiry: '',
  statementMessagePlanCode: '',
  statementMessagePlanCodeExpiry: '',
}

const accountWithKeyValidator = z.object({
  accountNumber: z.string(),
  accountStatus: z.enum(['Z', 'B', 'C', 'P', 'X']).nullable(),
  analysisAccountTypeCode: z.string(),
  applicationId: z.enum(['C', 'D']),
  bankNumber: z.string(),
  branchCode: z.string(),
  closeDate: z.string().date().nullable(),
  costCenter: z.string(),
  currencyCode: z.string(),
  customerSpecificPricingIndicator: z.boolean().nullable(),
  depositAccountTypeCode: z.string().nullable(),
  depositCategory: z
    .enum(['A', 'C', 'D', 'L', 'M', 'N', 'R', 'S', 'P', 'T'])
    .nullable(),
  effectiveDate: z.string().date(),
  isKeyAccount: z.boolean().nullable(),
  keyAccountCode: accountCodeValidator.nullable(),
  openDate: z.string().date(),
  primaryOfficerCode: z.string(),
  processingIndicator: z.enum(['A', 'B']).nullable(),
  secondaryOfficerCode: z.string().nullable(),
  shortName: z.string(),
  treasuryOfficerCode: z.string().nullable(),
})
const accountWithKeyTransformer = z.object({
  ...accountWithKeyValidator.shape,
  accountStatus:
    accountWithKeyValidator.shape.accountStatus.transform(nullToUndefined),
  closeDate: accountWithKeyValidator.shape.closeDate.transform(nullToUndefined),
  customerSpecificPricingIndicator:
    accountWithKeyValidator.shape.customerSpecificPricingIndicator.transform(
      nullToUndefined,
    ),
  depositAccountTypeCode:
    accountWithKeyValidator.shape.depositAccountTypeCode.transform(
      nullToUndefined,
    ),
  depositCategory:
    accountWithKeyValidator.shape.depositCategory.transform(nullToUndefined),
  isKeyAccount:
    accountWithKeyValidator.shape.isKeyAccount.transform(nullToUndefined),
  keyAccountCode: accountCodeTransformer.nullable().transform(nullToUndefined),
  processingIndicator:
    accountWithKeyValidator.shape.processingIndicator.transform(
      nullToUndefined,
    ),
  secondaryOfficerCode:
    accountWithKeyValidator.shape.secondaryOfficerCode.transform(
      nullToUndefined,
    ),
  treasuryOfficerCode:
    accountWithKeyValidator.shape.treasuryOfficerCode.transform(
      nullToUndefined,
    ),
})
export type AccountWithKeyForm = z.input<typeof accountWithKeyTransformer>
export type AccountWithKey = z.infer<typeof accountWithKeyTransformer>
const accountWithKeyDeprecated = z.object({
  accountNumber: z.string(),
  accountStatus: z.enum(['Z', 'B', 'C', 'P', 'X']).nullable(),
  analysisAccountTypeCode: z.string(),
  applicationId: z.enum(['C', 'D']),
  bankNumber: z.string(),
  branchCode: z.string(),
  closeDate: z.string().date().nullable(),
  costCenter: z.string(),
  currencyCode: z.string(),
  customerSpecificPricingIndicator: z.boolean().nullable(),
  depositAccountTypeCode: z.string().nullable(),
  depositCategory: z
    .enum(['A', 'C', 'D', 'L', 'M', 'N', 'R', 'S', 'P', 'T'])
    .nullable(),
  effectiveDate: z.string().date(),
  isKeyAccount: z.boolean().nullable(),
  keyAccountCode: accountCodeDeprecated,
  openDate: z.string().date(),
  primaryOfficerCode: z.string(),
  processingIndicator: z.enum(['A', 'B']).nullable(),
  secondaryOfficerCode: z.string().nullable(),
  shortName: z.string(),
  treasuryOfficerCode: z.string().nullable(),
})
export type DefaultAccountWithKeyForm = Omit<
  AccountWithKeyForm,
  'applicationId' | 'keyAccountCode'
> & {
  keyAccountCode: DefaultAccountCodeForm
}

const defaultAccountWithKey: DefaultAccountWithKeyForm = {
  accountNumber: '',
  accountStatus: null,
  analysisAccountTypeCode: '',
  bankNumber: '',
  branchCode: '',
  closeDate: '',
  costCenter: '',
  currencyCode: '',
  customerSpecificPricingIndicator: null,
  depositAccountTypeCode: '',
  depositCategory: null,
  effectiveDate: '',
  isKeyAccount: null,
  keyAccountCode: defaultAccountCode,
  openDate: '',
  primaryOfficerCode: '',
  processingIndicator: null,
  secondaryOfficerCode: '',
  shortName: '',
  treasuryOfficerCode: '',
}

const addressValidator = z.object({
  accountCode: accountCodeValidator,
  addressLine2: z.string().nullable(),
  addressLine3: z.string().nullable(),
  addressLine4: z.string().nullable(),
  addressLine5: z.string().nullable(),
  addressLine6: z.string().nullable(),
  addressLine7: z.string().nullable(),
  addressNumber: z.number().int(),
  applicationId: z.enum(['A', 'D']),
  effectiveDate: z.string().date(),
  name: z.string().nullable(),
})
const addressTransformer = z.object({
  ...addressValidator.shape,
  accountCode: accountCodeTransformer,
  addressLine2: addressValidator.shape.addressLine2.transform(nullToUndefined),
  addressLine3: addressValidator.shape.addressLine3.transform(nullToUndefined),
  addressLine4: addressValidator.shape.addressLine4.transform(nullToUndefined),
  addressLine5: addressValidator.shape.addressLine5.transform(nullToUndefined),
  addressLine6: addressValidator.shape.addressLine6.transform(nullToUndefined),
  addressLine7: addressValidator.shape.addressLine7.transform(nullToUndefined),
  name: addressValidator.shape.name.transform(nullToUndefined),
})
export type AddressForm = z.input<typeof addressTransformer>
export type Address = z.infer<typeof addressTransformer>
const addressDeprecated = z.object({
  accountCode: accountCodeDeprecated,
  addressLine2: z.string().nullable(),
  addressLine3: z.string().nullable(),
  addressLine4: z.string().nullable(),
  addressLine5: z.string().nullable(),
  addressLine6: z.string().nullable(),
  addressLine7: z.string().nullable(),
  addressNumber: z.number().int(),
  applicationId: z.enum(['A', 'D']),
  effectiveDate: z.string().date(),
  name: z.string().nullable(),
})
export type DefaultAddressForm = Omit<
  AddressForm,
  'addressNumber' | 'applicationId' | 'accountCode'
> & {
  accountCode: DefaultAccountCodeForm
}

const defaultAddress: DefaultAddressForm = {
  accountCode: defaultAccountCode,
  addressLine2: '',
  addressLine3: '',
  addressLine4: '',
  addressLine5: '',
  addressLine6: '',
  addressLine7: '',
  effectiveDate: '',
  name: '',
}

const addressCodeValidator = z.object({
  accountCode: accountCodeValidator,
  addressNumber: z.number().int(),
  applicationId: z.enum(['A', 'D']),
})
const addressCodeTransformer = z.object({
  ...addressCodeValidator.shape,
  accountCode: accountCodeTransformer,
})
export type AddressCodeForm = z.input<typeof addressCodeTransformer>
export type AddressCode = z.infer<typeof addressCodeTransformer>
const addressCodeDeprecated = z.object({
  accountCode: accountCodeDeprecated,
  addressNumber: z.number().int(),
  applicationId: z.enum(['A', 'D']),
})
export type DefaultAddressCodeForm = Omit<
  AddressCodeForm,
  'addressNumber' | 'applicationId' | 'accountCode'
> & {
  accountCode: DefaultAccountCodeForm
}

const defaultAddressCode: DefaultAddressCodeForm = {
  accountCode: defaultAccountCode,
}

const addressCodeAvailabilityRequestValidator = z.object({
  accountApplicationId: z.enum(['C', 'D']),
  accountNumber: z.string(),
  addressApplicationId: z.enum(['A', 'D']),
  addressNumber: z.number().int(),
  bankNumber: z.string(),
})
const addressCodeAvailabilityRequestTransformer =
  addressCodeAvailabilityRequestValidator
export type AddressCodeAvailabilityRequestForm = z.input<
  typeof addressCodeAvailabilityRequestTransformer
>
export type AddressCodeAvailabilityRequest = z.infer<
  typeof addressCodeAvailabilityRequestTransformer
>
const addressCodeAvailabilityRequestDeprecated = z.object({
  accountApplicationId: z.enum(['C', 'D']),
  accountNumber: z.string(),
  addressApplicationId: z.enum(['A', 'D']),
  addressNumber: z.number().int(),
  bankNumber: z.string(),
})
export type DefaultAddressCodeAvailabilityRequestForm = Omit<
  AddressCodeAvailabilityRequestForm,
  'accountApplicationId' | 'addressApplicationId' | 'addressNumber'
>
const defaultAddressCodeAvailabilityRequest: DefaultAddressCodeAvailabilityRequestForm =
  {
    accountNumber: '',
    bankNumber: '',
  }

const addressCodeAvailabilityResponseValidator = z.object({
  available: z.boolean(),
  suggestedAddressNumber: z.number().int(),
})
const addressCodeAvailabilityResponseTransformer =
  addressCodeAvailabilityResponseValidator
export type AddressCodeAvailabilityResponseForm = z.input<
  typeof addressCodeAvailabilityResponseTransformer
>
export type AddressCodeAvailabilityResponse = z.infer<
  typeof addressCodeAvailabilityResponseTransformer
>
const addressCodeAvailabilityResponseDeprecated = z.object({
  available: z.boolean(),
  suggestedAddressNumber: z.number().int(),
})
export type DefaultAddressCodeAvailabilityResponseForm = Omit<
  AddressCodeAvailabilityResponseForm,
  'available' | 'suggestedAddressNumber'
>
const defaultAddressCodeAvailabilityResponse: DefaultAddressCodeAvailabilityResponseForm =
  {}

const addressRetrieveRequestValidator = z.object({
  accountApplicationId: z.enum(['C', 'D']),
  accountCode: accountCodeValidator.nullable(),
  accountNumber: z.string(),
  addressApplicationId: z.enum(['A', 'D']),
  addressNumber: z.number().int(),
  bankNumber: z.string(),
  effectiveDate: z.string().date(),
})
const addressRetrieveRequestTransformer = z.object({
  ...addressRetrieveRequestValidator.shape,
  accountCode: accountCodeTransformer.nullable().transform(nullToUndefined),
})
export type AddressRetrieveRequestForm = z.input<
  typeof addressRetrieveRequestTransformer
>
export type AddressRetrieveRequest = z.infer<
  typeof addressRetrieveRequestTransformer
>
const addressRetrieveRequestDeprecated = z.object({
  accountApplicationId: z.enum(['C', 'D']),
  accountCode: accountCodeDeprecated,
  accountNumber: z.string(),
  addressApplicationId: z.enum(['A', 'D']),
  addressNumber: z.number().int(),
  bankNumber: z.string(),
  effectiveDate: z.string().date(),
})
export type DefaultAddressRetrieveRequestForm = Omit<
  AddressRetrieveRequestForm,
  | 'accountApplicationId'
  | 'addressApplicationId'
  | 'addressNumber'
  | 'accountCode'
> & {
  accountCode: DefaultAccountCodeForm
}

const defaultAddressRetrieveRequest: DefaultAddressRetrieveRequestForm = {
  accountCode: defaultAccountCode,
  accountNumber: '',
  bankNumber: '',
  effectiveDate: '',
}

const analysisResultOptionValidator = z.object({
  analysisChargeType: z.enum(['DIRECT_DEBIT', 'WAIVE']),
  analysisDirectDebitTrailer: z.string().nullable(),
  code: z.string(),
  daysAfter: z.number().int().nullable(),
  delay: z.string().pipe(z.coerce.number()).nullable(),
  effectiveDate: z.string().date(),
  excessCredits: z.enum(['WAIVE']),
  hardCharge: z.enum(['DIRECT_DEBIT', 'WAIVE']),
  hardDirectDebitTrailer: z.string().nullable(),
  markdownRate: z.string().pipe(z.coerce.number()).nullable(),
  markdownStatementLabel: z.string().nullable(),
  markupRate: z.string().pipe(z.coerce.number()).nullable(),
  markupStatementLabel: z.string().nullable(),
  maxChargeWaiveAmount: z.string().pipe(z.coerce.number()).nullable(),
  minChargeWaiveAmount: z.string().pipe(z.coerce.number()).nullable(),
  name: z.string(),
  settlementOverrideDateEachMonth: z.number().int().nullable(),
  settlementOverrideType: z.enum([
    'NO_OVERRIDE',
    'SAME_DATE_EACH_MONTH',
    'SPECIFIC_DAYS_AFTER_PRELIM_ANALYSIS',
  ]),
  waiveCycle: z.number().int().nullable(),
})
const analysisResultOptionTransformer = z.object({
  ...analysisResultOptionValidator.shape,
  analysisDirectDebitTrailer:
    analysisResultOptionValidator.shape.analysisDirectDebitTrailer.transform(
      nullToUndefined,
    ),
  daysAfter:
    analysisResultOptionValidator.shape.daysAfter.transform(nullToUndefined),
  delay: analysisResultOptionValidator.shape.delay.transform(nullToUndefined),
  hardDirectDebitTrailer:
    analysisResultOptionValidator.shape.hardDirectDebitTrailer.transform(
      nullToUndefined,
    ),
  markdownRate:
    analysisResultOptionValidator.shape.markdownRate.transform(nullToUndefined),
  markdownStatementLabel:
    analysisResultOptionValidator.shape.markdownStatementLabel.transform(
      nullToUndefined,
    ),
  markupRate:
    analysisResultOptionValidator.shape.markupRate.transform(nullToUndefined),
  markupStatementLabel:
    analysisResultOptionValidator.shape.markupStatementLabel.transform(
      nullToUndefined,
    ),
  maxChargeWaiveAmount:
    analysisResultOptionValidator.shape.maxChargeWaiveAmount.transform(
      nullToUndefined,
    ),
  minChargeWaiveAmount:
    analysisResultOptionValidator.shape.minChargeWaiveAmount.transform(
      nullToUndefined,
    ),
  settlementOverrideDateEachMonth:
    analysisResultOptionValidator.shape.settlementOverrideDateEachMonth.transform(
      nullToUndefined,
    ),
  waiveCycle:
    analysisResultOptionValidator.shape.waiveCycle.transform(nullToUndefined),
})
export type AnalysisResultOptionForm = z.input<
  typeof analysisResultOptionTransformer
>
export type AnalysisResultOption = z.infer<
  typeof analysisResultOptionTransformer
>
const analysisResultOptionDeprecated = z.object({
  analysisChargeType: z.enum(['DIRECT_DEBIT', 'WAIVE']),
  analysisDirectDebitTrailer: z.string().nullable(),
  code: z.string(),
  daysAfter: z.number().int().nullable(),
  delay: z.string().pipe(z.coerce.number()).nullable(),
  effectiveDate: z.string().date(),
  excessCredits: z.enum(['WAIVE']),
  hardCharge: z.enum(['DIRECT_DEBIT', 'WAIVE']),
  hardDirectDebitTrailer: z.string().nullable(),
  markdownRate: z.string().pipe(z.coerce.number()).nullable(),
  markdownStatementLabel: z.string().nullable(),
  markupRate: z.string().pipe(z.coerce.number()).nullable(),
  markupStatementLabel: z.string().nullable(),
  maxChargeWaiveAmount: z.string().pipe(z.coerce.number()).nullable(),
  minChargeWaiveAmount: z.string().pipe(z.coerce.number()).nullable(),
  name: z.string(),
  settlementOverrideDateEachMonth: z.number().int().nullable(),
  settlementOverrideType: z.enum([
    'NO_OVERRIDE',
    'SAME_DATE_EACH_MONTH',
    'SPECIFIC_DAYS_AFTER_PRELIM_ANALYSIS',
  ]),
  waiveCycle: z.number().int().nullable(),
})
export type DefaultAnalysisResultOptionForm = Omit<
  AnalysisResultOptionForm,
  | 'analysisChargeType'
  | 'excessCredits'
  | 'hardCharge'
  | 'settlementOverrideType'
>
const defaultAnalysisResultOption: DefaultAnalysisResultOptionForm = {
  analysisDirectDebitTrailer: '',
  code: '',
  daysAfter: null,
  delay: null,
  effectiveDate: '',
  hardDirectDebitTrailer: '',
  markdownRate: null,
  markdownStatementLabel: '',
  markupRate: null,
  markupStatementLabel: '',
  maxChargeWaiveAmount: null,
  minChargeWaiveAmount: null,
  name: '',
  settlementOverrideDateEachMonth: null,
  waiveCycle: null,
}

const balanceRequirementDefinitionValidator = z.object({
  addOtherBalanceLabel: z.string().nullable(),
  baseBalanceType: z.enum([
    'AVERAGE_COLLECTED',
    'AVERAGE_LEDGER',
    'AVERAGE_NEGATIVE_COLLECTED',
    'AVERAGE_NEGATIVE_LEDGER',
    'AVERAGE_POSITIVE_COLLECTED',
    'AVERAGE_POSITIVE_LEDGER',
    'AVERAGE_UNCOLLECTED_FUNDS',
    'COMPENSATING_BALANCE',
    'END_OF_MONTH_LEDGER',
    'INVESTABLE_BALANCE',
    'AVERAGE_FLOAT',
    'AVERAGE_CLEARINGHOUSE_FLOAT',
    'REQUIRED_BALANCE',
  ]),
  code: z.string(),
  effectiveDate: z.string().date(),
  name: z.string(),
  subtractCompensatingBalance: z.boolean(),
  subtractInterestPaidMonthToDate: z.boolean(),
  subtractOtherBalanceLabel: z.string().nullable(),
})
const balanceRequirementDefinitionTransformer = z.object({
  ...balanceRequirementDefinitionValidator.shape,
  addOtherBalanceLabel:
    balanceRequirementDefinitionValidator.shape.addOtherBalanceLabel.transform(
      nullToUndefined,
    ),
  subtractOtherBalanceLabel:
    balanceRequirementDefinitionValidator.shape.subtractOtherBalanceLabel.transform(
      nullToUndefined,
    ),
})
export type BalanceRequirementDefinitionForm = z.input<
  typeof balanceRequirementDefinitionTransformer
>
export type BalanceRequirementDefinition = z.infer<
  typeof balanceRequirementDefinitionTransformer
>
const balanceRequirementDefinitionDeprecated = z.object({
  addOtherBalanceLabel: z.string().nullable(),
  baseBalanceType: z.enum([
    'AVERAGE_COLLECTED',
    'AVERAGE_LEDGER',
    'AVERAGE_NEGATIVE_COLLECTED',
    'AVERAGE_NEGATIVE_LEDGER',
    'AVERAGE_POSITIVE_COLLECTED',
    'AVERAGE_POSITIVE_LEDGER',
    'AVERAGE_UNCOLLECTED_FUNDS',
    'COMPENSATING_BALANCE',
    'END_OF_MONTH_LEDGER',
    'INVESTABLE_BALANCE',
    'AVERAGE_FLOAT',
    'AVERAGE_CLEARINGHOUSE_FLOAT',
    'REQUIRED_BALANCE',
  ]),
  code: z.string(),
  effectiveDate: z.string().date(),
  name: z.string(),
  subtractCompensatingBalance: z.boolean(),
  subtractInterestPaidMonthToDate: z.boolean(),
  subtractOtherBalanceLabel: z.string().nullable(),
})
export type DefaultBalanceRequirementDefinitionForm = Omit<
  BalanceRequirementDefinitionForm,
  | 'baseBalanceType'
  | 'subtractCompensatingBalance'
  | 'subtractInterestPaidMonthToDate'
>
const defaultBalanceRequirementDefinition: DefaultBalanceRequirementDefinitionForm =
  {
    addOtherBalanceLabel: '',
    code: '',
    effectiveDate: '',
    name: '',
    subtractOtherBalanceLabel: '',
  }

const balanceTierValidator = z.object({
  indexAdjustmentRate: z.string().pipe(z.coerce.number()).nullable(),
  indexRate: z.string().pipe(z.coerce.number()).nullable(),
  maxTierExclusive: z.string().pipe(z.coerce.number()),
  minTierInclusive: z.string().pipe(z.coerce.number()),
})
const balanceTierTransformer = z.object({
  ...balanceTierValidator.shape,
  indexAdjustmentRate:
    balanceTierValidator.shape.indexAdjustmentRate.transform(nullToUndefined),
  indexRate: balanceTierValidator.shape.indexRate.transform(nullToUndefined),
})
export type BalanceTierForm = z.input<typeof balanceTierTransformer>
export type BalanceTier = z.infer<typeof balanceTierTransformer>
const balanceTierDeprecated = z.object({
  indexAdjustmentRate: z.string().pipe(z.coerce.number()).nullable(),
  indexRate: z.string().pipe(z.coerce.number()).nullable(),
  maxTierExclusive: z.string().pipe(z.coerce.number()),
  minTierInclusive: z.string().pipe(z.coerce.number()),
})
export type DefaultBalanceTierForm = Omit<
  BalanceTierForm,
  'maxTierExclusive' | 'minTierInclusive'
>
const defaultBalanceTier: DefaultBalanceTierForm = {
  indexAdjustmentRate: null,
  indexRate: null,
}

const bankOptionsValidator = z.object({
  accountNumberDigits: z.number().int(),
  accountNumberMasking: z.boolean(),
  accountNumberPattern: z.string(),
  assignDefaultStatementPackageToDepositAccount: z.boolean(),
  automaticPrelimAnalysis: z.boolean(),
  balanceCycleDays: z.enum(['ACTUAL_DAYS_IN_MONTH', 'OPEN_DAYS_IN_MONTH']),
  calculatingBalanceFeeBasis: z.enum(['360', '365', '366']),
  calculatingEarningsCreditBasis: z.enum(['360', '365', '366']),
  code: z.string(),
  copyAccountTypeOfKeyAccount: z.boolean(),
  copyUserFieldsFromKeyAccount: z.boolean(),
  defaultCurrency: z.enum(['US_DOLLARS']),
  defaultTermInMonthsForPromoAndOverrides: z.number().int(),
  earningsCycleDays: z.enum([
    'ACTUAL_DAYS_IN_MONTH',
    'OPEN_DAYS_IN_MONTH',
    'THIRTY',
  ]),
  effectiveDate: z.string().date(),
  finalAnalysis: z.enum(['SAME_DATE_EACH_MONTH', 'SPECIFIC_DAYS_POST_PRELIM']),
  finalAnalysisDays: z.number().int(),
  name: z.string(),
  prelimAnalysis: z.enum([
    'LastBusinessDayPriorMonth',
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9',
    '10',
    '11',
    '12',
    '13',
    '14',
    '15',
    '16',
    '17',
    '18',
    '19',
    '20',
    '21',
    '22',
    '23',
    '24',
    '25',
    '26',
    '27',
  ]),
  retentionMonths: z.number().int(),
  statementArchivingFrequency: z.enum([
    'AT_FINAL_AND_REANALYSIS',
    'AT_FINAL',
    'NOT_ARCHIVED',
  ]),
})
const bankOptionsTransformer = bankOptionsValidator
export type BankOptionsForm = z.input<typeof bankOptionsTransformer>
export type BankOptions = z.infer<typeof bankOptionsTransformer>
const bankOptionsDeprecated = z.object({
  accountNumberDigits: z.number().int(),
  accountNumberMasking: z.boolean(),
  accountNumberPattern: z.string(),
  assignDefaultStatementPackageToDepositAccount: z.boolean(),
  automaticPrelimAnalysis: z.boolean(),
  balanceCycleDays: z.enum(['ACTUAL_DAYS_IN_MONTH', 'OPEN_DAYS_IN_MONTH']),
  calculatingBalanceFeeBasis: z.enum(['360', '365', '366']),
  calculatingEarningsCreditBasis: z.enum(['360', '365', '366']),
  code: z.string(),
  copyAccountTypeOfKeyAccount: z.boolean(),
  copyUserFieldsFromKeyAccount: z.boolean(),
  defaultCurrency: z.enum(['US_DOLLARS']),
  defaultTermInMonthsForPromoAndOverrides: z.number().int(),
  earningsCycleDays: z.enum([
    'ACTUAL_DAYS_IN_MONTH',
    'OPEN_DAYS_IN_MONTH',
    'THIRTY',
  ]),
  effectiveDate: z.string().date(),
  finalAnalysis: z.enum(['SAME_DATE_EACH_MONTH', 'SPECIFIC_DAYS_POST_PRELIM']),
  finalAnalysisDays: z.number().int(),
  name: z.string(),
  prelimAnalysis: z.enum([
    'LastBusinessDayPriorMonth',
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9',
    '10',
    '11',
    '12',
    '13',
    '14',
    '15',
    '16',
    '17',
    '18',
    '19',
    '20',
    '21',
    '22',
    '23',
    '24',
    '25',
    '26',
    '27',
  ]),
  retentionMonths: z.number().int(),
  statementArchivingFrequency: z.enum([
    'AT_FINAL_AND_REANALYSIS',
    'AT_FINAL',
    'NOT_ARCHIVED',
  ]),
})
export type DefaultBankOptionsForm = Omit<
  BankOptionsForm,
  | 'accountNumberDigits'
  | 'accountNumberMasking'
  | 'assignDefaultStatementPackageToDepositAccount'
  | 'automaticPrelimAnalysis'
  | 'balanceCycleDays'
  | 'calculatingBalanceFeeBasis'
  | 'calculatingEarningsCreditBasis'
  | 'copyAccountTypeOfKeyAccount'
  | 'copyUserFieldsFromKeyAccount'
  | 'defaultCurrency'
  | 'defaultTermInMonthsForPromoAndOverrides'
  | 'earningsCycleDays'
  | 'finalAnalysis'
  | 'finalAnalysisDays'
  | 'prelimAnalysis'
  | 'retentionMonths'
  | 'statementArchivingFrequency'
>
const defaultBankOptions: DefaultBankOptionsForm = {
  accountNumberPattern: '',
  code: '',
  effectiveDate: '',
  name: '',
}

const branchValidator = z.object({
  abaNumbers: z.array(z.string()),
  addressLine1: z.string(),
  addressLine2: z.string(),
  addressLine3: z.string(),
  bankNumber: z.string(),
  branchName: z.string(),
  code: z.string(),
  correspondentAccountNumber: z.string().nullable(),
  effectiveDate: z.string().date(),
  phoneNumber: z.string(),
})
const branchTransformer = z.object({
  ...branchValidator.shape,
  correspondentAccountNumber:
    branchValidator.shape.correspondentAccountNumber.transform(nullToUndefined),
})
export type BranchForm = z.input<typeof branchTransformer>
export type Branch = z.infer<typeof branchTransformer>
const branchDeprecated = z.object({
  abaNumbers: z.array(z.string()),
  addressLine1: z.string(),
  addressLine2: z.string(),
  addressLine3: z.string(),
  bankNumber: z.string(),
  branchName: z.string(),
  code: z.string(),
  correspondentAccountNumber: z.string().nullable(),
  effectiveDate: z.string().date(),
  phoneNumber: z.string(),
})
const defaultBranch: BranchForm = {
  abaNumbers: [],
  addressLine1: '',
  addressLine2: '',
  addressLine3: '',
  bankNumber: '',
  branchName: '',
  code: '',
  correspondentAccountNumber: '',
  effectiveDate: '',
  phoneNumber: '',
}

const codeRequestBodyValidator = z.object({
  code: z.string(),
})
const codeRequestBodyTransformer = codeRequestBodyValidator
export type CodeRequestBodyForm = z.input<typeof codeRequestBodyTransformer>
export type CodeRequestBody = z.infer<typeof codeRequestBodyTransformer>
const codeRequestBodyDeprecated = z.object({
  code: z.string(),
})
const defaultCodeRequestBody: CodeRequestBodyForm = {
  code: '',
}

const codesAndEffectiveDateRequestBodyValidator = z.object({
  codes: z.array(z.string()),
  effectiveDate: z.string().date(),
})
const codesAndEffectiveDateRequestBodyTransformer =
  codesAndEffectiveDateRequestBodyValidator
export type CodesAndEffectiveDateRequestBodyForm = z.input<
  typeof codesAndEffectiveDateRequestBodyTransformer
>
export type CodesAndEffectiveDateRequestBody = z.infer<
  typeof codesAndEffectiveDateRequestBodyTransformer
>
const codesAndEffectiveDateRequestBodyDeprecated = z.object({
  codes: z.array(z.string()),
  effectiveDate: z.string().date(),
})
const defaultCodesAndEffectiveDateRequestBody: CodesAndEffectiveDateRequestBodyForm =
  {
    codes: [],
    effectiveDate: '',
  }

const statementPackageCreateUpdateRequestValidator = z.object({
  accountApplicationId: z.enum(['C', 'D']),
  accountNumber: z.string(),
  addressAccountApplicationId: z.enum(['C', 'D']),
  addressAccountNumber: z.string(),
  addressApplicationId: z.enum(['A', 'D']),
  addressNumber: z.number().int(),
  bankNumber: z.string(),
  effectiveDate: z.string().date(),
  packageDelivery: z.enum([
    'ELECTRONIC',
    'ELECTRONIC_AND_PRINT',
    'PRINT',
    'NO_STATEMENT',
  ]),
  packageType: z.enum([
    'ALL_ACCOUNTS',
    'COMPOSITE_ACCOUNTS',
    'DEPOSIT_ACCOUNTS',
    'SELECTED_ACCOUNTS',
  ]),
  selectedAccounts: z.array(accountCodeValidator),
  statementPackageNumber: z.number().int(),
})
const statementPackageCreateUpdateRequestTransformer = z.object({
  ...statementPackageCreateUpdateRequestValidator.shape,
  selectedAccounts: z.array(accountCodeTransformer),
})
export type StatementPackageCreateUpdateRequestForm = z.input<
  typeof statementPackageCreateUpdateRequestTransformer
>
export type StatementPackageCreateUpdateRequest = z.infer<
  typeof statementPackageCreateUpdateRequestTransformer
>
const statementPackageCreateUpdateRequestDeprecated = z.object({
  accountApplicationId: z.enum(['C', 'D']),
  accountNumber: z.string(),
  addressAccountApplicationId: z.enum(['C', 'D']),
  addressAccountNumber: z.string(),
  addressApplicationId: z.enum(['A', 'D']),
  addressNumber: z.number().int(),
  bankNumber: z.string(),
  effectiveDate: z.string().date(),
  packageDelivery: z.enum([
    'ELECTRONIC',
    'ELECTRONIC_AND_PRINT',
    'PRINT',
    'NO_STATEMENT',
  ]),
  packageType: z.enum([
    'ALL_ACCOUNTS',
    'COMPOSITE_ACCOUNTS',
    'DEPOSIT_ACCOUNTS',
    'SELECTED_ACCOUNTS',
  ]),
  selectedAccounts: z.array(accountCodeDeprecated),
  statementPackageNumber: z.number().int(),
})
export type DefaultStatementPackageCreateUpdateRequestForm = Omit<
  StatementPackageCreateUpdateRequestForm,
  | 'accountApplicationId'
  | 'addressAccountApplicationId'
  | 'addressApplicationId'
  | 'addressNumber'
  | 'packageDelivery'
  | 'packageType'
  | 'statementPackageNumber'
>
const defaultStatementPackageCreateUpdateRequest: DefaultStatementPackageCreateUpdateRequestForm =
  {
    accountNumber: '',
    addressAccountNumber: '',
    bankNumber: '',
    effectiveDate: '',
    selectedAccounts: [],
  }

const userFieldSelectionValidator = z.object({
  accountNumber: z.string(),
  applicationId: z.enum(['C', 'D']),
  applyToChildAccounts: z.boolean(),
  bankNumber: z.string(),
  booleanValue: z.boolean().nullable(),
  dropdownOptionCode: z.number().int().nullable(),
  effectiveDate: z.string().date(),
  expiry: z.string().date().nullable(),
  freeformValue: z.string().nullable(),
  isUnset: z.boolean(),
  userFieldCode: z.number().int(),
})
const userFieldSelectionTransformer = z.object({
  ...userFieldSelectionValidator.shape,
  booleanValue:
    userFieldSelectionValidator.shape.booleanValue.transform(nullToUndefined),
  dropdownOptionCode:
    userFieldSelectionValidator.shape.dropdownOptionCode.transform(
      nullToUndefined,
    ),
  expiry: userFieldSelectionValidator.shape.expiry.transform(nullToUndefined),
  freeformValue:
    userFieldSelectionValidator.shape.freeformValue.transform(nullToUndefined),
})
export type UserFieldSelectionForm = z.input<
  typeof userFieldSelectionTransformer
>
export type UserFieldSelection = z.infer<typeof userFieldSelectionTransformer>
const userFieldSelectionDeprecated = z.object({
  accountNumber: z.string(),
  applicationId: z.enum(['C', 'D']),
  applyToChildAccounts: z.boolean(),
  bankNumber: z.string(),
  booleanValue: z.boolean().nullable(),
  dropdownOptionCode: z.number().int().nullable(),
  effectiveDate: z.string().date(),
  expiry: z.string().date().nullable(),
  freeformValue: z.string().nullable(),
  isUnset: z.boolean(),
  userFieldCode: z.number().int(),
})
export type DefaultUserFieldSelectionForm = Omit<
  UserFieldSelectionForm,
  'applicationId' | 'applyToChildAccounts' | 'isUnset' | 'userFieldCode'
>
const defaultUserFieldSelection: DefaultUserFieldSelectionForm = {
  accountNumber: '',
  bankNumber: '',
  booleanValue: null,
  dropdownOptionCode: null,
  effectiveDate: '',
  expiry: '',
  freeformValue: '',
}

const compositeAccountCreateRequestValidator = z.object({
  account: accountValidator,
  accountTypeOverride: accountTypeOverrideValidator.nullable(),
  addresses: z.array(addressValidator).nullable(),
  childAccountCodes: z.array(accountCodeValidator),
  keyChildAccountCode: accountCodeValidator.nullable(),
  statementPackages: z
    .array(statementPackageCreateUpdateRequestValidator)
    .nullable(),
  userFieldSelections: z.array(userFieldSelectionValidator).nullable(),
})
const compositeAccountCreateRequestTransformer = z.object({
  ...compositeAccountCreateRequestValidator.shape,
  account: accountTransformer,
  accountTypeOverride: accountTypeOverrideTransformer
    .nullable()
    .transform(nullToUndefined),
  addresses: z.array(addressTransformer).nullable().transform(nullToUndefined),
  childAccountCodes: z.array(accountCodeTransformer),
  keyChildAccountCode: accountCodeTransformer
    .nullable()
    .transform(nullToUndefined),
  statementPackages: z
    .array(statementPackageCreateUpdateRequestTransformer)
    .nullable()
    .transform(nullToUndefined),
  userFieldSelections: z
    .array(userFieldSelectionTransformer)
    .nullable()
    .transform(nullToUndefined),
})
export type CompositeAccountCreateRequestForm = z.input<
  typeof compositeAccountCreateRequestTransformer
>
export type CompositeAccountCreateRequest = z.infer<
  typeof compositeAccountCreateRequestTransformer
>
const compositeAccountCreateRequestDeprecated = z.object({
  account: accountDeprecated,
  accountTypeOverride: accountTypeOverrideDeprecated,
  addresses: z.array(addressDeprecated),
  childAccountCodes: z.array(accountCodeDeprecated),
  keyChildAccountCode: accountCodeDeprecated,
  statementPackages: z.array(statementPackageCreateUpdateRequestDeprecated),
  userFieldSelections: z.array(userFieldSelectionDeprecated),
})
export type DefaultCompositeAccountCreateRequestForm = Omit<
  CompositeAccountCreateRequestForm,
  'account' | 'accountTypeOverride' | 'keyChildAccountCode'
> & {
  account: DefaultAccountForm
  accountTypeOverride: DefaultAccountTypeOverrideForm
  keyChildAccountCode: DefaultAccountCodeForm
}

const defaultCompositeAccountCreateRequest: DefaultCompositeAccountCreateRequestForm =
  {
    account: defaultAccount,
    accountTypeOverride: defaultAccountTypeOverride,
    addresses: [],
    childAccountCodes: [],
    keyChildAccountCode: defaultAccountCode,
    statementPackages: [],
    userFieldSelections: [],
  }

const serviceValidator = z.object({
  code: z.string(),
  description: z.string().nullable(),
  domesticAfpCode: z.string().nullable(),
  effectiveDate: z.string().date(),
  globalAfpCode: z.string().nullable(),
  internalNote: z.string().nullable(),
  serviceType: z.enum([
    'VOLUME_BASED',
    'RECURRING',
    'BALANCE_BASED',
    'PRE_PRICED',
    'SERVICE_SET',
  ]),
})
const serviceTransformer = z.object({
  ...serviceValidator.shape,
  description: serviceValidator.shape.description.transform(nullToUndefined),
  domesticAfpCode:
    serviceValidator.shape.domesticAfpCode.transform(nullToUndefined),
  globalAfpCode:
    serviceValidator.shape.globalAfpCode.transform(nullToUndefined),
  internalNote: serviceValidator.shape.internalNote.transform(nullToUndefined),
})
export type ServiceForm = z.input<typeof serviceTransformer>
export type Service = z.infer<typeof serviceTransformer>
const serviceDeprecated = z.object({
  code: z.string(),
  description: z.string().nullable(),
  domesticAfpCode: z.string().nullable(),
  effectiveDate: z.string().date(),
  globalAfpCode: z.string().nullable(),
  internalNote: z.string().nullable(),
  serviceType: z.enum([
    'VOLUME_BASED',
    'RECURRING',
    'BALANCE_BASED',
    'PRE_PRICED',
    'SERVICE_SET',
  ]),
})
export type DefaultServiceForm = Omit<ServiceForm, 'serviceType'>
const defaultService: DefaultServiceForm = {
  code: '',
  description: '',
  domesticAfpCode: '',
  effectiveDate: '',
  globalAfpCode: '',
  internalNote: '',
}

const servicePriceValidator = z.object({
  applyServiceTo: z
    .enum(['SELECT_DEPOSIT_ACCOUNTS', 'ALL_DEPOSIT_ACCOUNTS'])
    .nullable(),
  balanceDivisor: z.number().int().nullable(),
  balanceType: z
    .enum([
      'AVERAGE_COLLECTED',
      'AVERAGE_LEDGER',
      'AVERAGE_NEGATIVE_COLLECTED',
      'AVERAGE_NEGATIVE_LEDGER',
      'AVERAGE_POSITIVE_COLLECTED',
      'AVERAGE_POSITIVE_LEDGER',
      'AVERAGE_UNCOLLECTED_FUNDS',
      'COMPENSATING_BALANCE',
      'END_OF_MONTH_LEDGER',
      'INVESTABLE_BALANCE',
      'AVERAGE_FLOAT',
      'AVERAGE_CLEARINGHOUSE_FLOAT',
      'REQUIRED_BALANCE',
    ])
    .nullable(),
  baseFee: z.string().pipe(z.coerce.number()).nullable(),
  basisDays: z.enum(['360', '365', '366']).nullable(),
  costType: z.enum(['NO_COST', 'UNIT_COST', 'FLAT_COST']).nullable(),
  costValue: z.string().pipe(z.coerce.number()).nullable(),
  currency: z.string().nullable(),
  cycleDefinitionCode: z.string().nullable(),
  disposition: z
    .enum(['ANALYSED', 'HARD_CHARGE', 'IMMEDIATE_CHARGE', 'WAIVED'])
    .nullable(),
  effectiveDate: z.string().date(),
  expirationDate: z.string().date().nullable(),
  includeReferenceInformationOnStatements: z.boolean().nullable(),
  indexAdjustment: z.string().pipe(z.coerce.number()).nullable(),
  indexMultiplier: z.string().pipe(z.coerce.number()).nullable(),
  indexRateCode: z.string().nullable(),
  lastFinalsDate: z.string().date().nullable(),
  maximumFee: z.string().pipe(z.coerce.number()).nullable(),
  minimumFee: z.string().pipe(z.coerce.number()).nullable(),
  priceType: z
    .enum([
      'NOT_PRICED',
      'UNIT_PRICED',
      'FLAT_FEE',
      'THRESHOLD_TIER',
      'PARTITIONED_TIER',
      'PERCENTAGE',
      'OUTSIDE_PRICE',
      'INDEXED',
    ])
    .nullable(),
  priceValue: z.string().pipe(z.coerce.number()).nullable(),
  pricingHierarchyEntryCode: z.string(),
  pricingHierarchyEntryType: z.enum(['STANDARD', 'PRICE_LIST', 'OVERRIDE']),
  serviceCode: z.string(),
  subjectToDiscountOrPremium: z.boolean().nullable(),
  tierMaxBalanceExclusive: z.string().pipe(z.coerce.number()).nullable(),
  tierMaxVolumeExclusive: z.number().int().nullable(),
  tierMinBalanceInclusive: z.string().pipe(z.coerce.number()).nullable(),
  tierMinVolumeInclusive: z.number().int().nullable(),
  tierNumber: z.number().int(),
  tierPriceType: z
    .enum(['UNIT_PRICED', 'FLAT_FEE', 'PERCENTAGE', 'INDEXED'])
    .nullable(),
  units: z.number().int().nullable(),
})
const servicePriceTransformer = z.object({
  ...servicePriceValidator.shape,
  applyServiceTo:
    servicePriceValidator.shape.applyServiceTo.transform(nullToUndefined),
  balanceDivisor:
    servicePriceValidator.shape.balanceDivisor.transform(nullToUndefined),
  balanceType:
    servicePriceValidator.shape.balanceType.transform(nullToUndefined),
  baseFee: servicePriceValidator.shape.baseFee.transform(nullToUndefined),
  basisDays: servicePriceValidator.shape.basisDays.transform(nullToUndefined),
  costType: servicePriceValidator.shape.costType.transform(nullToUndefined),
  costValue: servicePriceValidator.shape.costValue.transform(nullToUndefined),
  currency: servicePriceValidator.shape.currency.transform(nullToUndefined),
  cycleDefinitionCode:
    servicePriceValidator.shape.cycleDefinitionCode.transform(nullToUndefined),
  disposition:
    servicePriceValidator.shape.disposition.transform(nullToUndefined),
  expirationDate:
    servicePriceValidator.shape.expirationDate.transform(nullToUndefined),
  includeReferenceInformationOnStatements:
    servicePriceValidator.shape.includeReferenceInformationOnStatements.transform(
      nullToUndefined,
    ),
  indexAdjustment:
    servicePriceValidator.shape.indexAdjustment.transform(nullToUndefined),
  indexMultiplier:
    servicePriceValidator.shape.indexMultiplier.transform(nullToUndefined),
  indexRateCode:
    servicePriceValidator.shape.indexRateCode.transform(nullToUndefined),
  lastFinalsDate:
    servicePriceValidator.shape.lastFinalsDate.transform(nullToUndefined),
  maximumFee: servicePriceValidator.shape.maximumFee.transform(nullToUndefined),
  minimumFee: servicePriceValidator.shape.minimumFee.transform(nullToUndefined),
  priceType: servicePriceValidator.shape.priceType.transform(nullToUndefined),
  priceValue: servicePriceValidator.shape.priceValue.transform(nullToUndefined),
  subjectToDiscountOrPremium:
    servicePriceValidator.shape.subjectToDiscountOrPremium.transform(
      nullToUndefined,
    ),
  tierMaxBalanceExclusive:
    servicePriceValidator.shape.tierMaxBalanceExclusive.transform(
      nullToUndefined,
    ),
  tierMaxVolumeExclusive:
    servicePriceValidator.shape.tierMaxVolumeExclusive.transform(
      nullToUndefined,
    ),
  tierMinBalanceInclusive:
    servicePriceValidator.shape.tierMinBalanceInclusive.transform(
      nullToUndefined,
    ),
  tierMinVolumeInclusive:
    servicePriceValidator.shape.tierMinVolumeInclusive.transform(
      nullToUndefined,
    ),
  tierPriceType:
    servicePriceValidator.shape.tierPriceType.transform(nullToUndefined),
  units: servicePriceValidator.shape.units.transform(nullToUndefined),
})
export type ServicePriceForm = z.input<typeof servicePriceTransformer>
export type ServicePrice = z.infer<typeof servicePriceTransformer>
const servicePriceDeprecated = z.object({
  applyServiceTo: z
    .enum(['SELECT_DEPOSIT_ACCOUNTS', 'ALL_DEPOSIT_ACCOUNTS'])
    .nullable(),
  balanceDivisor: z.number().int().nullable(),
  balanceType: z
    .enum([
      'AVERAGE_COLLECTED',
      'AVERAGE_LEDGER',
      'AVERAGE_NEGATIVE_COLLECTED',
      'AVERAGE_NEGATIVE_LEDGER',
      'AVERAGE_POSITIVE_COLLECTED',
      'AVERAGE_POSITIVE_LEDGER',
      'AVERAGE_UNCOLLECTED_FUNDS',
      'COMPENSATING_BALANCE',
      'END_OF_MONTH_LEDGER',
      'INVESTABLE_BALANCE',
      'AVERAGE_FLOAT',
      'AVERAGE_CLEARINGHOUSE_FLOAT',
      'REQUIRED_BALANCE',
    ])
    .nullable(),
  baseFee: z.string().pipe(z.coerce.number()).nullable(),
  basisDays: z.enum(['360', '365', '366']).nullable(),
  costType: z.enum(['NO_COST', 'UNIT_COST', 'FLAT_COST']).nullable(),
  costValue: z.string().pipe(z.coerce.number()).nullable(),
  currency: z.string().nullable(),
  cycleDefinitionCode: z.string().nullable(),
  disposition: z
    .enum(['ANALYSED', 'HARD_CHARGE', 'IMMEDIATE_CHARGE', 'WAIVED'])
    .nullable(),
  effectiveDate: z.string().date(),
  expirationDate: z.string().date().nullable(),
  includeReferenceInformationOnStatements: z.boolean().nullable(),
  indexAdjustment: z.string().pipe(z.coerce.number()).nullable(),
  indexMultiplier: z.string().pipe(z.coerce.number()).nullable(),
  indexRateCode: z.string().nullable(),
  lastFinalsDate: z.string().date().nullable(),
  maximumFee: z.string().pipe(z.coerce.number()).nullable(),
  minimumFee: z.string().pipe(z.coerce.number()).nullable(),
  priceType: z
    .enum([
      'NOT_PRICED',
      'UNIT_PRICED',
      'FLAT_FEE',
      'THRESHOLD_TIER',
      'PARTITIONED_TIER',
      'PERCENTAGE',
      'OUTSIDE_PRICE',
      'INDEXED',
    ])
    .nullable(),
  priceValue: z.string().pipe(z.coerce.number()).nullable(),
  pricingHierarchyEntryCode: z.string(),
  pricingHierarchyEntryType: z.enum(['STANDARD', 'PRICE_LIST', 'OVERRIDE']),
  serviceCode: z.string(),
  subjectToDiscountOrPremium: z.boolean().nullable(),
  tierMaxBalanceExclusive: z.string().pipe(z.coerce.number()).nullable(),
  tierMaxVolumeExclusive: z.number().int().nullable(),
  tierMinBalanceInclusive: z.string().pipe(z.coerce.number()).nullable(),
  tierMinVolumeInclusive: z.number().int().nullable(),
  tierNumber: z.number().int(),
  tierPriceType: z
    .enum(['UNIT_PRICED', 'FLAT_FEE', 'PERCENTAGE', 'INDEXED'])
    .nullable(),
  units: z.number().int().nullable(),
})
export type DefaultServicePriceForm = Omit<
  ServicePriceForm,
  'pricingHierarchyEntryType' | 'tierNumber'
>
const defaultServicePrice: DefaultServicePriceForm = {
  applyServiceTo: null,
  balanceDivisor: null,
  balanceType: null,
  baseFee: null,
  basisDays: null,
  costType: null,
  costValue: null,
  currency: '',
  cycleDefinitionCode: '',
  disposition: null,
  effectiveDate: '',
  expirationDate: '',
  includeReferenceInformationOnStatements: null,
  indexAdjustment: null,
  indexMultiplier: null,
  indexRateCode: '',
  lastFinalsDate: '',
  maximumFee: null,
  minimumFee: null,
  priceType: null,
  priceValue: null,
  pricingHierarchyEntryCode: '',
  serviceCode: '',
  subjectToDiscountOrPremium: null,
  tierMaxBalanceExclusive: null,
  tierMaxVolumeExclusive: null,
  tierMinBalanceInclusive: null,
  tierMinVolumeInclusive: null,
  tierPriceType: null,
  units: null,
}

const createOrUpdateServiceValidator = z.object({
  service: serviceValidator,
  serviceCategoryCode: z.string().nullable(),
  serviceCodes: z.array(z.string()).nullable(),
  servicePrice: z.array(servicePriceValidator),
})
const createOrUpdateServiceTransformer = z.object({
  ...createOrUpdateServiceValidator.shape,
  service: serviceTransformer,
  serviceCategoryCode:
    createOrUpdateServiceValidator.shape.serviceCategoryCode.transform(
      nullToUndefined,
    ),
  serviceCodes:
    createOrUpdateServiceValidator.shape.serviceCodes.transform(
      nullToUndefined,
    ),
  servicePrice: z.array(servicePriceTransformer),
})
export type CreateOrUpdateServiceForm = z.input<
  typeof createOrUpdateServiceTransformer
>
export type CreateOrUpdateService = z.infer<
  typeof createOrUpdateServiceTransformer
>
const createOrUpdateServiceDeprecated = z.object({
  service: serviceDeprecated,
  serviceCategoryCode: z.string().nullable(),
  serviceCodes: z.array(z.string()),
  servicePrice: z.array(servicePriceDeprecated),
})
export type DefaultCreateOrUpdateServiceForm = Omit<
  CreateOrUpdateServiceForm,
  'service'
> & {
  service: DefaultServiceForm
}

const defaultCreateOrUpdateService: DefaultCreateOrUpdateServiceForm = {
  service: defaultService,
  serviceCategoryCode: '',
  serviceCodes: [],
  servicePrice: [],
}

const serviceCategoryValidator = z.object({
  code: z.string(),
  effectiveDate: z.string().date(),
  name: z.string(),
})
const serviceCategoryTransformer = serviceCategoryValidator
export type ServiceCategoryForm = z.input<typeof serviceCategoryTransformer>
export type ServiceCategory = z.infer<typeof serviceCategoryTransformer>
const serviceCategoryDeprecated = z.object({
  code: z.string(),
  effectiveDate: z.string().date(),
  name: z.string(),
})
const defaultServiceCategory: ServiceCategoryForm = {
  code: '',
  effectiveDate: '',
  name: '',
}

const createServiceCategoryValidator = z.object({
  parentServiceCategoryCode: z.string().nullable(),
  serviceCategory: serviceCategoryValidator,
})
const createServiceCategoryTransformer = z.object({
  ...createServiceCategoryValidator.shape,
  parentServiceCategoryCode:
    createServiceCategoryValidator.shape.parentServiceCategoryCode.transform(
      nullToUndefined,
    ),
  serviceCategory: serviceCategoryTransformer,
})
export type CreateServiceCategoryForm = z.input<
  typeof createServiceCategoryTransformer
>
export type CreateServiceCategory = z.infer<
  typeof createServiceCategoryTransformer
>
const createServiceCategoryDeprecated = z.object({
  parentServiceCategoryCode: z.string().nullable(),
  serviceCategory: serviceCategoryDeprecated,
})
const defaultCreateServiceCategory: CreateServiceCategoryForm = {
  parentServiceCategoryCode: '',
  serviceCategory: defaultServiceCategory,
}

const cycleDefinitionValidator = z.object({
  code: z.string(),
  cycleType: z.enum(['STATEMENT', 'SETTLEMENT']),
  description: z.string(),
  effectiveDate: z.string().date(),
  includedMonths: z
    .array(
      z.enum([
        'JANUARY',
        'FEBRUARY',
        'MARCH',
        'APRIL',
        'MAY',
        'JUNE',
        'JULY',
        'AUGUST',
        'SEPTEMBER',
        'OCTOBER',
        'NOVEMBER',
        'DECEMBER',
      ]),
    )
    .nullable(),
  isAprilSelected: z.boolean().nullable(),
  isAugustSelected: z.boolean().nullable(),
  isDecemberSelected: z.boolean().nullable(),
  isFebruarySelected: z.boolean().nullable(),
  isJanuarySelected: z.boolean().nullable(),
  isJulySelected: z.boolean().nullable(),
  isJuneSelected: z.boolean().nullable(),
  isMarchSelected: z.boolean().nullable(),
  isMaySelected: z.boolean().nullable(),
  isNovemberSelected: z.boolean().nullable(),
  isOctoberSelected: z.boolean().nullable(),
  isSeptemberSelected: z.boolean().nullable(),
})
const cycleDefinitionTransformer = z.object({
  ...cycleDefinitionValidator.shape,
  includedMonths:
    cycleDefinitionValidator.shape.includedMonths.transform(nullToUndefined),
  isAprilSelected:
    cycleDefinitionValidator.shape.isAprilSelected.transform(nullToUndefined),
  isAugustSelected:
    cycleDefinitionValidator.shape.isAugustSelected.transform(nullToUndefined),
  isDecemberSelected:
    cycleDefinitionValidator.shape.isDecemberSelected.transform(
      nullToUndefined,
    ),
  isFebruarySelected:
    cycleDefinitionValidator.shape.isFebruarySelected.transform(
      nullToUndefined,
    ),
  isJanuarySelected:
    cycleDefinitionValidator.shape.isJanuarySelected.transform(nullToUndefined),
  isJulySelected:
    cycleDefinitionValidator.shape.isJulySelected.transform(nullToUndefined),
  isJuneSelected:
    cycleDefinitionValidator.shape.isJuneSelected.transform(nullToUndefined),
  isMarchSelected:
    cycleDefinitionValidator.shape.isMarchSelected.transform(nullToUndefined),
  isMaySelected:
    cycleDefinitionValidator.shape.isMaySelected.transform(nullToUndefined),
  isNovemberSelected:
    cycleDefinitionValidator.shape.isNovemberSelected.transform(
      nullToUndefined,
    ),
  isOctoberSelected:
    cycleDefinitionValidator.shape.isOctoberSelected.transform(nullToUndefined),
  isSeptemberSelected:
    cycleDefinitionValidator.shape.isSeptemberSelected.transform(
      nullToUndefined,
    ),
})
export type CycleDefinitionForm = z.input<typeof cycleDefinitionTransformer>
export type CycleDefinition = z.infer<typeof cycleDefinitionTransformer>
const cycleDefinitionDeprecated = z.object({
  code: z.string(),
  cycleType: z.enum(['STATEMENT', 'SETTLEMENT']),
  description: z.string(),
  effectiveDate: z.string().date(),
  includedMonths: z.array(
    z.enum([
      'JANUARY',
      'FEBRUARY',
      'MARCH',
      'APRIL',
      'MAY',
      'JUNE',
      'JULY',
      'AUGUST',
      'SEPTEMBER',
      'OCTOBER',
      'NOVEMBER',
      'DECEMBER',
    ]),
  ),
  isAprilSelected: z.boolean().nullable(),
  isAugustSelected: z.boolean().nullable(),
  isDecemberSelected: z.boolean().nullable(),
  isFebruarySelected: z.boolean().nullable(),
  isJanuarySelected: z.boolean().nullable(),
  isJulySelected: z.boolean().nullable(),
  isJuneSelected: z.boolean().nullable(),
  isMarchSelected: z.boolean().nullable(),
  isMaySelected: z.boolean().nullable(),
  isNovemberSelected: z.boolean().nullable(),
  isOctoberSelected: z.boolean().nullable(),
  isSeptemberSelected: z.boolean().nullable(),
})
export type DefaultCycleDefinitionForm = Omit<CycleDefinitionForm, 'cycleType'>
const defaultCycleDefinition: DefaultCycleDefinitionForm = {
  code: '',
  description: '',
  effectiveDate: '',
  includedMonths: [],
  isAprilSelected: null,
  isAugustSelected: null,
  isDecemberSelected: null,
  isFebruarySelected: null,
  isJanuarySelected: null,
  isJulySelected: null,
  isJuneSelected: null,
  isMarchSelected: null,
  isMaySelected: null,
  isNovemberSelected: null,
  isOctoberSelected: null,
  isSeptemberSelected: null,
}

const dailyBalanceHistoryValidator = z.object({
  accountNumber: z.string(),
  applicationId: z.enum(['C', 'D']),
  bankNumber: z.string(),
  clearingHouseFundsFloat: z.string().pipe(z.coerce.number()),
  floatBalance: z.string().pipe(z.coerce.number()),
  isBusinessDay: z.boolean(),
  ledgerBalance: z.string().pipe(z.coerce.number()),
  processDate: z.string().date(),
  todaysAccruedInterest: z.string().pipe(z.coerce.number()),
  todaysPostedInterest: z.string().pipe(z.coerce.number()),
})
const dailyBalanceHistoryTransformer = dailyBalanceHistoryValidator
export type DailyBalanceHistoryForm = z.input<
  typeof dailyBalanceHistoryTransformer
>
export type DailyBalanceHistory = z.infer<typeof dailyBalanceHistoryTransformer>
const dailyBalanceHistoryDeprecated = z.object({
  accountNumber: z.string(),
  applicationId: z.enum(['C', 'D']),
  bankNumber: z.string(),
  clearingHouseFundsFloat: z.string().pipe(z.coerce.number()),
  floatBalance: z.string().pipe(z.coerce.number()),
  isBusinessDay: z.boolean(),
  ledgerBalance: z.string().pipe(z.coerce.number()),
  processDate: z.string().date(),
  todaysAccruedInterest: z.string().pipe(z.coerce.number()),
  todaysPostedInterest: z.string().pipe(z.coerce.number()),
})
export type DefaultDailyBalanceHistoryForm = Omit<
  DailyBalanceHistoryForm,
  | 'applicationId'
  | 'clearingHouseFundsFloat'
  | 'floatBalance'
  | 'isBusinessDay'
  | 'ledgerBalance'
  | 'todaysAccruedInterest'
  | 'todaysPostedInterest'
>
const defaultDailyBalanceHistory: DefaultDailyBalanceHistoryForm = {
  accountNumber: '',
  bankNumber: '',
  processDate: '',
}

const demographicCriteriaIdValidator = z.object({
  codeKey: z.string().nullable(),
  criteriaCode: z.string(),
  keyType: z.enum(['USERFIELD', 'ACCOUNTFIELD']),
  priceListCode: z.string(),
  stringKey: z.string().nullable(),
  valueCode: z.string(),
})
const demographicCriteriaIdTransformer = z.object({
  ...demographicCriteriaIdValidator.shape,
  codeKey:
    demographicCriteriaIdValidator.shape.codeKey.transform(nullToUndefined),
  stringKey:
    demographicCriteriaIdValidator.shape.stringKey.transform(nullToUndefined),
})
export type DemographicCriteriaIdForm = z.input<
  typeof demographicCriteriaIdTransformer
>
export type DemographicCriteriaId = z.infer<
  typeof demographicCriteriaIdTransformer
>
const demographicCriteriaIdDeprecated = z.object({
  codeKey: z.string().nullable(),
  criteriaCode: z.string(),
  keyType: z.enum(['USERFIELD', 'ACCOUNTFIELD']),
  priceListCode: z.string(),
  stringKey: z.string().nullable(),
  valueCode: z.string(),
})
export type DefaultDemographicCriteriaIdForm = Omit<
  DemographicCriteriaIdForm,
  'keyType'
>
const defaultDemographicCriteriaId: DefaultDemographicCriteriaIdForm = {
  codeKey: '',
  criteriaCode: '',
  priceListCode: '',
  stringKey: '',
  valueCode: '',
}

const demographicCriteriaValidator = z.object({
  booleanValue: z.boolean().nullable(),
  code: z.string(),
  codeKey: z.string().nullable(),
  codeValue: z.string().nullable(),
  criteriaCode: z.string(),
  demographicCriteriaId: demographicCriteriaIdValidator,
  effectiveDate: z.string().date(),
  keyType: z.enum(['USERFIELD', 'ACCOUNTFIELD']),
  priceListCode: z.string(),
  stringKey: z.string().nullable(),
  stringValue: z.string().nullable(),
  valueCode: z.string(),
})
const demographicCriteriaTransformer = z.object({
  ...demographicCriteriaValidator.shape,
  booleanValue:
    demographicCriteriaValidator.shape.booleanValue.transform(nullToUndefined),
  codeKey:
    demographicCriteriaValidator.shape.codeKey.transform(nullToUndefined),
  codeValue:
    demographicCriteriaValidator.shape.codeValue.transform(nullToUndefined),
  demographicCriteriaId: demographicCriteriaIdTransformer,
  stringKey:
    demographicCriteriaValidator.shape.stringKey.transform(nullToUndefined),
  stringValue:
    demographicCriteriaValidator.shape.stringValue.transform(nullToUndefined),
})
export type DemographicCriteriaForm = z.input<
  typeof demographicCriteriaTransformer
>
export type DemographicCriteria = z.infer<typeof demographicCriteriaTransformer>
const demographicCriteriaDeprecated = z.object({
  booleanValue: z.boolean().nullable(),
  code: z.string(),
  codeKey: z.string().nullable(),
  codeValue: z.string().nullable(),
  criteriaCode: z.string(),
  demographicCriteriaId: demographicCriteriaIdDeprecated,
  effectiveDate: z.string().date(),
  keyType: z.enum(['USERFIELD', 'ACCOUNTFIELD']),
  priceListCode: z.string(),
  stringKey: z.string().nullable(),
  stringValue: z.string().nullable(),
  valueCode: z.string(),
})
export type DefaultDemographicCriteriaForm = Omit<
  DemographicCriteriaForm,
  'keyType' | 'demographicCriteriaId'
> & {
  demographicCriteriaId: DefaultDemographicCriteriaIdForm
}

const defaultDemographicCriteria: DefaultDemographicCriteriaForm = {
  booleanValue: null,
  code: '',
  codeKey: '',
  codeValue: '',
  criteriaCode: '',
  demographicCriteriaId: defaultDemographicCriteriaId,
  effectiveDate: '',
  priceListCode: '',
  stringKey: '',
  stringValue: '',
  valueCode: '',
}

const demographicPriceListValidator = z.object({
  code: z.string(),
  currency: z.string(),
  effectiveDate: z.string().date(),
  isLeadPriceList: z.boolean(),
  name: z.string().nullable(),
})
const demographicPriceListTransformer = z.object({
  ...demographicPriceListValidator.shape,
  name: demographicPriceListValidator.shape.name.transform(nullToUndefined),
})
export type DemographicPriceListForm = z.input<
  typeof demographicPriceListTransformer
>
export type DemographicPriceList = z.infer<
  typeof demographicPriceListTransformer
>
const demographicPriceListDeprecated = z.object({
  code: z.string(),
  currency: z.string(),
  effectiveDate: z.string().date(),
  isLeadPriceList: z.boolean(),
  name: z.string().nullable(),
})
export type DefaultDemographicPriceListForm = Omit<
  DemographicPriceListForm,
  'isLeadPriceList'
>
const defaultDemographicPriceList: DefaultDemographicPriceListForm = {
  code: '',
  currency: '',
  effectiveDate: '',
  name: '',
}

const demographicPriceListRankingValidator = z.object({
  effectiveDate: z.string().date(),
  ranking: z.string(),
})
const demographicPriceListRankingTransformer =
  demographicPriceListRankingValidator
export type DemographicPriceListRankingForm = z.input<
  typeof demographicPriceListRankingTransformer
>
export type DemographicPriceListRanking = z.infer<
  typeof demographicPriceListRankingTransformer
>
const demographicPriceListRankingDeprecated = z.object({
  effectiveDate: z.string().date(),
  ranking: z.string(),
})
const defaultDemographicPriceListRanking: DemographicPriceListRankingForm = {
  effectiveDate: '',
  ranking: '',
}

const demographicPriceListWithCountValidator = z.object({
  code: z.string(),
  currency: z.string(),
  effectiveDate: z.string().date(),
  isLeadPriceList: z.boolean(),
  name: z.string().nullable(),
  serviceCount: z.number().int(),
})
const demographicPriceListWithCountTransformer = z.object({
  ...demographicPriceListWithCountValidator.shape,
  name: demographicPriceListWithCountValidator.shape.name.transform(
    nullToUndefined,
  ),
})
export type DemographicPriceListWithCountForm = z.input<
  typeof demographicPriceListWithCountTransformer
>
export type DemographicPriceListWithCount = z.infer<
  typeof demographicPriceListWithCountTransformer
>
const demographicPriceListWithCountDeprecated = z.object({
  code: z.string(),
  currency: z.string(),
  effectiveDate: z.string().date(),
  isLeadPriceList: z.boolean(),
  name: z.string().nullable(),
  serviceCount: z.number().int(),
})
export type DefaultDemographicPriceListWithCountForm = Omit<
  DemographicPriceListWithCountForm,
  'isLeadPriceList' | 'serviceCount'
>
const defaultDemographicPriceListWithCount: DefaultDemographicPriceListWithCountForm =
  {
    code: '',
    currency: '',
    effectiveDate: '',
    name: '',
  }

const demographicUpdateRecordEntityValidator = z.object({
  aasAccountType: z.string().nullable(),
  accountNumber: z.string().nullable(),
  accountStatus: z.string().nullable(),
  alternatePrimaryOfficer: z.string().nullable(),
  alternateSecondaryOfficer: z.string().nullable(),
  alternateTreasuryOfficer: z.string().nullable(),
  applicationId: z.string().nullable(),
  bankNumber: z.string().nullable(),
  blendOverrideIndicator: z.string().nullable(),
  branchNumber: z.string().nullable(),
  closeDate: z.string().date().nullable(),
  control: z.string().nullable(),
  costCenter: z.string().nullable(),
  cpsAasIndicator: z.string().nullable(),
  createdAt: z.string().nullable(),
  currencyCode: z.string().nullable(),
  customerPricingIndicator: z.string().nullable(),
  dailyOffsetIndicator: z.string().nullable(),
  depositAccountType: z.string().nullable(),
  depositCategory: z.string().nullable(),
  effectiveDate: z.string().date().nullable(),
  fileType: z
    .enum([
      'CORE_UNPACKED',
      'IBS_CORE_PACKED',
      'ARPPA',
      'ACH_TRACKER',
      'CASH_MANAGER',
      'D1B',
      'DIRECT_LINK_MERCHANT',
      'EWIRE',
    ])
    .nullable(),
  id: z.string().nullable(),
  inboundRecordStatus: z
    .enum([
      'PARSED',
      'CONVERSION_ERROR',
      'VALIDATION_ERROR',
      'PERSIST_ERROR',
      'PROCESSED_CREATE',
      'PROCESSED_UPDATE',
      'PROCESSED_SKIP',
    ])
    .nullable(),
  openDate: z.string().date().nullable(),
  postingDate: z.string().date().nullable(),
  primaryOfficer: z.string().nullable(),
  processingIndicator: z.string().nullable(),
  secondaryOfficer: z.string().nullable(),
  settleAtAccountIndicator: z.string().nullable(),
  shortName: z.string().nullable(),
  source: z.string().nullable(),
  sourceFilePath: z.string().nullable(),
  treasuryOfficer: z.string().nullable(),
  updatedAt: z.string().nullable(),
})
const demographicUpdateRecordEntityTransformer = z.object({
  ...demographicUpdateRecordEntityValidator.shape,
  aasAccountType:
    demographicUpdateRecordEntityValidator.shape.aasAccountType.transform(
      nullToUndefined,
    ),
  accountNumber:
    demographicUpdateRecordEntityValidator.shape.accountNumber.transform(
      nullToUndefined,
    ),
  accountStatus:
    demographicUpdateRecordEntityValidator.shape.accountStatus.transform(
      nullToUndefined,
    ),
  alternatePrimaryOfficer:
    demographicUpdateRecordEntityValidator.shape.alternatePrimaryOfficer.transform(
      nullToUndefined,
    ),
  alternateSecondaryOfficer:
    demographicUpdateRecordEntityValidator.shape.alternateSecondaryOfficer.transform(
      nullToUndefined,
    ),
  alternateTreasuryOfficer:
    demographicUpdateRecordEntityValidator.shape.alternateTreasuryOfficer.transform(
      nullToUndefined,
    ),
  applicationId:
    demographicUpdateRecordEntityValidator.shape.applicationId.transform(
      nullToUndefined,
    ),
  bankNumber:
    demographicUpdateRecordEntityValidator.shape.bankNumber.transform(
      nullToUndefined,
    ),
  blendOverrideIndicator:
    demographicUpdateRecordEntityValidator.shape.blendOverrideIndicator.transform(
      nullToUndefined,
    ),
  branchNumber:
    demographicUpdateRecordEntityValidator.shape.branchNumber.transform(
      nullToUndefined,
    ),
  closeDate:
    demographicUpdateRecordEntityValidator.shape.closeDate.transform(
      nullToUndefined,
    ),
  control:
    demographicUpdateRecordEntityValidator.shape.control.transform(
      nullToUndefined,
    ),
  costCenter:
    demographicUpdateRecordEntityValidator.shape.costCenter.transform(
      nullToUndefined,
    ),
  cpsAasIndicator:
    demographicUpdateRecordEntityValidator.shape.cpsAasIndicator.transform(
      nullToUndefined,
    ),
  createdAt:
    demographicUpdateRecordEntityValidator.shape.createdAt.transform(
      nullToUndefined,
    ),
  currencyCode:
    demographicUpdateRecordEntityValidator.shape.currencyCode.transform(
      nullToUndefined,
    ),
  customerPricingIndicator:
    demographicUpdateRecordEntityValidator.shape.customerPricingIndicator.transform(
      nullToUndefined,
    ),
  dailyOffsetIndicator:
    demographicUpdateRecordEntityValidator.shape.dailyOffsetIndicator.transform(
      nullToUndefined,
    ),
  depositAccountType:
    demographicUpdateRecordEntityValidator.shape.depositAccountType.transform(
      nullToUndefined,
    ),
  depositCategory:
    demographicUpdateRecordEntityValidator.shape.depositCategory.transform(
      nullToUndefined,
    ),
  effectiveDate:
    demographicUpdateRecordEntityValidator.shape.effectiveDate.transform(
      nullToUndefined,
    ),
  fileType:
    demographicUpdateRecordEntityValidator.shape.fileType.transform(
      nullToUndefined,
    ),
  id: demographicUpdateRecordEntityValidator.shape.id.transform(
    nullToUndefined,
  ),
  inboundRecordStatus:
    demographicUpdateRecordEntityValidator.shape.inboundRecordStatus.transform(
      nullToUndefined,
    ),
  openDate:
    demographicUpdateRecordEntityValidator.shape.openDate.transform(
      nullToUndefined,
    ),
  postingDate:
    demographicUpdateRecordEntityValidator.shape.postingDate.transform(
      nullToUndefined,
    ),
  primaryOfficer:
    demographicUpdateRecordEntityValidator.shape.primaryOfficer.transform(
      nullToUndefined,
    ),
  processingIndicator:
    demographicUpdateRecordEntityValidator.shape.processingIndicator.transform(
      nullToUndefined,
    ),
  secondaryOfficer:
    demographicUpdateRecordEntityValidator.shape.secondaryOfficer.transform(
      nullToUndefined,
    ),
  settleAtAccountIndicator:
    demographicUpdateRecordEntityValidator.shape.settleAtAccountIndicator.transform(
      nullToUndefined,
    ),
  shortName:
    demographicUpdateRecordEntityValidator.shape.shortName.transform(
      nullToUndefined,
    ),
  source:
    demographicUpdateRecordEntityValidator.shape.source.transform(
      nullToUndefined,
    ),
  sourceFilePath:
    demographicUpdateRecordEntityValidator.shape.sourceFilePath.transform(
      nullToUndefined,
    ),
  treasuryOfficer:
    demographicUpdateRecordEntityValidator.shape.treasuryOfficer.transform(
      nullToUndefined,
    ),
  updatedAt:
    demographicUpdateRecordEntityValidator.shape.updatedAt.transform(
      nullToUndefined,
    ),
})
export type DemographicUpdateRecordEntityForm = z.input<
  typeof demographicUpdateRecordEntityTransformer
>
export type DemographicUpdateRecordEntity = z.infer<
  typeof demographicUpdateRecordEntityTransformer
>
const demographicUpdateRecordEntityDeprecated = z.object({
  aasAccountType: z.string().nullable(),
  accountNumber: z.string().nullable(),
  accountStatus: z.string().nullable(),
  alternatePrimaryOfficer: z.string().nullable(),
  alternateSecondaryOfficer: z.string().nullable(),
  alternateTreasuryOfficer: z.string().nullable(),
  applicationId: z.string().nullable(),
  bankNumber: z.string().nullable(),
  blendOverrideIndicator: z.string().nullable(),
  branchNumber: z.string().nullable(),
  closeDate: z.string().date().nullable(),
  control: z.string().nullable(),
  costCenter: z.string().nullable(),
  cpsAasIndicator: z.string().nullable(),
  createdAt: z.string().nullable(),
  currencyCode: z.string().nullable(),
  customerPricingIndicator: z.string().nullable(),
  dailyOffsetIndicator: z.string().nullable(),
  depositAccountType: z.string().nullable(),
  depositCategory: z.string().nullable(),
  effectiveDate: z.string().date().nullable(),
  fileType: z
    .enum([
      'CORE_UNPACKED',
      'IBS_CORE_PACKED',
      'ARPPA',
      'ACH_TRACKER',
      'CASH_MANAGER',
      'D1B',
      'DIRECT_LINK_MERCHANT',
      'EWIRE',
    ])
    .nullable(),
  id: z.string().nullable(),
  inboundRecordStatus: z
    .enum([
      'PARSED',
      'CONVERSION_ERROR',
      'VALIDATION_ERROR',
      'PERSIST_ERROR',
      'PROCESSED_CREATE',
      'PROCESSED_UPDATE',
      'PROCESSED_SKIP',
    ])
    .nullable(),
  openDate: z.string().date().nullable(),
  postingDate: z.string().date().nullable(),
  primaryOfficer: z.string().nullable(),
  processingIndicator: z.string().nullable(),
  secondaryOfficer: z.string().nullable(),
  settleAtAccountIndicator: z.string().nullable(),
  shortName: z.string().nullable(),
  source: z.string().nullable(),
  sourceFilePath: z.string().nullable(),
  treasuryOfficer: z.string().nullable(),
  updatedAt: z.string().nullable(),
})
const defaultDemographicUpdateRecordEntity: DemographicUpdateRecordEntityForm =
  {
    aasAccountType: '',
    accountNumber: '',
    accountStatus: '',
    alternatePrimaryOfficer: '',
    alternateSecondaryOfficer: '',
    alternateTreasuryOfficer: '',
    applicationId: '',
    bankNumber: '',
    blendOverrideIndicator: '',
    branchNumber: '',
    closeDate: '',
    control: '',
    costCenter: '',
    cpsAasIndicator: '',
    createdAt: '',
    currencyCode: '',
    customerPricingIndicator: '',
    dailyOffsetIndicator: '',
    depositAccountType: '',
    depositCategory: '',
    effectiveDate: '',
    fileType: null,
    id: '',
    inboundRecordStatus: null,
    openDate: '',
    postingDate: '',
    primaryOfficer: '',
    processingIndicator: '',
    secondaryOfficer: '',
    settleAtAccountIndicator: '',
    shortName: '',
    source: '',
    sourceFilePath: '',
    treasuryOfficer: '',
    updatedAt: '',
  }

const earningsCreditDefinitionValidator = z.object({
  balanceTiers: z.array(balanceTierValidator),
  baseBalanceType: z.enum([
    'AVERAGE_COLLECTED',
    'AVERAGE_LEDGER',
    'AVERAGE_NEGATIVE_COLLECTED',
    'AVERAGE_NEGATIVE_LEDGER',
    'AVERAGE_POSITIVE_COLLECTED',
    'AVERAGE_POSITIVE_LEDGER',
    'AVERAGE_UNCOLLECTED_FUNDS',
    'COMPENSATING_BALANCE',
    'END_OF_MONTH_LEDGER',
    'INVESTABLE_BALANCE',
    'AVERAGE_FLOAT',
    'AVERAGE_CLEARINGHOUSE_FLOAT',
    'REQUIRED_BALANCE',
  ]),
  code: z.string(),
  description: z.string(),
  effectiveDate: z.string().date(),
  indexRateCode: z.string().nullable(),
  maxRateInclusive: z.string().pipe(z.coerce.number()).nullable(),
  minRateInclusive: z.string().pipe(z.coerce.number()).nullable(),
  rateSource: z.enum(['INDEX_RATE', 'MANUAL']),
  tierMethod: z.enum(['THRESHOLD', 'PARTITIONED']),
})
const earningsCreditDefinitionTransformer = z.object({
  ...earningsCreditDefinitionValidator.shape,
  balanceTiers: z.array(balanceTierTransformer),
  indexRateCode:
    earningsCreditDefinitionValidator.shape.indexRateCode.transform(
      nullToUndefined,
    ),
  maxRateInclusive:
    earningsCreditDefinitionValidator.shape.maxRateInclusive.transform(
      nullToUndefined,
    ),
  minRateInclusive:
    earningsCreditDefinitionValidator.shape.minRateInclusive.transform(
      nullToUndefined,
    ),
})
export type EarningsCreditDefinitionForm = z.input<
  typeof earningsCreditDefinitionTransformer
>
export type EarningsCreditDefinition = z.infer<
  typeof earningsCreditDefinitionTransformer
>
const earningsCreditDefinitionDeprecated = z.object({
  balanceTiers: z.array(balanceTierDeprecated),
  baseBalanceType: z.enum([
    'AVERAGE_COLLECTED',
    'AVERAGE_LEDGER',
    'AVERAGE_NEGATIVE_COLLECTED',
    'AVERAGE_NEGATIVE_LEDGER',
    'AVERAGE_POSITIVE_COLLECTED',
    'AVERAGE_POSITIVE_LEDGER',
    'AVERAGE_UNCOLLECTED_FUNDS',
    'COMPENSATING_BALANCE',
    'END_OF_MONTH_LEDGER',
    'INVESTABLE_BALANCE',
    'AVERAGE_FLOAT',
    'AVERAGE_CLEARINGHOUSE_FLOAT',
    'REQUIRED_BALANCE',
  ]),
  code: z.string(),
  description: z.string(),
  effectiveDate: z.string().date(),
  indexRateCode: z.string().nullable(),
  maxRateInclusive: z.string().pipe(z.coerce.number()).nullable(),
  minRateInclusive: z.string().pipe(z.coerce.number()).nullable(),
  rateSource: z.enum(['INDEX_RATE', 'MANUAL']),
  tierMethod: z.enum(['THRESHOLD', 'PARTITIONED']),
})
export type DefaultEarningsCreditDefinitionForm = Omit<
  EarningsCreditDefinitionForm,
  'baseBalanceType' | 'rateSource' | 'tierMethod'
>
const defaultEarningsCreditDefinition: DefaultEarningsCreditDefinitionForm = {
  balanceTiers: [],
  code: '',
  description: '',
  effectiveDate: '',
  indexRateCode: '',
  maxRateInclusive: null,
  minRateInclusive: null,
}

const effectiveDateRequestBodyValidator = z.object({
  effectiveDate: z.string().date(),
})
const effectiveDateRequestBodyTransformer = effectiveDateRequestBodyValidator
export type EffectiveDateRequestBodyForm = z.input<
  typeof effectiveDateRequestBodyTransformer
>
export type EffectiveDateRequestBody = z.infer<
  typeof effectiveDateRequestBodyTransformer
>
const effectiveDateRequestBodyDeprecated = z.object({
  effectiveDate: z.string().date(),
})
const defaultEffectiveDateRequestBody: EffectiveDateRequestBodyForm = {
  effectiveDate: '',
}

const getDailyBalanceHistoryRequestValidator = z.object({
  bankNumber: z.string(),
  endDateExclusive: z.string().date(),
  startDateInclusive: z.string().date(),
})
const getDailyBalanceHistoryRequestTransformer =
  getDailyBalanceHistoryRequestValidator
export type GetDailyBalanceHistoryRequestForm = z.input<
  typeof getDailyBalanceHistoryRequestTransformer
>
export type GetDailyBalanceHistoryRequest = z.infer<
  typeof getDailyBalanceHistoryRequestTransformer
>
const getDailyBalanceHistoryRequestDeprecated = z.object({
  bankNumber: z.string(),
  endDateExclusive: z.string().date(),
  startDateInclusive: z.string().date(),
})
const defaultGetDailyBalanceHistoryRequest: GetDailyBalanceHistoryRequestForm =
  {
    bankNumber: '',
    endDateExclusive: '',
    startDateInclusive: '',
  }

const hydratedAccountWithKeyAccountMappingValidator = z.object({
  child: accountWithKeyValidator,
  parent: accountWithKeyValidator.nullable(),
})
const hydratedAccountWithKeyAccountMappingTransformer = z.object({
  ...hydratedAccountWithKeyAccountMappingValidator.shape,
  child: accountWithKeyTransformer,
  parent: accountWithKeyTransformer.nullable().transform(nullToUndefined),
})
export type HydratedAccountWithKeyAccountMappingForm = z.input<
  typeof hydratedAccountWithKeyAccountMappingTransformer
>
export type HydratedAccountWithKeyAccountMapping = z.infer<
  typeof hydratedAccountWithKeyAccountMappingTransformer
>
const hydratedAccountWithKeyAccountMappingDeprecated = z.object({
  child: accountWithKeyDeprecated,
  parent: accountWithKeyDeprecated,
})
export type DefaultHydratedAccountWithKeyAccountMappingForm = Omit<
  HydratedAccountWithKeyAccountMappingForm,
  'child' | 'parent'
> & {
  child: DefaultAccountWithKeyForm
  parent: DefaultAccountWithKeyForm
}

const defaultHydratedAccountWithKeyAccountMapping: DefaultHydratedAccountWithKeyAccountMappingForm =
  {
    child: defaultAccountWithKey,
    parent: defaultAccountWithKey,
  }

const hydratedCategoryToCategoryMappingValidator = z.object({
  child: serviceCategoryValidator,
  parent: serviceCategoryValidator.nullable(),
})
const hydratedCategoryToCategoryMappingTransformer = z.object({
  ...hydratedCategoryToCategoryMappingValidator.shape,
  child: serviceCategoryTransformer,
  parent: serviceCategoryTransformer.nullable().transform(nullToUndefined),
})
export type HydratedCategoryToCategoryMappingForm = z.input<
  typeof hydratedCategoryToCategoryMappingTransformer
>
export type HydratedCategoryToCategoryMapping = z.infer<
  typeof hydratedCategoryToCategoryMappingTransformer
>
const hydratedCategoryToCategoryMappingDeprecated = z.object({
  child: serviceCategoryDeprecated,
  parent: serviceCategoryDeprecated,
})
const defaultHydratedCategoryToCategoryMapping: HydratedCategoryToCategoryMappingForm =
  {
    child: defaultServiceCategory,
    parent: defaultServiceCategory,
  }

const hydratedDemographicPriceListValidator = z.object({
  demographicCriteria: z.array(demographicCriteriaValidator),
  demographicPriceList: demographicPriceListValidator,
  ranking: demographicPriceListRankingValidator,
  servicePricing: z.array(servicePriceValidator),
})
const hydratedDemographicPriceListTransformer = z.object({
  ...hydratedDemographicPriceListValidator.shape,
  demographicCriteria: z.array(demographicCriteriaTransformer),
  demographicPriceList: demographicPriceListTransformer,
  ranking: demographicPriceListRankingTransformer,
  servicePricing: z.array(servicePriceTransformer),
})
export type HydratedDemographicPriceListForm = z.input<
  typeof hydratedDemographicPriceListTransformer
>
export type HydratedDemographicPriceList = z.infer<
  typeof hydratedDemographicPriceListTransformer
>
const hydratedDemographicPriceListDeprecated = z.object({
  demographicCriteria: z.array(demographicCriteriaDeprecated),
  demographicPriceList: demographicPriceListDeprecated,
  ranking: demographicPriceListRankingDeprecated,
  servicePricing: z.array(servicePriceDeprecated),
})
export type DefaultHydratedDemographicPriceListForm = Omit<
  HydratedDemographicPriceListForm,
  'demographicPriceList'
> & {
  demographicPriceList: DefaultDemographicPriceListForm
}

const defaultHydratedDemographicPriceList: DefaultHydratedDemographicPriceListForm =
  {
    demographicCriteria: [],
    demographicPriceList: defaultDemographicPriceList,
    ranking: defaultDemographicPriceListRanking,
    servicePricing: [],
  }

const hydratedServiceToCategoryMappingValidator = z.object({
  child: serviceValidator,
  parent: serviceCategoryValidator.nullable(),
})
const hydratedServiceToCategoryMappingTransformer = z.object({
  ...hydratedServiceToCategoryMappingValidator.shape,
  child: serviceTransformer,
  parent: serviceCategoryTransformer.nullable().transform(nullToUndefined),
})
export type HydratedServiceToCategoryMappingForm = z.input<
  typeof hydratedServiceToCategoryMappingTransformer
>
export type HydratedServiceToCategoryMapping = z.infer<
  typeof hydratedServiceToCategoryMappingTransformer
>
const hydratedServiceToCategoryMappingDeprecated = z.object({
  child: serviceDeprecated,
  parent: serviceCategoryDeprecated,
})
export type DefaultHydratedServiceToCategoryMappingForm = Omit<
  HydratedServiceToCategoryMappingForm,
  'child'
> & {
  child: DefaultServiceForm
}

const defaultHydratedServiceToCategoryMapping: DefaultHydratedServiceToCategoryMappingForm =
  {
    child: defaultService,
    parent: defaultServiceCategory,
  }

const hydratedServiceCatalogMappingValidator = z.object({
  categoryToCategoryMappings: z.array(
    hydratedCategoryToCategoryMappingValidator,
  ),
  serviceToCategoryMappings: z.array(hydratedServiceToCategoryMappingValidator),
})
const hydratedServiceCatalogMappingTransformer = z.object({
  ...hydratedServiceCatalogMappingValidator.shape,
  categoryToCategoryMappings: z.array(
    hydratedCategoryToCategoryMappingTransformer,
  ),
  serviceToCategoryMappings: z.array(
    hydratedServiceToCategoryMappingTransformer,
  ),
})
export type HydratedServiceCatalogMappingForm = z.input<
  typeof hydratedServiceCatalogMappingTransformer
>
export type HydratedServiceCatalogMapping = z.infer<
  typeof hydratedServiceCatalogMappingTransformer
>
const hydratedServiceCatalogMappingDeprecated = z.object({
  categoryToCategoryMappings: z.array(
    hydratedCategoryToCategoryMappingDeprecated,
  ),
  serviceToCategoryMappings: z.array(
    hydratedServiceToCategoryMappingDeprecated,
  ),
})
const defaultHydratedServiceCatalogMapping: HydratedServiceCatalogMappingForm =
  {
    categoryToCategoryMappings: [],
    serviceToCategoryMappings: [],
  }

const indexRateValidator = z.object({
  code: z.string(),
  effectiveDate: z.string().date(),
  indexRate: z.string().pipe(z.coerce.number()),
  name: z.string(),
})
const indexRateTransformer = indexRateValidator
export type IndexRateForm = z.input<typeof indexRateTransformer>
export type IndexRate = z.infer<typeof indexRateTransformer>
const indexRateDeprecated = z.object({
  code: z.string(),
  effectiveDate: z.string().date(),
  indexRate: z.string().pipe(z.coerce.number()),
  name: z.string(),
})
export type DefaultIndexRateForm = Omit<IndexRateForm, 'indexRate'>
const defaultIndexRate: DefaultIndexRateForm = {
  code: '',
  effectiveDate: '',
  name: '',
}

const hydratedServicePriceValidator = z.object({
  cycleDefinition: cycleDefinitionValidator.nullable(),
  indexRate: indexRateValidator.nullable(),
  servicePrice: servicePriceValidator.nullable(),
})
const hydratedServicePriceTransformer = z.object({
  ...hydratedServicePriceValidator.shape,
  cycleDefinition: cycleDefinitionTransformer
    .nullable()
    .transform(nullToUndefined),
  indexRate: indexRateTransformer.nullable().transform(nullToUndefined),
  servicePrice: servicePriceTransformer.nullable().transform(nullToUndefined),
})
export type HydratedServicePriceForm = z.input<
  typeof hydratedServicePriceTransformer
>
export type HydratedServicePrice = z.infer<
  typeof hydratedServicePriceTransformer
>
const hydratedServicePriceDeprecated = z.object({
  cycleDefinition: cycleDefinitionDeprecated,
  indexRate: indexRateDeprecated,
  servicePrice: servicePriceDeprecated,
})
export type DefaultHydratedServicePriceForm = Omit<
  HydratedServicePriceForm,
  'cycleDefinition' | 'indexRate' | 'servicePrice'
> & {
  cycleDefinition: DefaultCycleDefinitionForm
  indexRate: DefaultIndexRateForm
  servicePrice: DefaultServicePriceForm
}

const defaultHydratedServicePrice: DefaultHydratedServicePriceForm = {
  cycleDefinition: defaultCycleDefinition,
  indexRate: defaultIndexRate,
  servicePrice: defaultServicePrice,
}

const statementPackageValidator = z.object({
  accountCode: accountCodeValidator,
  addressCode: addressCodeValidator,
  effectiveDate: z.string().date(),
  packageDelivery: z.enum([
    'ELECTRONIC',
    'ELECTRONIC_AND_PRINT',
    'PRINT',
    'NO_STATEMENT',
  ]),
  packageType: z.enum([
    'ALL_ACCOUNTS',
    'COMPOSITE_ACCOUNTS',
    'DEPOSIT_ACCOUNTS',
    'SELECTED_ACCOUNTS',
  ]),
  statementPackageNumber: z.number().int(),
})
const statementPackageTransformer = z.object({
  ...statementPackageValidator.shape,
  accountCode: accountCodeTransformer,
  addressCode: addressCodeTransformer,
})
export type StatementPackageForm = z.input<typeof statementPackageTransformer>
export type StatementPackage = z.infer<typeof statementPackageTransformer>
const statementPackageDeprecated = z.object({
  accountCode: accountCodeDeprecated,
  addressCode: addressCodeDeprecated,
  effectiveDate: z.string().date(),
  packageDelivery: z.enum([
    'ELECTRONIC',
    'ELECTRONIC_AND_PRINT',
    'PRINT',
    'NO_STATEMENT',
  ]),
  packageType: z.enum([
    'ALL_ACCOUNTS',
    'COMPOSITE_ACCOUNTS',
    'DEPOSIT_ACCOUNTS',
    'SELECTED_ACCOUNTS',
  ]),
  statementPackageNumber: z.number().int(),
})
export type DefaultStatementPackageForm = Omit<
  StatementPackageForm,
  | 'packageDelivery'
  | 'packageType'
  | 'statementPackageNumber'
  | 'accountCode'
  | 'addressCode'
> & {
  accountCode: DefaultAccountCodeForm
  addressCode: DefaultAddressCodeForm
}

const defaultStatementPackage: DefaultStatementPackageForm = {
  accountCode: defaultAccountCode,
  addressCode: defaultAddressCode,
  effectiveDate: '',
}

const hydratedStatementPackageValidator = z.object({
  address: addressValidator,
  selectedAccounts: z.array(accountCodeValidator),
  statementPackage: statementPackageValidator,
})
const hydratedStatementPackageTransformer = z.object({
  ...hydratedStatementPackageValidator.shape,
  address: addressTransformer,
  selectedAccounts: z.array(accountCodeTransformer),
  statementPackage: statementPackageTransformer,
})
export type HydratedStatementPackageForm = z.input<
  typeof hydratedStatementPackageTransformer
>
export type HydratedStatementPackage = z.infer<
  typeof hydratedStatementPackageTransformer
>
const hydratedStatementPackageDeprecated = z.object({
  address: addressDeprecated,
  selectedAccounts: z.array(accountCodeDeprecated),
  statementPackage: statementPackageDeprecated,
})
export type DefaultHydratedStatementPackageForm = Omit<
  HydratedStatementPackageForm,
  'address' | 'statementPackage'
> & {
  address: DefaultAddressForm
  statementPackage: DefaultStatementPackageForm
}

const defaultHydratedStatementPackage: DefaultHydratedStatementPackageForm = {
  address: defaultAddress,
  selectedAccounts: [],
  statementPackage: defaultStatementPackage,
}

const userFieldDropdownOptionCreateValidator = z.object({
  value: z.string(),
})
const userFieldDropdownOptionCreateTransformer =
  userFieldDropdownOptionCreateValidator
export type UserFieldDropdownOptionCreateForm = z.input<
  typeof userFieldDropdownOptionCreateTransformer
>
export type UserFieldDropdownOptionCreate = z.infer<
  typeof userFieldDropdownOptionCreateTransformer
>
const userFieldDropdownOptionCreateDeprecated = z.object({
  value: z.string(),
})
const defaultUserFieldDropdownOptionCreate: UserFieldDropdownOptionCreateForm =
  {
    value: '',
  }

const userFieldDropdownOptionUpdateValidator = z.object({
  code: z.number().int(),
  value: z.string(),
})
const userFieldDropdownOptionUpdateTransformer =
  userFieldDropdownOptionUpdateValidator
export type UserFieldDropdownOptionUpdateForm = z.input<
  typeof userFieldDropdownOptionUpdateTransformer
>
export type UserFieldDropdownOptionUpdate = z.infer<
  typeof userFieldDropdownOptionUpdateTransformer
>
const userFieldDropdownOptionUpdateDeprecated = z.object({
  code: z.number().int(),
  value: z.string(),
})
export type DefaultUserFieldDropdownOptionUpdateForm = Omit<
  UserFieldDropdownOptionUpdateForm,
  'code'
>
const defaultUserFieldDropdownOptionUpdate: DefaultUserFieldDropdownOptionUpdateForm =
  {
    value: '',
  }

const hydratedUserFieldConfigurationValidator = z.object({
  availableForPriceList: z.boolean(),
  code: z.number().int(),
  createdDate: z.string().date().nullable(),
  fieldType: z.enum(['FREEFORM', 'DROPDOWN', 'BOOLEAN']),
  lastUpdatedDate: z.string().date().nullable(),
  name: z.string(),
  newDropdownOptions: z
    .array(userFieldDropdownOptionCreateValidator)
    .nullable(),
  updatedDropdownOptions: z
    .array(userFieldDropdownOptionUpdateValidator)
    .nullable(),
})
const hydratedUserFieldConfigurationTransformer = z.object({
  ...hydratedUserFieldConfigurationValidator.shape,
  createdDate:
    hydratedUserFieldConfigurationValidator.shape.createdDate.transform(
      nullToUndefined,
    ),
  lastUpdatedDate:
    hydratedUserFieldConfigurationValidator.shape.lastUpdatedDate.transform(
      nullToUndefined,
    ),
  newDropdownOptions: z
    .array(userFieldDropdownOptionCreateTransformer)
    .nullable()
    .transform(nullToUndefined),
  updatedDropdownOptions: z
    .array(userFieldDropdownOptionUpdateTransformer)
    .nullable()
    .transform(nullToUndefined),
})
export type HydratedUserFieldConfigurationForm = z.input<
  typeof hydratedUserFieldConfigurationTransformer
>
export type HydratedUserFieldConfiguration = z.infer<
  typeof hydratedUserFieldConfigurationTransformer
>
const hydratedUserFieldConfigurationDeprecated = z.object({
  availableForPriceList: z.boolean(),
  code: z.number().int(),
  createdDate: z.string().date().nullable(),
  fieldType: z.enum(['FREEFORM', 'DROPDOWN', 'BOOLEAN']),
  lastUpdatedDate: z.string().date().nullable(),
  name: z.string(),
  newDropdownOptions: z.array(userFieldDropdownOptionCreateDeprecated),
  updatedDropdownOptions: z.array(userFieldDropdownOptionUpdateDeprecated),
})
export type DefaultHydratedUserFieldConfigurationForm = Omit<
  HydratedUserFieldConfigurationForm,
  'availableForPriceList' | 'code' | 'fieldType'
>
const defaultHydratedUserFieldConfiguration: DefaultHydratedUserFieldConfigurationForm =
  {
    createdDate: '',
    lastUpdatedDate: '',
    name: '',
    newDropdownOptions: [],
    updatedDropdownOptions: [],
  }

const hydratedUserFieldConfigurationCreateValidator = z.object({
  availableForPriceList: z.boolean(),
  fieldType: z.enum(['FREEFORM', 'DROPDOWN', 'BOOLEAN']),
  name: z.string(),
  newDropdownOptions: z
    .array(userFieldDropdownOptionCreateValidator)
    .nullable(),
})
const hydratedUserFieldConfigurationCreateTransformer = z.object({
  ...hydratedUserFieldConfigurationCreateValidator.shape,
  newDropdownOptions: z
    .array(userFieldDropdownOptionCreateTransformer)
    .nullable()
    .transform(nullToUndefined),
})
export type HydratedUserFieldConfigurationCreateForm = z.input<
  typeof hydratedUserFieldConfigurationCreateTransformer
>
export type HydratedUserFieldConfigurationCreate = z.infer<
  typeof hydratedUserFieldConfigurationCreateTransformer
>
const hydratedUserFieldConfigurationCreateDeprecated = z.object({
  availableForPriceList: z.boolean(),
  fieldType: z.enum(['FREEFORM', 'DROPDOWN', 'BOOLEAN']),
  name: z.string(),
  newDropdownOptions: z.array(userFieldDropdownOptionCreateDeprecated),
})
export type DefaultHydratedUserFieldConfigurationCreateForm = Omit<
  HydratedUserFieldConfigurationCreateForm,
  'availableForPriceList' | 'fieldType'
>
const defaultHydratedUserFieldConfigurationCreate: DefaultHydratedUserFieldConfigurationCreateForm =
  {
    name: '',
    newDropdownOptions: [],
  }

const hydratedUserFieldConfigurationUpdateValidator = z.object({
  availableForPriceList: z.boolean(),
  code: z.number().int(),
  fieldType: z.enum(['FREEFORM', 'DROPDOWN', 'BOOLEAN']),
  name: z.string(),
  newDropdownOptions: z
    .array(userFieldDropdownOptionCreateValidator)
    .nullable(),
  updatedDropdownOptions: z
    .array(userFieldDropdownOptionUpdateValidator)
    .nullable(),
})
const hydratedUserFieldConfigurationUpdateTransformer = z.object({
  ...hydratedUserFieldConfigurationUpdateValidator.shape,
  newDropdownOptions: z
    .array(userFieldDropdownOptionCreateTransformer)
    .nullable()
    .transform(nullToUndefined),
  updatedDropdownOptions: z
    .array(userFieldDropdownOptionUpdateTransformer)
    .nullable()
    .transform(nullToUndefined),
})
export type HydratedUserFieldConfigurationUpdateForm = z.input<
  typeof hydratedUserFieldConfigurationUpdateTransformer
>
export type HydratedUserFieldConfigurationUpdate = z.infer<
  typeof hydratedUserFieldConfigurationUpdateTransformer
>
const hydratedUserFieldConfigurationUpdateDeprecated = z.object({
  availableForPriceList: z.boolean(),
  code: z.number().int(),
  fieldType: z.enum(['FREEFORM', 'DROPDOWN', 'BOOLEAN']),
  name: z.string(),
  newDropdownOptions: z.array(userFieldDropdownOptionCreateDeprecated),
  updatedDropdownOptions: z.array(userFieldDropdownOptionUpdateDeprecated),
})
export type DefaultHydratedUserFieldConfigurationUpdateForm = Omit<
  HydratedUserFieldConfigurationUpdateForm,
  'availableForPriceList' | 'code' | 'fieldType'
>
const defaultHydratedUserFieldConfigurationUpdate: DefaultHydratedUserFieldConfigurationUpdateForm =
  {
    name: '',
    newDropdownOptions: [],
    updatedDropdownOptions: [],
  }

const investableBalanceDefinitionValidator = z.object({
  addOtherBalanceLabel: z.string().nullable(),
  baseBalanceType: z.enum([
    'AVERAGE_COLLECTED',
    'AVERAGE_LEDGER',
    'AVERAGE_NEGATIVE_COLLECTED',
    'AVERAGE_NEGATIVE_LEDGER',
    'AVERAGE_POSITIVE_COLLECTED',
    'AVERAGE_POSITIVE_LEDGER',
    'AVERAGE_UNCOLLECTED_FUNDS',
    'COMPENSATING_BALANCE',
    'END_OF_MONTH_LEDGER',
    'INVESTABLE_BALANCE',
    'AVERAGE_FLOAT',
    'AVERAGE_CLEARINGHOUSE_FLOAT',
    'REQUIRED_BALANCE',
  ]),
  code: z.string(),
  effectiveDate: z.string().date(),
  name: z.string(),
  subtractCompensatingBalance: z.boolean(),
  subtractFederalReserveRequirement: z.boolean(),
  subtractInterestPaidMonthToDate: z.boolean(),
  subtractOtherBalanceLabel: z.string().nullable(),
})
const investableBalanceDefinitionTransformer = z.object({
  ...investableBalanceDefinitionValidator.shape,
  addOtherBalanceLabel:
    investableBalanceDefinitionValidator.shape.addOtherBalanceLabel.transform(
      nullToUndefined,
    ),
  subtractOtherBalanceLabel:
    investableBalanceDefinitionValidator.shape.subtractOtherBalanceLabel.transform(
      nullToUndefined,
    ),
})
export type InvestableBalanceDefinitionForm = z.input<
  typeof investableBalanceDefinitionTransformer
>
export type InvestableBalanceDefinition = z.infer<
  typeof investableBalanceDefinitionTransformer
>
const investableBalanceDefinitionDeprecated = z.object({
  addOtherBalanceLabel: z.string().nullable(),
  baseBalanceType: z.enum([
    'AVERAGE_COLLECTED',
    'AVERAGE_LEDGER',
    'AVERAGE_NEGATIVE_COLLECTED',
    'AVERAGE_NEGATIVE_LEDGER',
    'AVERAGE_POSITIVE_COLLECTED',
    'AVERAGE_POSITIVE_LEDGER',
    'AVERAGE_UNCOLLECTED_FUNDS',
    'COMPENSATING_BALANCE',
    'END_OF_MONTH_LEDGER',
    'INVESTABLE_BALANCE',
    'AVERAGE_FLOAT',
    'AVERAGE_CLEARINGHOUSE_FLOAT',
    'REQUIRED_BALANCE',
  ]),
  code: z.string(),
  effectiveDate: z.string().date(),
  name: z.string(),
  subtractCompensatingBalance: z.boolean(),
  subtractFederalReserveRequirement: z.boolean(),
  subtractInterestPaidMonthToDate: z.boolean(),
  subtractOtherBalanceLabel: z.string().nullable(),
})
export type DefaultInvestableBalanceDefinitionForm = Omit<
  InvestableBalanceDefinitionForm,
  | 'baseBalanceType'
  | 'subtractCompensatingBalance'
  | 'subtractFederalReserveRequirement'
  | 'subtractInterestPaidMonthToDate'
>
const defaultInvestableBalanceDefinition: DefaultInvestableBalanceDefinitionForm =
  {
    addOtherBalanceLabel: '',
    code: '',
    effectiveDate: '',
    name: '',
    subtractOtherBalanceLabel: '',
  }

const keyAccountMappingValidator = z.object({
  accountCode: accountCodeValidator,
  childAccountCode: accountCodeValidator,
})
const keyAccountMappingTransformer = z.object({
  ...keyAccountMappingValidator.shape,
  accountCode: accountCodeTransformer,
  childAccountCode: accountCodeTransformer,
})
export type KeyAccountMappingForm = z.input<typeof keyAccountMappingTransformer>
export type KeyAccountMapping = z.infer<typeof keyAccountMappingTransformer>
const keyAccountMappingDeprecated = z.object({
  accountCode: accountCodeDeprecated,
  childAccountCode: accountCodeDeprecated,
})
export type DefaultKeyAccountMappingForm = Omit<
  KeyAccountMappingForm,
  'accountCode' | 'childAccountCode'
> & {
  accountCode: DefaultAccountCodeForm
  childAccountCode: DefaultAccountCodeForm
}

const defaultKeyAccountMapping: DefaultKeyAccountMappingForm = {
  accountCode: defaultAccountCode,
  childAccountCode: defaultAccountCode,
}

const nextAvailableAccountNumberResponseValidator = z.object({
  nextAvailableAccountNumber: z.string(),
})
const nextAvailableAccountNumberResponseTransformer =
  nextAvailableAccountNumberResponseValidator
export type NextAvailableAccountNumberResponseForm = z.input<
  typeof nextAvailableAccountNumberResponseTransformer
>
export type NextAvailableAccountNumberResponse = z.infer<
  typeof nextAvailableAccountNumberResponseTransformer
>
const nextAvailableAccountNumberResponseDeprecated = z.object({
  nextAvailableAccountNumber: z.string(),
})
const defaultNextAvailableAccountNumberResponse: NextAvailableAccountNumberResponseForm =
  {
    nextAvailableAccountNumber: '',
  }

const officerValidator = z.object({
  bankNumber: z.string(),
  code: z.string(),
  effectiveDate: z.string().date(),
  name: z.string(),
  phone: z.string(),
})
const officerTransformer = officerValidator
export type OfficerForm = z.input<typeof officerTransformer>
export type Officer = z.infer<typeof officerTransformer>
const officerDeprecated = z.object({
  bankNumber: z.string(),
  code: z.string(),
  effectiveDate: z.string().date(),
  name: z.string(),
  phone: z.string(),
})
const defaultOfficer: OfficerForm = {
  bankNumber: '',
  code: '',
  effectiveDate: '',
  name: '',
  phone: '',
}

const promotionalPriceListValidator = z.object({
  code: z.string(),
  currency: z.string(),
  effectiveDate: z.string().date(),
  expirationDate: z.string().date(),
  name: z.string().nullable(),
  term: z.number().int(),
})
const promotionalPriceListTransformer = z.object({
  ...promotionalPriceListValidator.shape,
  name: promotionalPriceListValidator.shape.name.transform(nullToUndefined),
})
export type PromotionalPriceListForm = z.input<
  typeof promotionalPriceListTransformer
>
export type PromotionalPriceList = z.infer<
  typeof promotionalPriceListTransformer
>
const promotionalPriceListDeprecated = z.object({
  code: z.string(),
  currency: z.string(),
  effectiveDate: z.string().date(),
  expirationDate: z.string().date(),
  name: z.string().nullable(),
  term: z.number().int(),
})
export type DefaultPromotionalPriceListForm = Omit<
  PromotionalPriceListForm,
  'term'
>
const defaultPromotionalPriceList: DefaultPromotionalPriceListForm = {
  code: '',
  currency: '',
  effectiveDate: '',
  expirationDate: '',
  name: '',
}

const reserveRequirementDefinitionValidator = z.object({
  baseBalanceType: z.enum(['AVERAGE_COLLECTED', 'AVERAGE_POSITIVE_COLLECTED']),
  calculationMethodType: z.enum(['INDEXED', 'PERCENTAGE']),
  ceiling: z.string().pipe(z.coerce.number()).nullable(),
  code: z.string(),
  effectiveDate: z.string().date(),
  floor: z.string().pipe(z.coerce.number()).nullable(),
  indexAdjustment: z.string().pipe(z.coerce.number()).nullable(),
  indexRateCode: z.string().nullable(),
  name: z.string(),
  reserveRate: z.string().pipe(z.coerce.number()).nullable(),
})
const reserveRequirementDefinitionTransformer = z.object({
  ...reserveRequirementDefinitionValidator.shape,
  ceiling:
    reserveRequirementDefinitionValidator.shape.ceiling.transform(
      nullToUndefined,
    ),
  floor:
    reserveRequirementDefinitionValidator.shape.floor.transform(
      nullToUndefined,
    ),
  indexAdjustment:
    reserveRequirementDefinitionValidator.shape.indexAdjustment.transform(
      nullToUndefined,
    ),
  indexRateCode:
    reserveRequirementDefinitionValidator.shape.indexRateCode.transform(
      nullToUndefined,
    ),
  reserveRate:
    reserveRequirementDefinitionValidator.shape.reserveRate.transform(
      nullToUndefined,
    ),
})
export type ReserveRequirementDefinitionForm = z.input<
  typeof reserveRequirementDefinitionTransformer
>
export type ReserveRequirementDefinition = z.infer<
  typeof reserveRequirementDefinitionTransformer
>
const reserveRequirementDefinitionDeprecated = z.object({
  baseBalanceType: z.enum(['AVERAGE_COLLECTED', 'AVERAGE_POSITIVE_COLLECTED']),
  calculationMethodType: z.enum(['INDEXED', 'PERCENTAGE']),
  ceiling: z.string().pipe(z.coerce.number()).nullable(),
  code: z.string(),
  effectiveDate: z.string().date(),
  floor: z.string().pipe(z.coerce.number()).nullable(),
  indexAdjustment: z.string().pipe(z.coerce.number()).nullable(),
  indexRateCode: z.string().nullable(),
  name: z.string(),
  reserveRate: z.string().pipe(z.coerce.number()).nullable(),
})
export type DefaultReserveRequirementDefinitionForm = Omit<
  ReserveRequirementDefinitionForm,
  'baseBalanceType' | 'calculationMethodType'
>
const defaultReserveRequirementDefinition: DefaultReserveRequirementDefinitionForm =
  {
    ceiling: null,
    code: '',
    effectiveDate: '',
    floor: null,
    indexAdjustment: null,
    indexRateCode: '',
    name: '',
    reserveRate: null,
  }

const serviceDetailsValidator = z.object({
  service: serviceValidator,
  serviceCategory: serviceCategoryValidator.nullable(),
  servicePrice: z.array(hydratedServicePriceValidator),
  servicesInServiceSet: z.array(serviceValidator).nullable(),
})
const serviceDetailsTransformer = z.object({
  ...serviceDetailsValidator.shape,
  service: serviceTransformer,
  serviceCategory: serviceCategoryTransformer
    .nullable()
    .transform(nullToUndefined),
  servicePrice: z.array(hydratedServicePriceTransformer),
  servicesInServiceSet: z
    .array(serviceTransformer)
    .nullable()
    .transform(nullToUndefined),
})
export type ServiceDetailsForm = z.input<typeof serviceDetailsTransformer>
export type ServiceDetails = z.infer<typeof serviceDetailsTransformer>
const serviceDetailsDeprecated = z.object({
  service: serviceDeprecated,
  serviceCategory: serviceCategoryDeprecated,
  servicePrice: z.array(hydratedServicePriceDeprecated),
  servicesInServiceSet: z.array(serviceDeprecated),
})
export type DefaultServiceDetailsForm = Omit<ServiceDetailsForm, 'service'> & {
  service: DefaultServiceForm
}

const defaultServiceDetails: DefaultServiceDetailsForm = {
  service: defaultService,
  serviceCategory: defaultServiceCategory,
  servicePrice: [],
  servicesInServiceSet: [],
}

const servicePriceRequestValidator = z.object({
  asOfDate: z.string().date(),
  pricingHierarchyEntryCode: z.string().nullable(),
  pricingHierarchyEntryType: z
    .enum(['STANDARD', 'PRICE_LIST', 'OVERRIDE'])
    .nullable(),
  serviceCodes: z.array(z.string()).nullable(),
})
const servicePriceRequestTransformer = z.object({
  ...servicePriceRequestValidator.shape,
  pricingHierarchyEntryCode:
    servicePriceRequestValidator.shape.pricingHierarchyEntryCode.transform(
      nullToUndefined,
    ),
  pricingHierarchyEntryType:
    servicePriceRequestValidator.shape.pricingHierarchyEntryType.transform(
      nullToUndefined,
    ),
  serviceCodes:
    servicePriceRequestValidator.shape.serviceCodes.transform(nullToUndefined),
})
export type ServicePriceRequestForm = z.input<
  typeof servicePriceRequestTransformer
>
export type ServicePriceRequest = z.infer<typeof servicePriceRequestTransformer>
const servicePriceRequestDeprecated = z.object({
  asOfDate: z.string().date(),
  pricingHierarchyEntryCode: z.string().nullable(),
  pricingHierarchyEntryType: z
    .enum(['STANDARD', 'PRICE_LIST', 'OVERRIDE'])
    .nullable(),
  serviceCodes: z.array(z.string()),
})
const defaultServicePriceRequest: ServicePriceRequestForm = {
  asOfDate: '',
  pricingHierarchyEntryCode: '',
  pricingHierarchyEntryType: null,
  serviceCodes: [],
}

const settlementProcessingOptionsResponseObjValidator = z.object({
  accountCode: accountCodeValidator,
  accountTypeOverride: accountTypeOverrideValidator.nullable(),
  chargeAccountCode: z.string().nullable(),
  effectiveDate: z.string().date(),
  overrideAsSettlementAccount: z.boolean().nullable(),
})
const settlementProcessingOptionsResponseObjTransformer = z.object({
  ...settlementProcessingOptionsResponseObjValidator.shape,
  accountCode: accountCodeTransformer,
  accountTypeOverride: accountTypeOverrideTransformer
    .nullable()
    .transform(nullToUndefined),
  chargeAccountCode:
    settlementProcessingOptionsResponseObjValidator.shape.chargeAccountCode.transform(
      nullToUndefined,
    ),
  overrideAsSettlementAccount:
    settlementProcessingOptionsResponseObjValidator.shape.overrideAsSettlementAccount.transform(
      nullToUndefined,
    ),
})
export type SettlementProcessingOptionsResponseObjForm = z.input<
  typeof settlementProcessingOptionsResponseObjTransformer
>
export type SettlementProcessingOptionsResponseObj = z.infer<
  typeof settlementProcessingOptionsResponseObjTransformer
>
const settlementProcessingOptionsResponseObjDeprecated = z.object({
  accountCode: accountCodeDeprecated,
  accountTypeOverride: accountTypeOverrideDeprecated,
  chargeAccountCode: z.string().nullable(),
  effectiveDate: z.string().date(),
  overrideAsSettlementAccount: z.boolean().nullable(),
})
export type DefaultSettlementProcessingOptionsResponseObjForm = Omit<
  SettlementProcessingOptionsResponseObjForm,
  'accountCode' | 'accountTypeOverride'
> & {
  accountCode: DefaultAccountCodeForm
  accountTypeOverride: DefaultAccountTypeOverrideForm
}

const defaultSettlementProcessingOptionsResponseObj: DefaultSettlementProcessingOptionsResponseObjForm =
  {
    accountCode: defaultAccountCode,
    accountTypeOverride: defaultAccountTypeOverride,
    chargeAccountCode: '',
    effectiveDate: '',
    overrideAsSettlementAccount: null,
  }

const statementFormatPlanValidator = z.object({
  afpServiceCode: z.boolean().nullable(),
  balanceSummary: z.boolean().nullable(),
  balanceSummaryLabel: z.string().nullable(),
  balanceSummaryLocation: z.string().nullable(),
  balanceSummarySize: z.enum(['FULL', 'HALF']).nullable(),
  boldServiceCategoryLabel: z.boolean().nullable(),
  boldServiceCategorySubtotalLabel: z.boolean().nullable(),
  code: z.string(),
  dailyBalanceSummary: z.enum(['NO', 'YES_ALL_ACCOUNTS', 'YES_COMPOSITE_ONLY']),
  dailyBalanceSummaryLabel: z.string().nullable(),
  dailyBalanceSummaryLocation: z.string().nullable(),
  description: z.string(),
  earningsCreditRate: z.enum(['EARNINGS_CREDIT_DEFINITION', 'CALCULATED_RATE']),
  effectiveDate: z.string().date(),
  enableServiceCategory: z.boolean().nullable(),
  headerLogoFileName: z.string(),
  historicalSummaryLabel: z.string().nullable(),
  historicalSummaryLocation: z.string().nullable(),
  historicalSummaryType: z.enum(['ROLLING_12_MONTHS', 'YTD', 'NO']),
  includeOfficerName: z.boolean().nullable(),
  includeOfficerPhone: z.boolean().nullable(),
  printOfficer: z.enum([
    'PRIMARY_OFFICER',
    'SECONDARY_OFFICER',
    'TREASURY_OFFICER',
    'NO_PRINT',
  ]),
  relationshipSummary: z.boolean().nullable(),
  relationshipSummaryLabel: z.string().nullable(),
  relationshipSummaryLocation: z.string().nullable(),
  requiredBalance: z.boolean().nullable(),
  requiredBalanceMultiplier: z.boolean().nullable(),
  resultsSummaryLabel: z.string(),
  resultsSummaryLocation: z.string(),
  resultsSummarySize: z.string(),
  returnAddress: z.string().nullable(),
  sectionBackgroundColor: z.string(),
  sectionBorderColor: z.string(),
  sectionTextColor: z.string(),
  serviceCategoryBackgroundColor: z.string().nullable(),
  serviceCategoryLevel: z.enum(['ONE', 'TWO', 'THREE']).nullable(),
  serviceCategoryPieChart: z.boolean().nullable(),
  serviceCategoryPieChartLabel: z.string().nullable(),
  serviceCategoryPieChartLocation: z.string().nullable(),
  serviceCategorySort: z
    .enum([
      'USER_DEFINED',
      'ALPHABETICAL_BY_CATEGORY_NAME',
      'BY_LOWEST_TO_HIGHEST_SERVICE_CODE',
    ])
    .nullable(),
  serviceCategorySubtotal: z.boolean().nullable(),
  serviceCategorySubtotalBackgroundColor: z.string().nullable(),
  serviceChargesDueBarChart: z.boolean().nullable(),
  serviceChargesDueBarChartLabel: z.string().nullable(),
  serviceChargesDueBarChartLocation: z.string().nullable(),
  serviceCode: z.boolean().nullable(),
  serviceDetailLabel: z.string(),
  serviceDetailLocation: z.string().nullable(),
  sortServicesType: z.enum([
    'USER_DEFINED',
    'ALPHABETICAL_BY_CATEGORY_NAME',
    'BY_LOWEST_TO_HIGHEST_SERVICE_CODE',
  ]),
  statementImageLocations: z.array(z.string()),
  statementMessage: z.boolean().nullable(),
  statementMessageImages: z.array(z.string()),
  statementMessageLocation: z.string().nullable(),
})
const statementFormatPlanTransformer = z.object({
  ...statementFormatPlanValidator.shape,
  afpServiceCode:
    statementFormatPlanValidator.shape.afpServiceCode.transform(
      nullToUndefined,
    ),
  balanceSummary:
    statementFormatPlanValidator.shape.balanceSummary.transform(
      nullToUndefined,
    ),
  balanceSummaryLabel:
    statementFormatPlanValidator.shape.balanceSummaryLabel.transform(
      nullToUndefined,
    ),
  balanceSummaryLocation:
    statementFormatPlanValidator.shape.balanceSummaryLocation.transform(
      nullToUndefined,
    ),
  balanceSummarySize:
    statementFormatPlanValidator.shape.balanceSummarySize.transform(
      nullToUndefined,
    ),
  boldServiceCategoryLabel:
    statementFormatPlanValidator.shape.boldServiceCategoryLabel.transform(
      nullToUndefined,
    ),
  boldServiceCategorySubtotalLabel:
    statementFormatPlanValidator.shape.boldServiceCategorySubtotalLabel.transform(
      nullToUndefined,
    ),
  dailyBalanceSummaryLabel:
    statementFormatPlanValidator.shape.dailyBalanceSummaryLabel.transform(
      nullToUndefined,
    ),
  dailyBalanceSummaryLocation:
    statementFormatPlanValidator.shape.dailyBalanceSummaryLocation.transform(
      nullToUndefined,
    ),
  enableServiceCategory:
    statementFormatPlanValidator.shape.enableServiceCategory.transform(
      nullToUndefined,
    ),
  historicalSummaryLabel:
    statementFormatPlanValidator.shape.historicalSummaryLabel.transform(
      nullToUndefined,
    ),
  historicalSummaryLocation:
    statementFormatPlanValidator.shape.historicalSummaryLocation.transform(
      nullToUndefined,
    ),
  includeOfficerName:
    statementFormatPlanValidator.shape.includeOfficerName.transform(
      nullToUndefined,
    ),
  includeOfficerPhone:
    statementFormatPlanValidator.shape.includeOfficerPhone.transform(
      nullToUndefined,
    ),
  relationshipSummary:
    statementFormatPlanValidator.shape.relationshipSummary.transform(
      nullToUndefined,
    ),
  relationshipSummaryLabel:
    statementFormatPlanValidator.shape.relationshipSummaryLabel.transform(
      nullToUndefined,
    ),
  relationshipSummaryLocation:
    statementFormatPlanValidator.shape.relationshipSummaryLocation.transform(
      nullToUndefined,
    ),
  requiredBalance:
    statementFormatPlanValidator.shape.requiredBalance.transform(
      nullToUndefined,
    ),
  requiredBalanceMultiplier:
    statementFormatPlanValidator.shape.requiredBalanceMultiplier.transform(
      nullToUndefined,
    ),
  returnAddress:
    statementFormatPlanValidator.shape.returnAddress.transform(nullToUndefined),
  serviceCategoryBackgroundColor:
    statementFormatPlanValidator.shape.serviceCategoryBackgroundColor.transform(
      nullToUndefined,
    ),
  serviceCategoryLevel:
    statementFormatPlanValidator.shape.serviceCategoryLevel.transform(
      nullToUndefined,
    ),
  serviceCategoryPieChart:
    statementFormatPlanValidator.shape.serviceCategoryPieChart.transform(
      nullToUndefined,
    ),
  serviceCategoryPieChartLabel:
    statementFormatPlanValidator.shape.serviceCategoryPieChartLabel.transform(
      nullToUndefined,
    ),
  serviceCategoryPieChartLocation:
    statementFormatPlanValidator.shape.serviceCategoryPieChartLocation.transform(
      nullToUndefined,
    ),
  serviceCategorySort:
    statementFormatPlanValidator.shape.serviceCategorySort.transform(
      nullToUndefined,
    ),
  serviceCategorySubtotal:
    statementFormatPlanValidator.shape.serviceCategorySubtotal.transform(
      nullToUndefined,
    ),
  serviceCategorySubtotalBackgroundColor:
    statementFormatPlanValidator.shape.serviceCategorySubtotalBackgroundColor.transform(
      nullToUndefined,
    ),
  serviceChargesDueBarChart:
    statementFormatPlanValidator.shape.serviceChargesDueBarChart.transform(
      nullToUndefined,
    ),
  serviceChargesDueBarChartLabel:
    statementFormatPlanValidator.shape.serviceChargesDueBarChartLabel.transform(
      nullToUndefined,
    ),
  serviceChargesDueBarChartLocation:
    statementFormatPlanValidator.shape.serviceChargesDueBarChartLocation.transform(
      nullToUndefined,
    ),
  serviceCode:
    statementFormatPlanValidator.shape.serviceCode.transform(nullToUndefined),
  serviceDetailLocation:
    statementFormatPlanValidator.shape.serviceDetailLocation.transform(
      nullToUndefined,
    ),
  statementMessage:
    statementFormatPlanValidator.shape.statementMessage.transform(
      nullToUndefined,
    ),
  statementMessageLocation:
    statementFormatPlanValidator.shape.statementMessageLocation.transform(
      nullToUndefined,
    ),
})
export type StatementFormatPlanForm = z.input<
  typeof statementFormatPlanTransformer
>
export type StatementFormatPlan = z.infer<typeof statementFormatPlanTransformer>
const statementFormatPlanDeprecated = z.object({
  afpServiceCode: z.boolean().nullable(),
  balanceSummary: z.boolean().nullable(),
  balanceSummaryLabel: z.string().nullable(),
  balanceSummaryLocation: z.string().nullable(),
  balanceSummarySize: z.enum(['FULL', 'HALF']).nullable(),
  boldServiceCategoryLabel: z.boolean().nullable(),
  boldServiceCategorySubtotalLabel: z.boolean().nullable(),
  code: z.string(),
  dailyBalanceSummary: z.enum(['NO', 'YES_ALL_ACCOUNTS', 'YES_COMPOSITE_ONLY']),
  dailyBalanceSummaryLabel: z.string().nullable(),
  dailyBalanceSummaryLocation: z.string().nullable(),
  description: z.string(),
  earningsCreditRate: z.enum(['EARNINGS_CREDIT_DEFINITION', 'CALCULATED_RATE']),
  effectiveDate: z.string().date(),
  enableServiceCategory: z.boolean().nullable(),
  headerLogoFileName: z.string(),
  historicalSummaryLabel: z.string().nullable(),
  historicalSummaryLocation: z.string().nullable(),
  historicalSummaryType: z.enum(['ROLLING_12_MONTHS', 'YTD', 'NO']),
  includeOfficerName: z.boolean().nullable(),
  includeOfficerPhone: z.boolean().nullable(),
  printOfficer: z.enum([
    'PRIMARY_OFFICER',
    'SECONDARY_OFFICER',
    'TREASURY_OFFICER',
    'NO_PRINT',
  ]),
  relationshipSummary: z.boolean().nullable(),
  relationshipSummaryLabel: z.string().nullable(),
  relationshipSummaryLocation: z.string().nullable(),
  requiredBalance: z.boolean().nullable(),
  requiredBalanceMultiplier: z.boolean().nullable(),
  resultsSummaryLabel: z.string(),
  resultsSummaryLocation: z.string(),
  resultsSummarySize: z.string(),
  returnAddress: z.string().nullable(),
  sectionBackgroundColor: z.string(),
  sectionBorderColor: z.string(),
  sectionTextColor: z.string(),
  serviceCategoryBackgroundColor: z.string().nullable(),
  serviceCategoryLevel: z.enum(['ONE', 'TWO', 'THREE']).nullable(),
  serviceCategoryPieChart: z.boolean().nullable(),
  serviceCategoryPieChartLabel: z.string().nullable(),
  serviceCategoryPieChartLocation: z.string().nullable(),
  serviceCategorySort: z
    .enum([
      'USER_DEFINED',
      'ALPHABETICAL_BY_CATEGORY_NAME',
      'BY_LOWEST_TO_HIGHEST_SERVICE_CODE',
    ])
    .nullable(),
  serviceCategorySubtotal: z.boolean().nullable(),
  serviceCategorySubtotalBackgroundColor: z.string().nullable(),
  serviceChargesDueBarChart: z.boolean().nullable(),
  serviceChargesDueBarChartLabel: z.string().nullable(),
  serviceChargesDueBarChartLocation: z.string().nullable(),
  serviceCode: z.boolean().nullable(),
  serviceDetailLabel: z.string(),
  serviceDetailLocation: z.string().nullable(),
  sortServicesType: z.enum([
    'USER_DEFINED',
    'ALPHABETICAL_BY_CATEGORY_NAME',
    'BY_LOWEST_TO_HIGHEST_SERVICE_CODE',
  ]),
  statementImageLocations: z.array(z.string()),
  statementMessage: z.boolean().nullable(),
  statementMessageImages: z.array(z.string()),
  statementMessageLocation: z.string().nullable(),
})
export type DefaultStatementFormatPlanForm = Omit<
  StatementFormatPlanForm,
  | 'dailyBalanceSummary'
  | 'earningsCreditRate'
  | 'historicalSummaryType'
  | 'printOfficer'
  | 'sortServicesType'
>
const defaultStatementFormatPlan: DefaultStatementFormatPlanForm = {
  afpServiceCode: null,
  balanceSummary: null,
  balanceSummaryLabel: '',
  balanceSummaryLocation: '',
  balanceSummarySize: null,
  boldServiceCategoryLabel: null,
  boldServiceCategorySubtotalLabel: null,
  code: '',
  dailyBalanceSummaryLabel: '',
  dailyBalanceSummaryLocation: '',
  description: '',
  effectiveDate: '',
  enableServiceCategory: null,
  headerLogoFileName: '',
  historicalSummaryLabel: '',
  historicalSummaryLocation: '',
  includeOfficerName: null,
  includeOfficerPhone: null,
  relationshipSummary: null,
  relationshipSummaryLabel: '',
  relationshipSummaryLocation: '',
  requiredBalance: null,
  requiredBalanceMultiplier: null,
  resultsSummaryLabel: '',
  resultsSummaryLocation: '',
  resultsSummarySize: '',
  returnAddress: '',
  sectionBackgroundColor: '',
  sectionBorderColor: '',
  sectionTextColor: '',
  serviceCategoryBackgroundColor: '',
  serviceCategoryLevel: null,
  serviceCategoryPieChart: null,
  serviceCategoryPieChartLabel: '',
  serviceCategoryPieChartLocation: '',
  serviceCategorySort: null,
  serviceCategorySubtotal: null,
  serviceCategorySubtotalBackgroundColor: '',
  serviceChargesDueBarChart: null,
  serviceChargesDueBarChartLabel: '',
  serviceChargesDueBarChartLocation: '',
  serviceCode: null,
  serviceDetailLabel: '',
  serviceDetailLocation: '',
  statementImageLocations: [],
  statementMessage: null,
  statementMessageImages: [],
  statementMessageLocation: '',
}

const statementMessageValidator = z.object({
  code: z.string(),
  effectiveDate: z.string().date(),
  message: z.string(),
  name: z.string(),
})
const statementMessageTransformer = statementMessageValidator
export type StatementMessageForm = z.input<typeof statementMessageTransformer>
export type StatementMessage = z.infer<typeof statementMessageTransformer>
const statementMessageDeprecated = z.object({
  code: z.string(),
  effectiveDate: z.string().date(),
  message: z.string(),
  name: z.string(),
})
const defaultStatementMessage: StatementMessageForm = {
  code: '',
  effectiveDate: '',
  message: '',
  name: '',
}

const versionableEntityIdValidator = z.object({
  code: z.string(),
  effectiveDate: z.string().date(),
})
const versionableEntityIdTransformer = versionableEntityIdValidator
export type VersionableEntityIdForm = z.input<
  typeof versionableEntityIdTransformer
>
export type VersionableEntityId = z.infer<typeof versionableEntityIdTransformer>
const versionableEntityIdDeprecated = z.object({
  code: z.string(),
  effectiveDate: z.string().date(),
})
const defaultVersionableEntityId: VersionableEntityIdForm = {
  code: '',
  effectiveDate: '',
}

export const formToApiSchemas = {
  account: accountTransformer,
  accountCode: accountCodeTransformer,
  accountCodeWithEffectivedateReq: accountCodeWithEffectivedateReqTransformer,
  accountCodesDateLeadAccountRequest:
    accountCodesDateLeadAccountRequestTransformer,
  accountCodesRetrieveRequest: accountCodesRetrieveRequestTransformer,
  accountEffectiveDateRequest: accountEffectiveDateRequestTransformer,
  accountEntity: accountEntityTransformer,
  accountMappingCreateRequest: accountMappingCreateRequestTransformer,
  accountMappingEntity: accountMappingEntityTransformer,
  accountMappingUpdateRequest: accountMappingUpdateRequestTransformer,
  accountType: accountTypeTransformer,
  accountTypeOverride: accountTypeOverrideTransformer,
  accountWithKey: accountWithKeyTransformer,
  address: addressTransformer,
  addressCode: addressCodeTransformer,
  addressCodeAvailabilityRequest: addressCodeAvailabilityRequestTransformer,
  addressCodeAvailabilityResponse: addressCodeAvailabilityResponseTransformer,
  addressRetrieveRequest: addressRetrieveRequestTransformer,
  analysisResultOption: analysisResultOptionTransformer,
  balanceRequirementDefinition: balanceRequirementDefinitionTransformer,
  balanceTier: balanceTierTransformer,
  bankOptions: bankOptionsTransformer,
  branch: branchTransformer,
  codeRequestBody: codeRequestBodyTransformer,
  codesAndEffectiveDateRequestBody: codesAndEffectiveDateRequestBodyTransformer,
  compositeAccountCreateRequest: compositeAccountCreateRequestTransformer,
  createOrUpdateService: createOrUpdateServiceTransformer,
  createServiceCategory: createServiceCategoryTransformer,
  cycleDefinition: cycleDefinitionTransformer,
  dailyBalanceHistory: dailyBalanceHistoryTransformer,
  demographicCriteria: demographicCriteriaTransformer,
  demographicCriteriaId: demographicCriteriaIdTransformer,
  demographicPriceList: demographicPriceListTransformer,
  demographicPriceListRanking: demographicPriceListRankingTransformer,
  demographicPriceListWithCount: demographicPriceListWithCountTransformer,
  demographicUpdateRecordEntity: demographicUpdateRecordEntityTransformer,
  earningsCreditDefinition: earningsCreditDefinitionTransformer,
  effectiveDateRequestBody: effectiveDateRequestBodyTransformer,
  getDailyBalanceHistoryRequest: getDailyBalanceHistoryRequestTransformer,
  hydratedAccountWithKeyAccountMapping:
    hydratedAccountWithKeyAccountMappingTransformer,
  hydratedCategoryToCategoryMapping:
    hydratedCategoryToCategoryMappingTransformer,
  hydratedDemographicPriceList: hydratedDemographicPriceListTransformer,
  hydratedServiceCatalogMapping: hydratedServiceCatalogMappingTransformer,
  hydratedServicePrice: hydratedServicePriceTransformer,
  hydratedServiceToCategoryMapping: hydratedServiceToCategoryMappingTransformer,
  hydratedStatementPackage: hydratedStatementPackageTransformer,
  hydratedUserFieldConfiguration: hydratedUserFieldConfigurationTransformer,
  hydratedUserFieldConfigurationCreate:
    hydratedUserFieldConfigurationCreateTransformer,
  hydratedUserFieldConfigurationUpdate:
    hydratedUserFieldConfigurationUpdateTransformer,
  indexRate: indexRateTransformer,
  investableBalanceDefinition: investableBalanceDefinitionTransformer,
  keyAccountMapping: keyAccountMappingTransformer,
  nextAvailableAccountNumberResponse:
    nextAvailableAccountNumberResponseTransformer,
  officer: officerTransformer,
  promotionalPriceList: promotionalPriceListTransformer,
  reserveRequirementDefinition: reserveRequirementDefinitionTransformer,
  service: serviceTransformer,
  serviceCategory: serviceCategoryTransformer,
  serviceDetails: serviceDetailsTransformer,
  servicePrice: servicePriceTransformer,
  servicePriceRequest: servicePriceRequestTransformer,
  settlementProcessingOptionsResponseObj:
    settlementProcessingOptionsResponseObjTransformer,
  statementFormatPlan: statementFormatPlanTransformer,
  statementMessage: statementMessageTransformer,
  statementPackage: statementPackageTransformer,
  statementPackageCreateUpdateRequest:
    statementPackageCreateUpdateRequestTransformer,
  userFieldDropdownOptionCreate: userFieldDropdownOptionCreateTransformer,
  userFieldDropdownOptionUpdate: userFieldDropdownOptionUpdateTransformer,
  userFieldSelection: userFieldSelectionTransformer,
  versionableEntityId: versionableEntityIdTransformer,
}

export const formValidators = {
  account: accountValidator,
  accountCode: accountCodeValidator,
  accountCodeWithEffectivedateReq: accountCodeWithEffectivedateReqValidator,
  accountCodesDateLeadAccountRequest:
    accountCodesDateLeadAccountRequestValidator,
  accountCodesRetrieveRequest: accountCodesRetrieveRequestValidator,
  accountEffectiveDateRequest: accountEffectiveDateRequestValidator,
  accountEntity: accountEntityValidator,
  accountMappingCreateRequest: accountMappingCreateRequestValidator,
  accountMappingEntity: accountMappingEntityValidator,
  accountMappingUpdateRequest: accountMappingUpdateRequestValidator,
  accountType: accountTypeValidator,
  accountTypeOverride: accountTypeOverrideValidator,
  accountWithKey: accountWithKeyValidator,
  address: addressValidator,
  addressCode: addressCodeValidator,
  addressCodeAvailabilityRequest: addressCodeAvailabilityRequestValidator,
  addressCodeAvailabilityResponse: addressCodeAvailabilityResponseValidator,
  addressRetrieveRequest: addressRetrieveRequestValidator,
  analysisResultOption: analysisResultOptionValidator,
  balanceRequirementDefinition: balanceRequirementDefinitionValidator,
  balanceTier: balanceTierValidator,
  bankOptions: bankOptionsValidator,
  branch: branchValidator,
  codeRequestBody: codeRequestBodyValidator,
  codesAndEffectiveDateRequestBody: codesAndEffectiveDateRequestBodyValidator,
  compositeAccountCreateRequest: compositeAccountCreateRequestValidator,
  createOrUpdateService: createOrUpdateServiceValidator,
  createServiceCategory: createServiceCategoryValidator,
  cycleDefinition: cycleDefinitionValidator,
  dailyBalanceHistory: dailyBalanceHistoryValidator,
  demographicCriteria: demographicCriteriaValidator,
  demographicCriteriaId: demographicCriteriaIdValidator,
  demographicPriceList: demographicPriceListValidator,
  demographicPriceListRanking: demographicPriceListRankingValidator,
  demographicPriceListWithCount: demographicPriceListWithCountValidator,
  demographicUpdateRecordEntity: demographicUpdateRecordEntityValidator,
  earningsCreditDefinition: earningsCreditDefinitionValidator,
  effectiveDateRequestBody: effectiveDateRequestBodyValidator,
  getDailyBalanceHistoryRequest: getDailyBalanceHistoryRequestValidator,
  hydratedAccountWithKeyAccountMapping:
    hydratedAccountWithKeyAccountMappingValidator,
  hydratedCategoryToCategoryMapping: hydratedCategoryToCategoryMappingValidator,
  hydratedDemographicPriceList: hydratedDemographicPriceListValidator,
  hydratedServiceCatalogMapping: hydratedServiceCatalogMappingValidator,
  hydratedServicePrice: hydratedServicePriceValidator,
  hydratedServiceToCategoryMapping: hydratedServiceToCategoryMappingValidator,
  hydratedStatementPackage: hydratedStatementPackageValidator,
  hydratedUserFieldConfiguration: hydratedUserFieldConfigurationValidator,
  hydratedUserFieldConfigurationCreate:
    hydratedUserFieldConfigurationCreateValidator,
  hydratedUserFieldConfigurationUpdate:
    hydratedUserFieldConfigurationUpdateValidator,
  indexRate: indexRateValidator,
  investableBalanceDefinition: investableBalanceDefinitionValidator,
  keyAccountMapping: keyAccountMappingValidator,
  nextAvailableAccountNumberResponse:
    nextAvailableAccountNumberResponseValidator,
  officer: officerValidator,
  promotionalPriceList: promotionalPriceListValidator,
  reserveRequirementDefinition: reserveRequirementDefinitionValidator,
  service: serviceValidator,
  serviceCategory: serviceCategoryValidator,
  serviceDetails: serviceDetailsValidator,
  servicePrice: servicePriceValidator,
  servicePriceRequest: servicePriceRequestValidator,
  settlementProcessingOptionsResponseObj:
    settlementProcessingOptionsResponseObjValidator,
  statementFormatPlan: statementFormatPlanValidator,
  statementMessage: statementMessageValidator,
  statementPackage: statementPackageValidator,
  statementPackageCreateUpdateRequest:
    statementPackageCreateUpdateRequestValidator,
  userFieldDropdownOptionCreate: userFieldDropdownOptionCreateValidator,
  userFieldDropdownOptionUpdate: userFieldDropdownOptionUpdateValidator,
  userFieldSelection: userFieldSelectionValidator,
  versionableEntityId: versionableEntityIdValidator,
}

export const defaultValues = {
  account: defaultAccount,
  accountCode: defaultAccountCode,
  accountCodeWithEffectivedateReq: defaultAccountCodeWithEffectivedateReq,
  accountCodesDateLeadAccountRequest: defaultAccountCodesDateLeadAccountRequest,
  accountCodesRetrieveRequest: defaultAccountCodesRetrieveRequest,
  accountEffectiveDateRequest: defaultAccountEffectiveDateRequest,
  accountEntity: defaultAccountEntity,
  accountMappingCreateRequest: defaultAccountMappingCreateRequest,
  accountMappingEntity: defaultAccountMappingEntity,
  accountMappingUpdateRequest: defaultAccountMappingUpdateRequest,
  accountType: defaultAccountType,
  accountTypeOverride: defaultAccountTypeOverride,
  accountWithKey: defaultAccountWithKey,
  address: defaultAddress,
  addressCode: defaultAddressCode,
  addressCodeAvailabilityRequest: defaultAddressCodeAvailabilityRequest,
  addressCodeAvailabilityResponse: defaultAddressCodeAvailabilityResponse,
  addressRetrieveRequest: defaultAddressRetrieveRequest,
  analysisResultOption: defaultAnalysisResultOption,
  balanceRequirementDefinition: defaultBalanceRequirementDefinition,
  balanceTier: defaultBalanceTier,
  bankOptions: defaultBankOptions,
  branch: defaultBranch,
  codeRequestBody: defaultCodeRequestBody,
  codesAndEffectiveDateRequestBody: defaultCodesAndEffectiveDateRequestBody,
  compositeAccountCreateRequest: defaultCompositeAccountCreateRequest,
  createOrUpdateService: defaultCreateOrUpdateService,
  createServiceCategory: defaultCreateServiceCategory,
  cycleDefinition: defaultCycleDefinition,
  dailyBalanceHistory: defaultDailyBalanceHistory,
  demographicCriteria: defaultDemographicCriteria,
  demographicCriteriaId: defaultDemographicCriteriaId,
  demographicPriceList: defaultDemographicPriceList,
  demographicPriceListRanking: defaultDemographicPriceListRanking,
  demographicPriceListWithCount: defaultDemographicPriceListWithCount,
  demographicUpdateRecordEntity: defaultDemographicUpdateRecordEntity,
  earningsCreditDefinition: defaultEarningsCreditDefinition,
  effectiveDateRequestBody: defaultEffectiveDateRequestBody,
  getDailyBalanceHistoryRequest: defaultGetDailyBalanceHistoryRequest,
  hydratedAccountWithKeyAccountMapping:
    defaultHydratedAccountWithKeyAccountMapping,
  hydratedCategoryToCategoryMapping: defaultHydratedCategoryToCategoryMapping,
  hydratedDemographicPriceList: defaultHydratedDemographicPriceList,
  hydratedServiceCatalogMapping: defaultHydratedServiceCatalogMapping,
  hydratedServicePrice: defaultHydratedServicePrice,
  hydratedServiceToCategoryMapping: defaultHydratedServiceToCategoryMapping,
  hydratedStatementPackage: defaultHydratedStatementPackage,
  hydratedUserFieldConfiguration: defaultHydratedUserFieldConfiguration,
  hydratedUserFieldConfigurationCreate:
    defaultHydratedUserFieldConfigurationCreate,
  hydratedUserFieldConfigurationUpdate:
    defaultHydratedUserFieldConfigurationUpdate,
  indexRate: defaultIndexRate,
  investableBalanceDefinition: defaultInvestableBalanceDefinition,
  keyAccountMapping: defaultKeyAccountMapping,
  nextAvailableAccountNumberResponse: defaultNextAvailableAccountNumberResponse,
  officer: defaultOfficer,
  promotionalPriceList: defaultPromotionalPriceList,
  reserveRequirementDefinition: defaultReserveRequirementDefinition,
  service: defaultService,
  serviceCategory: defaultServiceCategory,
  serviceDetails: defaultServiceDetails,
  servicePrice: defaultServicePrice,
  servicePriceRequest: defaultServicePriceRequest,
  settlementProcessingOptionsResponseObj:
    defaultSettlementProcessingOptionsResponseObj,
  statementFormatPlan: defaultStatementFormatPlan,
  statementMessage: defaultStatementMessage,
  statementPackage: defaultStatementPackage,
  statementPackageCreateUpdateRequest:
    defaultStatementPackageCreateUpdateRequest,
  userFieldDropdownOptionCreate: defaultUserFieldDropdownOptionCreate,
  userFieldDropdownOptionUpdate: defaultUserFieldDropdownOptionUpdate,
  userFieldSelection: defaultUserFieldSelection,
  versionableEntityId: defaultVersionableEntityId,
}

/**
 * @deprecated we should remove all references to these
 */
export const deprecatedFormToApiSchemas = {
  account: accountDeprecated,
  accountCode: accountCodeDeprecated,
  accountCodeWithEffectivedateReq: accountCodeWithEffectivedateReqDeprecated,
  accountCodesDateLeadAccountRequest:
    accountCodesDateLeadAccountRequestDeprecated,
  accountCodesRetrieveRequest: accountCodesRetrieveRequestDeprecated,
  accountEffectiveDateRequest: accountEffectiveDateRequestDeprecated,
  accountEntity: accountEntityDeprecated,
  accountMappingCreateRequest: accountMappingCreateRequestDeprecated,
  accountMappingEntity: accountMappingEntityDeprecated,
  accountMappingUpdateRequest: accountMappingUpdateRequestDeprecated,
  accountType: accountTypeDeprecated,
  accountTypeOverride: accountTypeOverrideDeprecated,
  accountWithKey: accountWithKeyDeprecated,
  address: addressDeprecated,
  addressCode: addressCodeDeprecated,
  addressCodeAvailabilityRequest: addressCodeAvailabilityRequestDeprecated,
  addressCodeAvailabilityResponse: addressCodeAvailabilityResponseDeprecated,
  addressRetrieveRequest: addressRetrieveRequestDeprecated,
  analysisResultOption: analysisResultOptionDeprecated,
  balanceRequirementDefinition: balanceRequirementDefinitionDeprecated,
  balanceTier: balanceTierDeprecated,
  bankOptions: bankOptionsDeprecated,
  branch: branchDeprecated,
  codeRequestBody: codeRequestBodyDeprecated,
  codesAndEffectiveDateRequestBody: codesAndEffectiveDateRequestBodyDeprecated,
  compositeAccountCreateRequest: compositeAccountCreateRequestDeprecated,
  createOrUpdateService: createOrUpdateServiceDeprecated,
  createServiceCategory: createServiceCategoryDeprecated,
  cycleDefinition: cycleDefinitionDeprecated,
  dailyBalanceHistory: dailyBalanceHistoryDeprecated,
  demographicCriteria: demographicCriteriaDeprecated,
  demographicCriteriaId: demographicCriteriaIdDeprecated,
  demographicPriceList: demographicPriceListDeprecated,
  demographicPriceListRanking: demographicPriceListRankingDeprecated,
  demographicPriceListWithCount: demographicPriceListWithCountDeprecated,
  demographicUpdateRecordEntity: demographicUpdateRecordEntityDeprecated,
  earningsCreditDefinition: earningsCreditDefinitionDeprecated,
  effectiveDateRequestBody: effectiveDateRequestBodyDeprecated,
  getDailyBalanceHistoryRequest: getDailyBalanceHistoryRequestDeprecated,
  hydratedAccountWithKeyAccountMapping:
    hydratedAccountWithKeyAccountMappingDeprecated,
  hydratedCategoryToCategoryMapping:
    hydratedCategoryToCategoryMappingDeprecated,
  hydratedDemographicPriceList: hydratedDemographicPriceListDeprecated,
  hydratedServiceCatalogMapping: hydratedServiceCatalogMappingDeprecated,
  hydratedServicePrice: hydratedServicePriceDeprecated,
  hydratedServiceToCategoryMapping: hydratedServiceToCategoryMappingDeprecated,
  hydratedStatementPackage: hydratedStatementPackageDeprecated,
  hydratedUserFieldConfiguration: hydratedUserFieldConfigurationDeprecated,
  hydratedUserFieldConfigurationCreate:
    hydratedUserFieldConfigurationCreateDeprecated,
  hydratedUserFieldConfigurationUpdate:
    hydratedUserFieldConfigurationUpdateDeprecated,
  indexRate: indexRateDeprecated,
  investableBalanceDefinition: investableBalanceDefinitionDeprecated,
  keyAccountMapping: keyAccountMappingDeprecated,
  nextAvailableAccountNumberResponse:
    nextAvailableAccountNumberResponseDeprecated,
  officer: officerDeprecated,
  promotionalPriceList: promotionalPriceListDeprecated,
  reserveRequirementDefinition: reserveRequirementDefinitionDeprecated,
  service: serviceDeprecated,
  serviceCategory: serviceCategoryDeprecated,
  serviceDetails: serviceDetailsDeprecated,
  servicePrice: servicePriceDeprecated,
  servicePriceRequest: servicePriceRequestDeprecated,
  settlementProcessingOptionsResponseObj:
    settlementProcessingOptionsResponseObjDeprecated,
  statementFormatPlan: statementFormatPlanDeprecated,
  statementMessage: statementMessageDeprecated,
  statementPackage: statementPackageDeprecated,
  statementPackageCreateUpdateRequest:
    statementPackageCreateUpdateRequestDeprecated,
  userFieldDropdownOptionCreate: userFieldDropdownOptionCreateDeprecated,
  userFieldDropdownOptionUpdate: userFieldDropdownOptionUpdateDeprecated,
  userFieldSelection: userFieldSelectionDeprecated,
  versionableEntityId: versionableEntityIdDeprecated,
}
