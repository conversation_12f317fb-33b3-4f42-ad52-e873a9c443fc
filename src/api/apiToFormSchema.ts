// This file is generated via the `npm run schemagen` command.
//
// That command runs the `./generateZodSchemas.ts` script.
// Edit that file and not this one.

import { z } from 'zod'

const undefinedToNull = <T>(v: T) => (v === undefined ? null : v)

const accountValidator = z.object({
  accountNumber: z.string(),
  accountStatus: z.enum(['Z', 'B', 'C', 'P', 'X']).optional().nullable(),
  analysisAccountTypeCode: z.string(),
  applicationId: z.enum(['C', 'D']),
  bankNumber: z.string(),
  branchCode: z.string(),
  closeDate: z.string().date().optional().nullable(),
  costCenter: z.string(),
  currencyCode: z.string(),
  customerSpecificPricingIndicator: z.boolean().optional().nullable(),
  depositAccountTypeCode: z.string().optional().nullable(),
  depositCategory: z
    .enum(['A', 'C', 'D', 'L', 'M', 'N', 'R', 'S', 'P', 'T'])
    .optional()
    .nullable(),
  effectiveDate: z.string().date(),
  openDate: z.string().date(),
  primaryOfficerCode: z.string(),
  processingIndicator: z.enum(['A', 'B']).optional().nullable(),
  secondaryOfficerCode: z.string().optional().nullable(),
  shortName: z.string(),
  treasuryOfficerCode: z.string().optional().nullable(),
})
const accountTransformer = z.object({
  ...accountValidator.shape,
  accountStatus:
    accountValidator.shape.accountStatus.transform(undefinedToNull),
  closeDate: accountValidator.shape.closeDate.transform(undefinedToNull),
  customerSpecificPricingIndicator:
    accountValidator.shape.customerSpecificPricingIndicator.transform(
      undefinedToNull,
    ),
  depositAccountTypeCode:
    accountValidator.shape.depositAccountTypeCode.transform(undefinedToNull),
  depositCategory:
    accountValidator.shape.depositCategory.transform(undefinedToNull),
  processingIndicator:
    accountValidator.shape.processingIndicator.transform(undefinedToNull),
  secondaryOfficerCode:
    accountValidator.shape.secondaryOfficerCode.transform(undefinedToNull),
  treasuryOfficerCode:
    accountValidator.shape.treasuryOfficerCode.transform(undefinedToNull),
})
const accountDeprecated = z.object({
  accountNumber: z.string(),
  accountStatus: z.enum(['Z', 'B', 'C', 'P', 'X']).optional().nullable(),
  analysisAccountTypeCode: z.string(),
  applicationId: z.enum(['C', 'D']),
  bankNumber: z.string(),
  branchCode: z.string(),
  closeDate: z.string().date().optional().nullable(),
  costCenter: z.string(),
  currencyCode: z.string(),
  customerSpecificPricingIndicator: z.boolean().optional().nullable(),
  depositAccountTypeCode: z.string().optional().nullable(),
  depositCategory: z
    .enum(['A', 'C', 'D', 'L', 'M', 'N', 'R', 'S', 'P', 'T'])
    .optional()
    .nullable(),
  effectiveDate: z.string().date(),
  openDate: z.string().date(),
  primaryOfficerCode: z.string(),
  processingIndicator: z.enum(['A', 'B']).optional().nullable(),
  secondaryOfficerCode: z.string().optional().nullable(),
  shortName: z.string(),
  treasuryOfficerCode: z.string().optional().nullable(),
})

const accountCodeValidator = z.object({
  accountNumber: z.string(),
  applicationId: z.enum(['C', 'D']),
  bankNumber: z.string(),
})
const accountCodeTransformer = accountCodeValidator
const accountCodeDeprecated = z.object({
  accountNumber: z.string(),
  applicationId: z.enum(['C', 'D']),
  bankNumber: z.string(),
})

const accountCodeWithEffectivedateReqValidator = z.object({
  accountCode: accountCodeValidator,
  effectiveDate: z.string().date(),
})
const accountCodeWithEffectivedateReqTransformer = z.object({
  ...accountCodeWithEffectivedateReqValidator.shape,
  accountCode: accountCodeTransformer,
})
const accountCodeWithEffectivedateReqDeprecated = z.object({
  accountCode: accountCodeDeprecated,
  effectiveDate: z.string().date(),
})

const accountCodesDateLeadAccountRequestValidator = z.object({
  accountCodes: z.array(accountCodeValidator),
  effectiveDate: z.string().date(),
  leadAccount: accountCodeValidator,
})
const accountCodesDateLeadAccountRequestTransformer = z.object({
  ...accountCodesDateLeadAccountRequestValidator.shape,
  accountCodes: z.array(accountCodeTransformer),
  leadAccount: accountCodeTransformer,
})
const accountCodesDateLeadAccountRequestDeprecated = z.object({
  accountCodes: z.array(accountCodeDeprecated),
  effectiveDate: z.string().date(),
  leadAccount: accountCodeDeprecated,
})

const accountCodesRetrieveRequestValidator = z.object({
  accountCodes: z.array(accountCodeValidator),
  effectiveDate: z.string().date(),
})
const accountCodesRetrieveRequestTransformer = z.object({
  ...accountCodesRetrieveRequestValidator.shape,
  accountCodes: z.array(accountCodeTransformer),
})
const accountCodesRetrieveRequestDeprecated = z.object({
  accountCodes: z.array(accountCodeDeprecated),
  effectiveDate: z.string().date(),
})

const accountEffectiveDateRequestValidator = z.object({
  accountNumber: z.string(),
  applicationId: z.enum(['C', 'D']),
  bankNumber: z.string(),
  effectiveDate: z.string().date(),
})
const accountEffectiveDateRequestTransformer =
  accountEffectiveDateRequestValidator
const accountEffectiveDateRequestDeprecated = z.object({
  accountNumber: z.string(),
  applicationId: z.enum(['C', 'D']),
  bankNumber: z.string(),
  effectiveDate: z.string().date(),
})

const accountEntityValidator = z.object({
  accountNumber: z.string(),
  accountStatus: z.enum(['Z', 'B', 'C', 'P', 'X']).optional().nullable(),
  analysisAccountTypeCode: z.string(),
  applicationId: z.enum(['C', 'D']),
  bankNumber: z.string(),
  branchCode: z.string(),
  closeDate: z.string().date().optional().nullable(),
  code: z.string().optional().nullable(),
  costCenter: z.string(),
  currencyCode: z.string(),
  customerSpecificPricingIndicator: z.boolean().optional().nullable(),
  depositAccountTypeCode: z.string().optional().nullable(),
  depositCategory: z
    .enum(['A', 'C', 'D', 'L', 'M', 'N', 'R', 'S', 'P', 'T'])
    .optional()
    .nullable(),
  effectiveDate: z.string().date(),
  openDate: z.string().date(),
  primaryOfficerCode: z.string(),
  processingIndicator: z.enum(['A', 'B']).optional().nullable(),
  secondaryOfficerCode: z.string().optional().nullable(),
  shortName: z.string().optional().nullable(),
  treasuryOfficerCode: z.string().optional().nullable(),
})
const accountEntityTransformer = z.object({
  ...accountEntityValidator.shape,
  accountStatus:
    accountEntityValidator.shape.accountStatus.transform(undefinedToNull),
  closeDate: accountEntityValidator.shape.closeDate.transform(undefinedToNull),
  code: accountEntityValidator.shape.code.transform(undefinedToNull),
  customerSpecificPricingIndicator:
    accountEntityValidator.shape.customerSpecificPricingIndicator.transform(
      undefinedToNull,
    ),
  depositAccountTypeCode:
    accountEntityValidator.shape.depositAccountTypeCode.transform(
      undefinedToNull,
    ),
  depositCategory:
    accountEntityValidator.shape.depositCategory.transform(undefinedToNull),
  processingIndicator:
    accountEntityValidator.shape.processingIndicator.transform(undefinedToNull),
  secondaryOfficerCode:
    accountEntityValidator.shape.secondaryOfficerCode.transform(
      undefinedToNull,
    ),
  shortName: accountEntityValidator.shape.shortName.transform(undefinedToNull),
  treasuryOfficerCode:
    accountEntityValidator.shape.treasuryOfficerCode.transform(undefinedToNull),
})
const accountEntityDeprecated = z.object({
  accountNumber: z.string(),
  accountStatus: z.enum(['Z', 'B', 'C', 'P', 'X']).optional().nullable(),
  analysisAccountTypeCode: z.string(),
  applicationId: z.enum(['C', 'D']),
  bankNumber: z.string(),
  branchCode: z.string(),
  closeDate: z.string().date().optional().nullable(),
  code: z.string().optional().nullable(),
  costCenter: z.string(),
  currencyCode: z.string(),
  customerSpecificPricingIndicator: z.boolean().optional().nullable(),
  depositAccountTypeCode: z.string().optional().nullable(),
  depositCategory: z
    .enum(['A', 'C', 'D', 'L', 'M', 'N', 'R', 'S', 'P', 'T'])
    .optional()
    .nullable(),
  effectiveDate: z.string().date(),
  openDate: z.string().date(),
  primaryOfficerCode: z.string(),
  processingIndicator: z.enum(['A', 'B']).optional().nullable(),
  secondaryOfficerCode: z.string().optional().nullable(),
  shortName: z.string().optional().nullable(),
  treasuryOfficerCode: z.string().optional().nullable(),
})

const accountMappingCreateRequestValidator = z.object({
  childAccountCode: accountCodeValidator,
  effectiveDate: z.string().date(),
  parentAccountCode: accountCodeValidator,
})
const accountMappingCreateRequestTransformer = z.object({
  ...accountMappingCreateRequestValidator.shape,
  childAccountCode: accountCodeTransformer,
  parentAccountCode: accountCodeTransformer,
})
const accountMappingCreateRequestDeprecated = z.object({
  childAccountCode: accountCodeDeprecated,
  effectiveDate: z.string().date(),
  parentAccountCode: accountCodeDeprecated,
})

const accountMappingEntityValidator = z.object({
  code: z.string().optional().nullable(),
  effectiveDate: z.string().date(),
  endDate: z.string().date().optional().nullable(),
  parentCode: z.string().optional().nullable(),
})
const accountMappingEntityTransformer = z.object({
  ...accountMappingEntityValidator.shape,
  code: accountMappingEntityValidator.shape.code.transform(undefinedToNull),
  endDate:
    accountMappingEntityValidator.shape.endDate.transform(undefinedToNull),
  parentCode:
    accountMappingEntityValidator.shape.parentCode.transform(undefinedToNull),
})
const accountMappingEntityDeprecated = z.object({
  code: z.string().optional().nullable(),
  effectiveDate: z.string().date(),
  endDate: z.string().date().optional().nullable(),
  parentCode: z.string().optional().nullable(),
})

const accountMappingUpdateRequestValidator = z.object({
  childAccountCodes: z.array(accountCodeValidator),
  effectiveDate: z.string().date(),
  parentAccountCode: accountCodeValidator,
})
const accountMappingUpdateRequestTransformer = z.object({
  ...accountMappingUpdateRequestValidator.shape,
  childAccountCodes: z.array(accountCodeTransformer),
  parentAccountCode: accountCodeTransformer,
})
const accountMappingUpdateRequestDeprecated = z.object({
  childAccountCodes: z.array(accountCodeDeprecated),
  effectiveDate: z.string().date(),
  parentAccountCode: accountCodeDeprecated,
})

const accountTypeValidator = z.object({
  accountTypeCode: z.string(),
  analysisResultOptionsPlanCode: z.string(),
  balanceRequirementDefinitionCode: z.string(),
  description: z.string(),
  earningsCreditDefinitionCode: z.string().optional().nullable(),
  effectiveDate: z.string().date(),
  interestRequirementDefinitionCode: z.string().optional().nullable(),
  investableBalanceDefinitionCode: z.string(),
  reserveRequirementDefinitionCode: z.string().optional().nullable(),
  settlementCyclePlanCode: z.string(),
  statementCyclePlanCode: z.string(),
  statementFormatPlanCode: z.string(),
  statementMessagePlanCode: z.string().optional().nullable(),
})
const accountTypeTransformer = z.object({
  ...accountTypeValidator.shape,
  earningsCreditDefinitionCode:
    accountTypeValidator.shape.earningsCreditDefinitionCode.transform(
      undefinedToNull,
    ),
  interestRequirementDefinitionCode:
    accountTypeValidator.shape.interestRequirementDefinitionCode.transform(
      undefinedToNull,
    ),
  reserveRequirementDefinitionCode:
    accountTypeValidator.shape.reserveRequirementDefinitionCode.transform(
      undefinedToNull,
    ),
  statementMessagePlanCode:
    accountTypeValidator.shape.statementMessagePlanCode.transform(
      undefinedToNull,
    ),
})
const accountTypeDeprecated = z.object({
  accountTypeCode: z.string(),
  analysisResultOptionsPlanCode: z.string(),
  balanceRequirementDefinitionCode: z.string(),
  description: z.string(),
  earningsCreditDefinitionCode: z.string().optional().nullable(),
  effectiveDate: z.string().date(),
  interestRequirementDefinitionCode: z.string().optional().nullable(),
  investableBalanceDefinitionCode: z.string(),
  reserveRequirementDefinitionCode: z.string().optional().nullable(),
  settlementCyclePlanCode: z.string(),
  statementCyclePlanCode: z.string(),
  statementFormatPlanCode: z.string(),
  statementMessagePlanCode: z.string().optional().nullable(),
})

const accountTypeOverrideValidator = z.object({
  accountCode: accountCodeValidator,
  analysisResultOptionsPlanCode: z.string().optional().nullable(),
  analysisResultOptionsPlanCodeExpiry: z.string().date().optional().nullable(),
  balanceRequirementDefinitionCode: z.string().optional().nullable(),
  balanceRequirementDefinitionCodeExpiry: z
    .string()
    .date()
    .optional()
    .nullable(),
  chargeAccountCode: z.string().optional().nullable(),
  earningsCreditDefinitionCode: z.string().optional().nullable(),
  earningsCreditDefinitionCodeExpiry: z.string().date().optional().nullable(),
  effectiveDate: z.string().date(),
  interestRequirementDefinitionCode: z.string().optional().nullable(),
  interestRequirementDefinitionCodeExpiry: z
    .string()
    .date()
    .optional()
    .nullable(),
  investableBalanceDefinitionCode: z.string().optional().nullable(),
  investableBalanceDefinitionCodeExpiry: z
    .string()
    .date()
    .optional()
    .nullable(),
  isOverrideAsSettlementAccount: z.boolean(),
  reserveRequirementDefinitionCode: z.string().optional().nullable(),
  reserveRequirementDefinitionCodeExpiry: z
    .string()
    .date()
    .optional()
    .nullable(),
  settlementCyclePlanCode: z.string().optional().nullable(),
  settlementCyclePlanCodeExpiry: z.string().date().optional().nullable(),
  statementCyclePlanCode: z.string().optional().nullable(),
  statementCyclePlanCodeExpiry: z.string().date().optional().nullable(),
  statementFormatPlanCode: z.string().optional().nullable(),
  statementFormatPlanCodeExpiry: z.string().date().optional().nullable(),
  statementMessagePlanCode: z.string().optional().nullable(),
  statementMessagePlanCodeExpiry: z.string().date().optional().nullable(),
})
const accountTypeOverrideTransformer = z.object({
  ...accountTypeOverrideValidator.shape,
  accountCode: accountCodeTransformer,
  analysisResultOptionsPlanCode:
    accountTypeOverrideValidator.shape.analysisResultOptionsPlanCode.transform(
      undefinedToNull,
    ),
  analysisResultOptionsPlanCodeExpiry:
    accountTypeOverrideValidator.shape.analysisResultOptionsPlanCodeExpiry.transform(
      undefinedToNull,
    ),
  balanceRequirementDefinitionCode:
    accountTypeOverrideValidator.shape.balanceRequirementDefinitionCode.transform(
      undefinedToNull,
    ),
  balanceRequirementDefinitionCodeExpiry:
    accountTypeOverrideValidator.shape.balanceRequirementDefinitionCodeExpiry.transform(
      undefinedToNull,
    ),
  chargeAccountCode:
    accountTypeOverrideValidator.shape.chargeAccountCode.transform(
      undefinedToNull,
    ),
  earningsCreditDefinitionCode:
    accountTypeOverrideValidator.shape.earningsCreditDefinitionCode.transform(
      undefinedToNull,
    ),
  earningsCreditDefinitionCodeExpiry:
    accountTypeOverrideValidator.shape.earningsCreditDefinitionCodeExpiry.transform(
      undefinedToNull,
    ),
  interestRequirementDefinitionCode:
    accountTypeOverrideValidator.shape.interestRequirementDefinitionCode.transform(
      undefinedToNull,
    ),
  interestRequirementDefinitionCodeExpiry:
    accountTypeOverrideValidator.shape.interestRequirementDefinitionCodeExpiry.transform(
      undefinedToNull,
    ),
  investableBalanceDefinitionCode:
    accountTypeOverrideValidator.shape.investableBalanceDefinitionCode.transform(
      undefinedToNull,
    ),
  investableBalanceDefinitionCodeExpiry:
    accountTypeOverrideValidator.shape.investableBalanceDefinitionCodeExpiry.transform(
      undefinedToNull,
    ),
  reserveRequirementDefinitionCode:
    accountTypeOverrideValidator.shape.reserveRequirementDefinitionCode.transform(
      undefinedToNull,
    ),
  reserveRequirementDefinitionCodeExpiry:
    accountTypeOverrideValidator.shape.reserveRequirementDefinitionCodeExpiry.transform(
      undefinedToNull,
    ),
  settlementCyclePlanCode:
    accountTypeOverrideValidator.shape.settlementCyclePlanCode.transform(
      undefinedToNull,
    ),
  settlementCyclePlanCodeExpiry:
    accountTypeOverrideValidator.shape.settlementCyclePlanCodeExpiry.transform(
      undefinedToNull,
    ),
  statementCyclePlanCode:
    accountTypeOverrideValidator.shape.statementCyclePlanCode.transform(
      undefinedToNull,
    ),
  statementCyclePlanCodeExpiry:
    accountTypeOverrideValidator.shape.statementCyclePlanCodeExpiry.transform(
      undefinedToNull,
    ),
  statementFormatPlanCode:
    accountTypeOverrideValidator.shape.statementFormatPlanCode.transform(
      undefinedToNull,
    ),
  statementFormatPlanCodeExpiry:
    accountTypeOverrideValidator.shape.statementFormatPlanCodeExpiry.transform(
      undefinedToNull,
    ),
  statementMessagePlanCode:
    accountTypeOverrideValidator.shape.statementMessagePlanCode.transform(
      undefinedToNull,
    ),
  statementMessagePlanCodeExpiry:
    accountTypeOverrideValidator.shape.statementMessagePlanCodeExpiry.transform(
      undefinedToNull,
    ),
})
const accountTypeOverrideDeprecated = z.object({
  accountCode: accountCodeDeprecated,
  analysisResultOptionsPlanCode: z.string().optional().nullable(),
  analysisResultOptionsPlanCodeExpiry: z.string().date().optional().nullable(),
  balanceRequirementDefinitionCode: z.string().optional().nullable(),
  balanceRequirementDefinitionCodeExpiry: z
    .string()
    .date()
    .optional()
    .nullable(),
  chargeAccountCode: z.string().optional().nullable(),
  earningsCreditDefinitionCode: z.string().optional().nullable(),
  earningsCreditDefinitionCodeExpiry: z.string().date().optional().nullable(),
  effectiveDate: z.string().date(),
  interestRequirementDefinitionCode: z.string().optional().nullable(),
  interestRequirementDefinitionCodeExpiry: z
    .string()
    .date()
    .optional()
    .nullable(),
  investableBalanceDefinitionCode: z.string().optional().nullable(),
  investableBalanceDefinitionCodeExpiry: z
    .string()
    .date()
    .optional()
    .nullable(),
  isOverrideAsSettlementAccount: z.boolean(),
  reserveRequirementDefinitionCode: z.string().optional().nullable(),
  reserveRequirementDefinitionCodeExpiry: z
    .string()
    .date()
    .optional()
    .nullable(),
  settlementCyclePlanCode: z.string().optional().nullable(),
  settlementCyclePlanCodeExpiry: z.string().date().optional().nullable(),
  statementCyclePlanCode: z.string().optional().nullable(),
  statementCyclePlanCodeExpiry: z.string().date().optional().nullable(),
  statementFormatPlanCode: z.string().optional().nullable(),
  statementFormatPlanCodeExpiry: z.string().date().optional().nullable(),
  statementMessagePlanCode: z.string().optional().nullable(),
  statementMessagePlanCodeExpiry: z.string().date().optional().nullable(),
})

const accountWithKeyValidator = z.object({
  accountNumber: z.string(),
  accountStatus: z.enum(['Z', 'B', 'C', 'P', 'X']).optional().nullable(),
  analysisAccountTypeCode: z.string(),
  applicationId: z.enum(['C', 'D']),
  bankNumber: z.string(),
  branchCode: z.string(),
  closeDate: z.string().date().optional().nullable(),
  costCenter: z.string(),
  currencyCode: z.string(),
  customerSpecificPricingIndicator: z.boolean().optional().nullable(),
  depositAccountTypeCode: z.string().optional().nullable(),
  depositCategory: z
    .enum(['A', 'C', 'D', 'L', 'M', 'N', 'R', 'S', 'P', 'T'])
    .optional()
    .nullable(),
  effectiveDate: z.string().date(),
  isKeyAccount: z.boolean().optional().nullable(),
  keyAccountCode: accountCodeValidator.optional().nullable(),
  openDate: z.string().date(),
  primaryOfficerCode: z.string(),
  processingIndicator: z.enum(['A', 'B']).optional().nullable(),
  secondaryOfficerCode: z.string().optional().nullable(),
  shortName: z.string(),
  treasuryOfficerCode: z.string().optional().nullable(),
})
const accountWithKeyTransformer = z.object({
  ...accountWithKeyValidator.shape,
  accountStatus:
    accountWithKeyValidator.shape.accountStatus.transform(undefinedToNull),
  closeDate: accountWithKeyValidator.shape.closeDate.transform(undefinedToNull),
  customerSpecificPricingIndicator:
    accountWithKeyValidator.shape.customerSpecificPricingIndicator.transform(
      undefinedToNull,
    ),
  depositAccountTypeCode:
    accountWithKeyValidator.shape.depositAccountTypeCode.transform(
      undefinedToNull,
    ),
  depositCategory:
    accountWithKeyValidator.shape.depositCategory.transform(undefinedToNull),
  isKeyAccount:
    accountWithKeyValidator.shape.isKeyAccount.transform(undefinedToNull),
  keyAccountCode: accountCodeTransformer
    .optional()
    .nullable()
    .transform(undefinedToNull),
  processingIndicator:
    accountWithKeyValidator.shape.processingIndicator.transform(
      undefinedToNull,
    ),
  secondaryOfficerCode:
    accountWithKeyValidator.shape.secondaryOfficerCode.transform(
      undefinedToNull,
    ),
  treasuryOfficerCode:
    accountWithKeyValidator.shape.treasuryOfficerCode.transform(
      undefinedToNull,
    ),
})
const accountWithKeyDeprecated = z.object({
  accountNumber: z.string(),
  accountStatus: z.enum(['Z', 'B', 'C', 'P', 'X']).optional().nullable(),
  analysisAccountTypeCode: z.string(),
  applicationId: z.enum(['C', 'D']),
  bankNumber: z.string(),
  branchCode: z.string(),
  closeDate: z.string().date().optional().nullable(),
  costCenter: z.string(),
  currencyCode: z.string(),
  customerSpecificPricingIndicator: z.boolean().optional().nullable(),
  depositAccountTypeCode: z.string().optional().nullable(),
  depositCategory: z
    .enum(['A', 'C', 'D', 'L', 'M', 'N', 'R', 'S', 'P', 'T'])
    .optional()
    .nullable(),
  effectiveDate: z.string().date(),
  isKeyAccount: z.boolean().optional().nullable(),
  keyAccountCode: accountCodeDeprecated,
  openDate: z.string().date(),
  primaryOfficerCode: z.string(),
  processingIndicator: z.enum(['A', 'B']).optional().nullable(),
  secondaryOfficerCode: z.string().optional().nullable(),
  shortName: z.string(),
  treasuryOfficerCode: z.string().optional().nullable(),
})

const addressValidator = z.object({
  accountCode: accountCodeValidator,
  addressLine2: z.string().optional().nullable(),
  addressLine3: z.string().optional().nullable(),
  addressLine4: z.string().optional().nullable(),
  addressLine5: z.string().optional().nullable(),
  addressLine6: z.string().optional().nullable(),
  addressLine7: z.string().optional().nullable(),
  addressNumber: z.number().int(),
  applicationId: z.enum(['A', 'D']),
  effectiveDate: z.string().date(),
  name: z.string().optional().nullable(),
})
const addressTransformer = z.object({
  ...addressValidator.shape,
  accountCode: accountCodeTransformer,
  addressLine2: addressValidator.shape.addressLine2.transform(undefinedToNull),
  addressLine3: addressValidator.shape.addressLine3.transform(undefinedToNull),
  addressLine4: addressValidator.shape.addressLine4.transform(undefinedToNull),
  addressLine5: addressValidator.shape.addressLine5.transform(undefinedToNull),
  addressLine6: addressValidator.shape.addressLine6.transform(undefinedToNull),
  addressLine7: addressValidator.shape.addressLine7.transform(undefinedToNull),
  name: addressValidator.shape.name.transform(undefinedToNull),
})
const addressDeprecated = z.object({
  accountCode: accountCodeDeprecated,
  addressLine2: z.string().optional().nullable(),
  addressLine3: z.string().optional().nullable(),
  addressLine4: z.string().optional().nullable(),
  addressLine5: z.string().optional().nullable(),
  addressLine6: z.string().optional().nullable(),
  addressLine7: z.string().optional().nullable(),
  addressNumber: z.number().int(),
  applicationId: z.enum(['A', 'D']),
  effectiveDate: z.string().date(),
  name: z.string().optional().nullable(),
})

const addressCodeValidator = z.object({
  accountCode: accountCodeValidator,
  addressNumber: z.number().int(),
  applicationId: z.enum(['A', 'D']),
})
const addressCodeTransformer = z.object({
  ...addressCodeValidator.shape,
  accountCode: accountCodeTransformer,
})
const addressCodeDeprecated = z.object({
  accountCode: accountCodeDeprecated,
  addressNumber: z.number().int(),
  applicationId: z.enum(['A', 'D']),
})

const addressCodeAvailabilityRequestValidator = z.object({
  accountApplicationId: z.enum(['C', 'D']),
  accountNumber: z.string(),
  addressApplicationId: z.enum(['A', 'D']),
  addressNumber: z.number().int(),
  bankNumber: z.string(),
})
const addressCodeAvailabilityRequestTransformer =
  addressCodeAvailabilityRequestValidator
const addressCodeAvailabilityRequestDeprecated = z.object({
  accountApplicationId: z.enum(['C', 'D']),
  accountNumber: z.string(),
  addressApplicationId: z.enum(['A', 'D']),
  addressNumber: z.number().int(),
  bankNumber: z.string(),
})

const addressCodeAvailabilityResponseValidator = z.object({
  available: z.boolean(),
  suggestedAddressNumber: z.number().int(),
})
const addressCodeAvailabilityResponseTransformer =
  addressCodeAvailabilityResponseValidator
const addressCodeAvailabilityResponseDeprecated = z.object({
  available: z.boolean(),
  suggestedAddressNumber: z.number().int(),
})

const addressRetrieveRequestValidator = z.object({
  accountApplicationId: z.enum(['C', 'D']),
  accountCode: accountCodeValidator.optional().nullable(),
  accountNumber: z.string(),
  addressApplicationId: z.enum(['A', 'D']),
  addressNumber: z.number().int(),
  bankNumber: z.string(),
  effectiveDate: z.string().date(),
})
const addressRetrieveRequestTransformer = z.object({
  ...addressRetrieveRequestValidator.shape,
  accountCode: accountCodeTransformer
    .optional()
    .nullable()
    .transform(undefinedToNull),
})
const addressRetrieveRequestDeprecated = z.object({
  accountApplicationId: z.enum(['C', 'D']),
  accountCode: accountCodeDeprecated,
  accountNumber: z.string(),
  addressApplicationId: z.enum(['A', 'D']),
  addressNumber: z.number().int(),
  bankNumber: z.string(),
  effectiveDate: z.string().date(),
})

const analysisResultOptionValidator = z.object({
  analysisChargeType: z.enum(['DIRECT_DEBIT', 'WAIVE']),
  analysisDirectDebitTrailer: z.string().optional().nullable(),
  code: z.string(),
  daysAfter: z.number().int().optional().nullable(),
  delay: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  effectiveDate: z.string().date(),
  excessCredits: z.enum(['WAIVE']),
  hardCharge: z.enum(['DIRECT_DEBIT', 'WAIVE']),
  hardDirectDebitTrailer: z.string().optional().nullable(),
  markdownRate: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  markdownStatementLabel: z.string().optional().nullable(),
  markupRate: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  markupStatementLabel: z.string().optional().nullable(),
  maxChargeWaiveAmount: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  minChargeWaiveAmount: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  name: z.string(),
  settlementOverrideDateEachMonth: z.number().int().optional().nullable(),
  settlementOverrideType: z.enum([
    'NO_OVERRIDE',
    'SAME_DATE_EACH_MONTH',
    'SPECIFIC_DAYS_AFTER_PRELIM_ANALYSIS',
  ]),
  waiveCycle: z.number().int().optional().nullable(),
})
const analysisResultOptionTransformer = z.object({
  ...analysisResultOptionValidator.shape,
  analysisDirectDebitTrailer:
    analysisResultOptionValidator.shape.analysisDirectDebitTrailer.transform(
      undefinedToNull,
    ),
  daysAfter:
    analysisResultOptionValidator.shape.daysAfter.transform(undefinedToNull),
  delay: analysisResultOptionValidator.shape.delay.transform(undefinedToNull),
  hardDirectDebitTrailer:
    analysisResultOptionValidator.shape.hardDirectDebitTrailer.transform(
      undefinedToNull,
    ),
  markdownRate:
    analysisResultOptionValidator.shape.markdownRate.transform(undefinedToNull),
  markdownStatementLabel:
    analysisResultOptionValidator.shape.markdownStatementLabel.transform(
      undefinedToNull,
    ),
  markupRate:
    analysisResultOptionValidator.shape.markupRate.transform(undefinedToNull),
  markupStatementLabel:
    analysisResultOptionValidator.shape.markupStatementLabel.transform(
      undefinedToNull,
    ),
  maxChargeWaiveAmount:
    analysisResultOptionValidator.shape.maxChargeWaiveAmount.transform(
      undefinedToNull,
    ),
  minChargeWaiveAmount:
    analysisResultOptionValidator.shape.minChargeWaiveAmount.transform(
      undefinedToNull,
    ),
  settlementOverrideDateEachMonth:
    analysisResultOptionValidator.shape.settlementOverrideDateEachMonth.transform(
      undefinedToNull,
    ),
  waiveCycle:
    analysisResultOptionValidator.shape.waiveCycle.transform(undefinedToNull),
})
const analysisResultOptionDeprecated = z.object({
  analysisChargeType: z.enum(['DIRECT_DEBIT', 'WAIVE']),
  analysisDirectDebitTrailer: z.string().optional().nullable(),
  code: z.string(),
  daysAfter: z.number().int().optional().nullable(),
  delay: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  effectiveDate: z.string().date(),
  excessCredits: z.enum(['WAIVE']),
  hardCharge: z.enum(['DIRECT_DEBIT', 'WAIVE']),
  hardDirectDebitTrailer: z.string().optional().nullable(),
  markdownRate: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  markdownStatementLabel: z.string().optional().nullable(),
  markupRate: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  markupStatementLabel: z.string().optional().nullable(),
  maxChargeWaiveAmount: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  minChargeWaiveAmount: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  name: z.string(),
  settlementOverrideDateEachMonth: z.number().int().optional().nullable(),
  settlementOverrideType: z.enum([
    'NO_OVERRIDE',
    'SAME_DATE_EACH_MONTH',
    'SPECIFIC_DAYS_AFTER_PRELIM_ANALYSIS',
  ]),
  waiveCycle: z.number().int().optional().nullable(),
})

const balanceRequirementDefinitionValidator = z.object({
  addOtherBalanceLabel: z.string().optional().nullable(),
  baseBalanceType: z.enum([
    'AVERAGE_COLLECTED',
    'AVERAGE_LEDGER',
    'AVERAGE_NEGATIVE_COLLECTED',
    'AVERAGE_NEGATIVE_LEDGER',
    'AVERAGE_POSITIVE_COLLECTED',
    'AVERAGE_POSITIVE_LEDGER',
    'AVERAGE_UNCOLLECTED_FUNDS',
    'COMPENSATING_BALANCE',
    'END_OF_MONTH_LEDGER',
    'INVESTABLE_BALANCE',
    'AVERAGE_FLOAT',
    'AVERAGE_CLEARINGHOUSE_FLOAT',
    'REQUIRED_BALANCE',
  ]),
  code: z.string(),
  effectiveDate: z.string().date(),
  name: z.string(),
  subtractCompensatingBalance: z.boolean(),
  subtractInterestPaidMonthToDate: z.boolean(),
  subtractOtherBalanceLabel: z.string().optional().nullable(),
})
const balanceRequirementDefinitionTransformer = z.object({
  ...balanceRequirementDefinitionValidator.shape,
  addOtherBalanceLabel:
    balanceRequirementDefinitionValidator.shape.addOtherBalanceLabel.transform(
      undefinedToNull,
    ),
  subtractOtherBalanceLabel:
    balanceRequirementDefinitionValidator.shape.subtractOtherBalanceLabel.transform(
      undefinedToNull,
    ),
})
const balanceRequirementDefinitionDeprecated = z.object({
  addOtherBalanceLabel: z.string().optional().nullable(),
  baseBalanceType: z.enum([
    'AVERAGE_COLLECTED',
    'AVERAGE_LEDGER',
    'AVERAGE_NEGATIVE_COLLECTED',
    'AVERAGE_NEGATIVE_LEDGER',
    'AVERAGE_POSITIVE_COLLECTED',
    'AVERAGE_POSITIVE_LEDGER',
    'AVERAGE_UNCOLLECTED_FUNDS',
    'COMPENSATING_BALANCE',
    'END_OF_MONTH_LEDGER',
    'INVESTABLE_BALANCE',
    'AVERAGE_FLOAT',
    'AVERAGE_CLEARINGHOUSE_FLOAT',
    'REQUIRED_BALANCE',
  ]),
  code: z.string(),
  effectiveDate: z.string().date(),
  name: z.string(),
  subtractCompensatingBalance: z.boolean(),
  subtractInterestPaidMonthToDate: z.boolean(),
  subtractOtherBalanceLabel: z.string().optional().nullable(),
})

const balanceTierValidator = z.object({
  indexAdjustmentRate: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  indexRate: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  maxTierExclusive: z.number().transform((v) => v.toFixed(4)),
  minTierInclusive: z.number().transform((v) => v.toFixed(4)),
})
const balanceTierTransformer = z.object({
  ...balanceTierValidator.shape,
  indexAdjustmentRate:
    balanceTierValidator.shape.indexAdjustmentRate.transform(undefinedToNull),
  indexRate: balanceTierValidator.shape.indexRate.transform(undefinedToNull),
})
const balanceTierDeprecated = z.object({
  indexAdjustmentRate: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  indexRate: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  maxTierExclusive: z.number().transform((v) => v.toFixed(4)),
  minTierInclusive: z.number().transform((v) => v.toFixed(4)),
})

const bankOptionsValidator = z.object({
  accountNumberDigits: z.number().int(),
  accountNumberMasking: z.boolean(),
  accountNumberPattern: z.string(),
  assignDefaultStatementPackageToDepositAccount: z.boolean(),
  automaticPrelimAnalysis: z.boolean(),
  balanceCycleDays: z.enum(['ACTUAL_DAYS_IN_MONTH', 'OPEN_DAYS_IN_MONTH']),
  calculatingBalanceFeeBasis: z.enum(['360', '365', '366']),
  calculatingEarningsCreditBasis: z.enum(['360', '365', '366']),
  code: z.string(),
  copyAccountTypeOfKeyAccount: z.boolean(),
  copyUserFieldsFromKeyAccount: z.boolean(),
  defaultCurrency: z.enum(['US_DOLLARS']),
  defaultTermInMonthsForPromoAndOverrides: z.number().int(),
  earningsCycleDays: z.enum([
    'ACTUAL_DAYS_IN_MONTH',
    'OPEN_DAYS_IN_MONTH',
    'THIRTY',
  ]),
  effectiveDate: z.string().date(),
  finalAnalysis: z.enum(['SAME_DATE_EACH_MONTH', 'SPECIFIC_DAYS_POST_PRELIM']),
  finalAnalysisDays: z.number().int(),
  name: z.string(),
  prelimAnalysis: z.enum([
    'LastBusinessDayPriorMonth',
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9',
    '10',
    '11',
    '12',
    '13',
    '14',
    '15',
    '16',
    '17',
    '18',
    '19',
    '20',
    '21',
    '22',
    '23',
    '24',
    '25',
    '26',
    '27',
  ]),
  retentionMonths: z.number().int(),
  statementArchivingFrequency: z.enum([
    'AT_FINAL_AND_REANALYSIS',
    'AT_FINAL',
    'NOT_ARCHIVED',
  ]),
})
const bankOptionsTransformer = bankOptionsValidator
const bankOptionsDeprecated = z.object({
  accountNumberDigits: z.number().int(),
  accountNumberMasking: z.boolean(),
  accountNumberPattern: z.string(),
  assignDefaultStatementPackageToDepositAccount: z.boolean(),
  automaticPrelimAnalysis: z.boolean(),
  balanceCycleDays: z.enum(['ACTUAL_DAYS_IN_MONTH', 'OPEN_DAYS_IN_MONTH']),
  calculatingBalanceFeeBasis: z.enum(['360', '365', '366']),
  calculatingEarningsCreditBasis: z.enum(['360', '365', '366']),
  code: z.string(),
  copyAccountTypeOfKeyAccount: z.boolean(),
  copyUserFieldsFromKeyAccount: z.boolean(),
  defaultCurrency: z.enum(['US_DOLLARS']),
  defaultTermInMonthsForPromoAndOverrides: z.number().int(),
  earningsCycleDays: z.enum([
    'ACTUAL_DAYS_IN_MONTH',
    'OPEN_DAYS_IN_MONTH',
    'THIRTY',
  ]),
  effectiveDate: z.string().date(),
  finalAnalysis: z.enum(['SAME_DATE_EACH_MONTH', 'SPECIFIC_DAYS_POST_PRELIM']),
  finalAnalysisDays: z.number().int(),
  name: z.string(),
  prelimAnalysis: z.enum([
    'LastBusinessDayPriorMonth',
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9',
    '10',
    '11',
    '12',
    '13',
    '14',
    '15',
    '16',
    '17',
    '18',
    '19',
    '20',
    '21',
    '22',
    '23',
    '24',
    '25',
    '26',
    '27',
  ]),
  retentionMonths: z.number().int(),
  statementArchivingFrequency: z.enum([
    'AT_FINAL_AND_REANALYSIS',
    'AT_FINAL',
    'NOT_ARCHIVED',
  ]),
})

const branchValidator = z.object({
  abaNumbers: z.array(z.string()),
  addressLine1: z.string(),
  addressLine2: z.string(),
  addressLine3: z.string(),
  bankNumber: z.string(),
  branchName: z.string(),
  code: z.string(),
  correspondentAccountNumber: z.string().optional().nullable(),
  effectiveDate: z.string().date(),
  phoneNumber: z.string(),
})
const branchTransformer = z.object({
  ...branchValidator.shape,
  correspondentAccountNumber:
    branchValidator.shape.correspondentAccountNumber.transform(undefinedToNull),
})
const branchDeprecated = z.object({
  abaNumbers: z.array(z.string()),
  addressLine1: z.string(),
  addressLine2: z.string(),
  addressLine3: z.string(),
  bankNumber: z.string(),
  branchName: z.string(),
  code: z.string(),
  correspondentAccountNumber: z.string().optional().nullable(),
  effectiveDate: z.string().date(),
  phoneNumber: z.string(),
})

const codeRequestBodyValidator = z.object({
  code: z.string(),
})
const codeRequestBodyTransformer = codeRequestBodyValidator
const codeRequestBodyDeprecated = z.object({
  code: z.string(),
})

const codesAndEffectiveDateRequestBodyValidator = z.object({
  codes: z.array(z.string()),
  effectiveDate: z.string().date(),
})
const codesAndEffectiveDateRequestBodyTransformer =
  codesAndEffectiveDateRequestBodyValidator
const codesAndEffectiveDateRequestBodyDeprecated = z.object({
  codes: z.array(z.string()),
  effectiveDate: z.string().date(),
})

const statementPackageCreateUpdateRequestValidator = z.object({
  accountApplicationId: z.enum(['C', 'D']),
  accountNumber: z.string(),
  addressAccountApplicationId: z.enum(['C', 'D']),
  addressAccountNumber: z.string(),
  addressApplicationId: z.enum(['A', 'D']),
  addressNumber: z.number().int(),
  bankNumber: z.string(),
  effectiveDate: z.string().date(),
  packageDelivery: z.enum([
    'ELECTRONIC',
    'ELECTRONIC_AND_PRINT',
    'PRINT',
    'NO_STATEMENT',
  ]),
  packageType: z.enum([
    'ALL_ACCOUNTS',
    'COMPOSITE_ACCOUNTS',
    'DEPOSIT_ACCOUNTS',
    'SELECTED_ACCOUNTS',
  ]),
  selectedAccounts: z.array(accountCodeValidator),
  statementPackageNumber: z.number().int(),
})
const statementPackageCreateUpdateRequestTransformer = z.object({
  ...statementPackageCreateUpdateRequestValidator.shape,
  selectedAccounts: z.array(accountCodeTransformer),
})
const statementPackageCreateUpdateRequestDeprecated = z.object({
  accountApplicationId: z.enum(['C', 'D']),
  accountNumber: z.string(),
  addressAccountApplicationId: z.enum(['C', 'D']),
  addressAccountNumber: z.string(),
  addressApplicationId: z.enum(['A', 'D']),
  addressNumber: z.number().int(),
  bankNumber: z.string(),
  effectiveDate: z.string().date(),
  packageDelivery: z.enum([
    'ELECTRONIC',
    'ELECTRONIC_AND_PRINT',
    'PRINT',
    'NO_STATEMENT',
  ]),
  packageType: z.enum([
    'ALL_ACCOUNTS',
    'COMPOSITE_ACCOUNTS',
    'DEPOSIT_ACCOUNTS',
    'SELECTED_ACCOUNTS',
  ]),
  selectedAccounts: z.array(accountCodeDeprecated),
  statementPackageNumber: z.number().int(),
})

const userFieldSelectionValidator = z.object({
  accountNumber: z.string(),
  applicationId: z.enum(['C', 'D']),
  applyToChildAccounts: z.boolean(),
  bankNumber: z.string(),
  booleanValue: z.boolean().optional().nullable(),
  dropdownOptionCode: z.number().int().optional().nullable(),
  effectiveDate: z.string().date(),
  expiry: z.string().date().optional().nullable(),
  freeformValue: z.string().optional().nullable(),
  isUnset: z.boolean(),
  userFieldCode: z.number().int(),
})
const userFieldSelectionTransformer = z.object({
  ...userFieldSelectionValidator.shape,
  booleanValue:
    userFieldSelectionValidator.shape.booleanValue.transform(undefinedToNull),
  dropdownOptionCode:
    userFieldSelectionValidator.shape.dropdownOptionCode.transform(
      undefinedToNull,
    ),
  expiry: userFieldSelectionValidator.shape.expiry.transform(undefinedToNull),
  freeformValue:
    userFieldSelectionValidator.shape.freeformValue.transform(undefinedToNull),
})
const userFieldSelectionDeprecated = z.object({
  accountNumber: z.string(),
  applicationId: z.enum(['C', 'D']),
  applyToChildAccounts: z.boolean(),
  bankNumber: z.string(),
  booleanValue: z.boolean().optional().nullable(),
  dropdownOptionCode: z.number().int().optional().nullable(),
  effectiveDate: z.string().date(),
  expiry: z.string().date().optional().nullable(),
  freeformValue: z.string().optional().nullable(),
  isUnset: z.boolean(),
  userFieldCode: z.number().int(),
})

const compositeAccountCreateRequestValidator = z.object({
  account: accountValidator,
  accountTypeOverride: accountTypeOverrideValidator.optional().nullable(),
  addresses: z.array(addressValidator).optional().nullable(),
  childAccountCodes: z.array(accountCodeValidator),
  keyChildAccountCode: accountCodeValidator.optional().nullable(),
  statementPackages: z
    .array(statementPackageCreateUpdateRequestValidator)
    .optional()
    .nullable(),
  userFieldSelections: z
    .array(userFieldSelectionValidator)
    .optional()
    .nullable(),
})
const compositeAccountCreateRequestTransformer = z.object({
  ...compositeAccountCreateRequestValidator.shape,
  account: accountTransformer,
  accountTypeOverride: accountTypeOverrideTransformer
    .optional()
    .nullable()
    .transform(undefinedToNull),
  addresses: z
    .array(addressTransformer)
    .optional()
    .nullable()
    .transform(undefinedToNull),
  childAccountCodes: z.array(accountCodeTransformer),
  keyChildAccountCode: accountCodeTransformer
    .optional()
    .nullable()
    .transform(undefinedToNull),
  statementPackages: z
    .array(statementPackageCreateUpdateRequestTransformer)
    .optional()
    .nullable()
    .transform(undefinedToNull),
  userFieldSelections: z
    .array(userFieldSelectionTransformer)
    .optional()
    .nullable()
    .transform(undefinedToNull),
})
const compositeAccountCreateRequestDeprecated = z.object({
  account: accountDeprecated,
  accountTypeOverride: accountTypeOverrideDeprecated,
  addresses: z.array(addressDeprecated),
  childAccountCodes: z.array(accountCodeDeprecated),
  keyChildAccountCode: accountCodeDeprecated,
  statementPackages: z.array(statementPackageCreateUpdateRequestDeprecated),
  userFieldSelections: z.array(userFieldSelectionDeprecated),
})

const serviceValidator = z.object({
  code: z.string(),
  description: z.string().optional().nullable(),
  domesticAfpCode: z.string().optional().nullable(),
  effectiveDate: z.string().date(),
  globalAfpCode: z.string().optional().nullable(),
  internalNote: z.string().optional().nullable(),
  serviceType: z.enum([
    'VOLUME_BASED',
    'RECURRING',
    'BALANCE_BASED',
    'PRE_PRICED',
    'SERVICE_SET',
  ]),
})
const serviceTransformer = z.object({
  ...serviceValidator.shape,
  description: serviceValidator.shape.description.transform(undefinedToNull),
  domesticAfpCode:
    serviceValidator.shape.domesticAfpCode.transform(undefinedToNull),
  globalAfpCode:
    serviceValidator.shape.globalAfpCode.transform(undefinedToNull),
  internalNote: serviceValidator.shape.internalNote.transform(undefinedToNull),
})
const serviceDeprecated = z.object({
  code: z.string(),
  description: z.string().optional().nullable(),
  domesticAfpCode: z.string().optional().nullable(),
  effectiveDate: z.string().date(),
  globalAfpCode: z.string().optional().nullable(),
  internalNote: z.string().optional().nullable(),
  serviceType: z.enum([
    'VOLUME_BASED',
    'RECURRING',
    'BALANCE_BASED',
    'PRE_PRICED',
    'SERVICE_SET',
  ]),
})

const servicePriceValidator = z.object({
  applyServiceTo: z
    .enum(['SELECT_DEPOSIT_ACCOUNTS', 'ALL_DEPOSIT_ACCOUNTS'])
    .optional()
    .nullable(),
  balanceDivisor: z.number().int().optional().nullable(),
  balanceType: z
    .enum([
      'AVERAGE_COLLECTED',
      'AVERAGE_LEDGER',
      'AVERAGE_NEGATIVE_COLLECTED',
      'AVERAGE_NEGATIVE_LEDGER',
      'AVERAGE_POSITIVE_COLLECTED',
      'AVERAGE_POSITIVE_LEDGER',
      'AVERAGE_UNCOLLECTED_FUNDS',
      'COMPENSATING_BALANCE',
      'END_OF_MONTH_LEDGER',
      'INVESTABLE_BALANCE',
      'AVERAGE_FLOAT',
      'AVERAGE_CLEARINGHOUSE_FLOAT',
      'REQUIRED_BALANCE',
    ])
    .optional()
    .nullable(),
  baseFee: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  basisDays: z.enum(['360', '365', '366']).optional().nullable(),
  costType: z.enum(['NO_COST', 'UNIT_COST', 'FLAT_COST']).optional().nullable(),
  costValue: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  currency: z.string().optional().nullable(),
  cycleDefinitionCode: z.string().optional().nullable(),
  disposition: z
    .enum(['ANALYSED', 'HARD_CHARGE', 'IMMEDIATE_CHARGE', 'WAIVED'])
    .optional()
    .nullable(),
  effectiveDate: z.string().date(),
  expirationDate: z.string().date().optional().nullable(),
  includeReferenceInformationOnStatements: z.boolean().optional().nullable(),
  indexAdjustment: z
    .number()
    .transform((v) => v.toFixed(6))
    .optional()
    .nullable(),
  indexMultiplier: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  indexRateCode: z.string().optional().nullable(),
  lastFinalsDate: z.string().date().optional().nullable(),
  maximumFee: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  minimumFee: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  priceType: z
    .enum([
      'NOT_PRICED',
      'UNIT_PRICED',
      'FLAT_FEE',
      'THRESHOLD_TIER',
      'PARTITIONED_TIER',
      'PERCENTAGE',
      'OUTSIDE_PRICE',
      'INDEXED',
    ])
    .optional()
    .nullable(),
  priceValue: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  pricingHierarchyEntryCode: z.string(),
  pricingHierarchyEntryType: z.enum(['STANDARD', 'PRICE_LIST', 'OVERRIDE']),
  serviceCode: z.string(),
  subjectToDiscountOrPremium: z.boolean().optional().nullable(),
  tierMaxBalanceExclusive: z
    .number()
    .transform((v) => v.toFixed(2))
    .optional()
    .nullable(),
  tierMaxVolumeExclusive: z.number().int().optional().nullable(),
  tierMinBalanceInclusive: z
    .number()
    .transform((v) => v.toFixed(2))
    .optional()
    .nullable(),
  tierMinVolumeInclusive: z.number().int().optional().nullable(),
  tierNumber: z.number().int(),
  tierPriceType: z
    .enum(['UNIT_PRICED', 'FLAT_FEE', 'PERCENTAGE', 'INDEXED'])
    .optional()
    .nullable(),
  units: z.number().int().optional().nullable(),
})
const servicePriceTransformer = z.object({
  ...servicePriceValidator.shape,
  applyServiceTo:
    servicePriceValidator.shape.applyServiceTo.transform(undefinedToNull),
  balanceDivisor:
    servicePriceValidator.shape.balanceDivisor.transform(undefinedToNull),
  balanceType:
    servicePriceValidator.shape.balanceType.transform(undefinedToNull),
  baseFee: servicePriceValidator.shape.baseFee.transform(undefinedToNull),
  basisDays: servicePriceValidator.shape.basisDays.transform(undefinedToNull),
  costType: servicePriceValidator.shape.costType.transform(undefinedToNull),
  costValue: servicePriceValidator.shape.costValue.transform(undefinedToNull),
  currency: servicePriceValidator.shape.currency.transform(undefinedToNull),
  cycleDefinitionCode:
    servicePriceValidator.shape.cycleDefinitionCode.transform(undefinedToNull),
  disposition:
    servicePriceValidator.shape.disposition.transform(undefinedToNull),
  expirationDate:
    servicePriceValidator.shape.expirationDate.transform(undefinedToNull),
  includeReferenceInformationOnStatements:
    servicePriceValidator.shape.includeReferenceInformationOnStatements.transform(
      undefinedToNull,
    ),
  indexAdjustment:
    servicePriceValidator.shape.indexAdjustment.transform(undefinedToNull),
  indexMultiplier:
    servicePriceValidator.shape.indexMultiplier.transform(undefinedToNull),
  indexRateCode:
    servicePriceValidator.shape.indexRateCode.transform(undefinedToNull),
  lastFinalsDate:
    servicePriceValidator.shape.lastFinalsDate.transform(undefinedToNull),
  maximumFee: servicePriceValidator.shape.maximumFee.transform(undefinedToNull),
  minimumFee: servicePriceValidator.shape.minimumFee.transform(undefinedToNull),
  priceType: servicePriceValidator.shape.priceType.transform(undefinedToNull),
  priceValue: servicePriceValidator.shape.priceValue.transform(undefinedToNull),
  subjectToDiscountOrPremium:
    servicePriceValidator.shape.subjectToDiscountOrPremium.transform(
      undefinedToNull,
    ),
  tierMaxBalanceExclusive:
    servicePriceValidator.shape.tierMaxBalanceExclusive.transform(
      undefinedToNull,
    ),
  tierMaxVolumeExclusive:
    servicePriceValidator.shape.tierMaxVolumeExclusive.transform(
      undefinedToNull,
    ),
  tierMinBalanceInclusive:
    servicePriceValidator.shape.tierMinBalanceInclusive.transform(
      undefinedToNull,
    ),
  tierMinVolumeInclusive:
    servicePriceValidator.shape.tierMinVolumeInclusive.transform(
      undefinedToNull,
    ),
  tierPriceType:
    servicePriceValidator.shape.tierPriceType.transform(undefinedToNull),
  units: servicePriceValidator.shape.units.transform(undefinedToNull),
})
const servicePriceDeprecated = z.object({
  applyServiceTo: z
    .enum(['SELECT_DEPOSIT_ACCOUNTS', 'ALL_DEPOSIT_ACCOUNTS'])
    .optional()
    .nullable(),
  balanceDivisor: z.number().int().optional().nullable(),
  balanceType: z
    .enum([
      'AVERAGE_COLLECTED',
      'AVERAGE_LEDGER',
      'AVERAGE_NEGATIVE_COLLECTED',
      'AVERAGE_NEGATIVE_LEDGER',
      'AVERAGE_POSITIVE_COLLECTED',
      'AVERAGE_POSITIVE_LEDGER',
      'AVERAGE_UNCOLLECTED_FUNDS',
      'COMPENSATING_BALANCE',
      'END_OF_MONTH_LEDGER',
      'INVESTABLE_BALANCE',
      'AVERAGE_FLOAT',
      'AVERAGE_CLEARINGHOUSE_FLOAT',
      'REQUIRED_BALANCE',
    ])
    .optional()
    .nullable(),
  baseFee: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  basisDays: z.enum(['360', '365', '366']).optional().nullable(),
  costType: z.enum(['NO_COST', 'UNIT_COST', 'FLAT_COST']).optional().nullable(),
  costValue: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  currency: z.string().optional().nullable(),
  cycleDefinitionCode: z.string().optional().nullable(),
  disposition: z
    .enum(['ANALYSED', 'HARD_CHARGE', 'IMMEDIATE_CHARGE', 'WAIVED'])
    .optional()
    .nullable(),
  effectiveDate: z.string().date(),
  expirationDate: z.string().date().optional().nullable(),
  includeReferenceInformationOnStatements: z.boolean().optional().nullable(),
  indexAdjustment: z
    .number()
    .transform((v) => v.toFixed(6))
    .optional()
    .nullable(),
  indexMultiplier: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  indexRateCode: z.string().optional().nullable(),
  lastFinalsDate: z.string().date().optional().nullable(),
  maximumFee: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  minimumFee: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  priceType: z
    .enum([
      'NOT_PRICED',
      'UNIT_PRICED',
      'FLAT_FEE',
      'THRESHOLD_TIER',
      'PARTITIONED_TIER',
      'PERCENTAGE',
      'OUTSIDE_PRICE',
      'INDEXED',
    ])
    .optional()
    .nullable(),
  priceValue: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  pricingHierarchyEntryCode: z.string(),
  pricingHierarchyEntryType: z.enum(['STANDARD', 'PRICE_LIST', 'OVERRIDE']),
  serviceCode: z.string(),
  subjectToDiscountOrPremium: z.boolean().optional().nullable(),
  tierMaxBalanceExclusive: z
    .number()
    .transform((v) => v.toFixed(2))
    .optional()
    .nullable(),
  tierMaxVolumeExclusive: z.number().int().optional().nullable(),
  tierMinBalanceInclusive: z
    .number()
    .transform((v) => v.toFixed(2))
    .optional()
    .nullable(),
  tierMinVolumeInclusive: z.number().int().optional().nullable(),
  tierNumber: z.number().int(),
  tierPriceType: z
    .enum(['UNIT_PRICED', 'FLAT_FEE', 'PERCENTAGE', 'INDEXED'])
    .optional()
    .nullable(),
  units: z.number().int().optional().nullable(),
})

const createOrUpdateServiceValidator = z.object({
  service: serviceValidator,
  serviceCategoryCode: z.string().optional().nullable(),
  serviceCodes: z.array(z.string()).optional().nullable(),
  servicePrice: z.array(servicePriceValidator),
})
const createOrUpdateServiceTransformer = z.object({
  ...createOrUpdateServiceValidator.shape,
  service: serviceTransformer,
  serviceCategoryCode:
    createOrUpdateServiceValidator.shape.serviceCategoryCode.transform(
      undefinedToNull,
    ),
  serviceCodes:
    createOrUpdateServiceValidator.shape.serviceCodes.transform(
      undefinedToNull,
    ),
  servicePrice: z.array(servicePriceTransformer),
})
const createOrUpdateServiceDeprecated = z.object({
  service: serviceDeprecated,
  serviceCategoryCode: z.string().optional().nullable(),
  serviceCodes: z.array(z.string()),
  servicePrice: z.array(servicePriceDeprecated),
})

const serviceCategoryValidator = z.object({
  code: z.string(),
  effectiveDate: z.string().date(),
  name: z.string(),
})
const serviceCategoryTransformer = serviceCategoryValidator
const serviceCategoryDeprecated = z.object({
  code: z.string(),
  effectiveDate: z.string().date(),
  name: z.string(),
})

const createServiceCategoryValidator = z.object({
  parentServiceCategoryCode: z.string().optional().nullable(),
  serviceCategory: serviceCategoryValidator,
})
const createServiceCategoryTransformer = z.object({
  ...createServiceCategoryValidator.shape,
  parentServiceCategoryCode:
    createServiceCategoryValidator.shape.parentServiceCategoryCode.transform(
      undefinedToNull,
    ),
  serviceCategory: serviceCategoryTransformer,
})
const createServiceCategoryDeprecated = z.object({
  parentServiceCategoryCode: z.string().optional().nullable(),
  serviceCategory: serviceCategoryDeprecated,
})

const cycleDefinitionValidator = z.object({
  code: z.string(),
  cycleType: z.enum(['STATEMENT', 'SETTLEMENT']),
  description: z.string(),
  effectiveDate: z.string().date(),
  includedMonths: z
    .array(
      z.enum([
        'JANUARY',
        'FEBRUARY',
        'MARCH',
        'APRIL',
        'MAY',
        'JUNE',
        'JULY',
        'AUGUST',
        'SEPTEMBER',
        'OCTOBER',
        'NOVEMBER',
        'DECEMBER',
      ]),
    )
    .optional()
    .nullable(),
  isAprilSelected: z.boolean().optional().nullable(),
  isAugustSelected: z.boolean().optional().nullable(),
  isDecemberSelected: z.boolean().optional().nullable(),
  isFebruarySelected: z.boolean().optional().nullable(),
  isJanuarySelected: z.boolean().optional().nullable(),
  isJulySelected: z.boolean().optional().nullable(),
  isJuneSelected: z.boolean().optional().nullable(),
  isMarchSelected: z.boolean().optional().nullable(),
  isMaySelected: z.boolean().optional().nullable(),
  isNovemberSelected: z.boolean().optional().nullable(),
  isOctoberSelected: z.boolean().optional().nullable(),
  isSeptemberSelected: z.boolean().optional().nullable(),
})
const cycleDefinitionTransformer = z.object({
  ...cycleDefinitionValidator.shape,
  includedMonths:
    cycleDefinitionValidator.shape.includedMonths.transform(undefinedToNull),
  isAprilSelected:
    cycleDefinitionValidator.shape.isAprilSelected.transform(undefinedToNull),
  isAugustSelected:
    cycleDefinitionValidator.shape.isAugustSelected.transform(undefinedToNull),
  isDecemberSelected:
    cycleDefinitionValidator.shape.isDecemberSelected.transform(
      undefinedToNull,
    ),
  isFebruarySelected:
    cycleDefinitionValidator.shape.isFebruarySelected.transform(
      undefinedToNull,
    ),
  isJanuarySelected:
    cycleDefinitionValidator.shape.isJanuarySelected.transform(undefinedToNull),
  isJulySelected:
    cycleDefinitionValidator.shape.isJulySelected.transform(undefinedToNull),
  isJuneSelected:
    cycleDefinitionValidator.shape.isJuneSelected.transform(undefinedToNull),
  isMarchSelected:
    cycleDefinitionValidator.shape.isMarchSelected.transform(undefinedToNull),
  isMaySelected:
    cycleDefinitionValidator.shape.isMaySelected.transform(undefinedToNull),
  isNovemberSelected:
    cycleDefinitionValidator.shape.isNovemberSelected.transform(
      undefinedToNull,
    ),
  isOctoberSelected:
    cycleDefinitionValidator.shape.isOctoberSelected.transform(undefinedToNull),
  isSeptemberSelected:
    cycleDefinitionValidator.shape.isSeptemberSelected.transform(
      undefinedToNull,
    ),
})
const cycleDefinitionDeprecated = z.object({
  code: z.string(),
  cycleType: z.enum(['STATEMENT', 'SETTLEMENT']),
  description: z.string(),
  effectiveDate: z.string().date(),
  includedMonths: z.array(
    z.enum([
      'JANUARY',
      'FEBRUARY',
      'MARCH',
      'APRIL',
      'MAY',
      'JUNE',
      'JULY',
      'AUGUST',
      'SEPTEMBER',
      'OCTOBER',
      'NOVEMBER',
      'DECEMBER',
    ]),
  ),
  isAprilSelected: z.boolean().optional().nullable(),
  isAugustSelected: z.boolean().optional().nullable(),
  isDecemberSelected: z.boolean().optional().nullable(),
  isFebruarySelected: z.boolean().optional().nullable(),
  isJanuarySelected: z.boolean().optional().nullable(),
  isJulySelected: z.boolean().optional().nullable(),
  isJuneSelected: z.boolean().optional().nullable(),
  isMarchSelected: z.boolean().optional().nullable(),
  isMaySelected: z.boolean().optional().nullable(),
  isNovemberSelected: z.boolean().optional().nullable(),
  isOctoberSelected: z.boolean().optional().nullable(),
  isSeptemberSelected: z.boolean().optional().nullable(),
})

const dailyBalanceHistoryValidator = z.object({
  accountNumber: z.string(),
  applicationId: z.enum(['C', 'D']),
  bankNumber: z.string(),
  clearingHouseFundsFloat: z.number().transform((v) => v.toFixed(4)),
  floatBalance: z.number().transform((v) => v.toFixed(4)),
  isBusinessDay: z.boolean(),
  ledgerBalance: z.number().transform((v) => v.toFixed(4)),
  processDate: z.string().date(),
  todaysAccruedInterest: z.number().transform((v) => v.toFixed(4)),
  todaysPostedInterest: z.number().transform((v) => v.toFixed(4)),
})
const dailyBalanceHistoryTransformer = dailyBalanceHistoryValidator
const dailyBalanceHistoryDeprecated = z.object({
  accountNumber: z.string(),
  applicationId: z.enum(['C', 'D']),
  bankNumber: z.string(),
  clearingHouseFundsFloat: z.number().transform((v) => v.toFixed(4)),
  floatBalance: z.number().transform((v) => v.toFixed(4)),
  isBusinessDay: z.boolean(),
  ledgerBalance: z.number().transform((v) => v.toFixed(4)),
  processDate: z.string().date(),
  todaysAccruedInterest: z.number().transform((v) => v.toFixed(4)),
  todaysPostedInterest: z.number().transform((v) => v.toFixed(4)),
})

const demographicCriteriaIdValidator = z.object({
  codeKey: z.string().optional().nullable(),
  criteriaCode: z.string(),
  keyType: z.enum(['USERFIELD', 'ACCOUNTFIELD']),
  priceListCode: z.string(),
  stringKey: z.string().optional().nullable(),
  valueCode: z.string(),
})
const demographicCriteriaIdTransformer = z.object({
  ...demographicCriteriaIdValidator.shape,
  codeKey:
    demographicCriteriaIdValidator.shape.codeKey.transform(undefinedToNull),
  stringKey:
    demographicCriteriaIdValidator.shape.stringKey.transform(undefinedToNull),
})
const demographicCriteriaIdDeprecated = z.object({
  codeKey: z.string().optional().nullable(),
  criteriaCode: z.string(),
  keyType: z.enum(['USERFIELD', 'ACCOUNTFIELD']),
  priceListCode: z.string(),
  stringKey: z.string().optional().nullable(),
  valueCode: z.string(),
})

const demographicCriteriaValidator = z.object({
  booleanValue: z.boolean().optional().nullable(),
  code: z.string(),
  codeKey: z.string().optional().nullable(),
  codeValue: z.string().optional().nullable(),
  criteriaCode: z.string(),
  demographicCriteriaId: demographicCriteriaIdValidator,
  effectiveDate: z.string().date(),
  keyType: z.enum(['USERFIELD', 'ACCOUNTFIELD']),
  priceListCode: z.string(),
  stringKey: z.string().optional().nullable(),
  stringValue: z.string().optional().nullable(),
  valueCode: z.string(),
})
const demographicCriteriaTransformer = z.object({
  ...demographicCriteriaValidator.shape,
  booleanValue:
    demographicCriteriaValidator.shape.booleanValue.transform(undefinedToNull),
  codeKey:
    demographicCriteriaValidator.shape.codeKey.transform(undefinedToNull),
  codeValue:
    demographicCriteriaValidator.shape.codeValue.transform(undefinedToNull),
  demographicCriteriaId: demographicCriteriaIdTransformer,
  stringKey:
    demographicCriteriaValidator.shape.stringKey.transform(undefinedToNull),
  stringValue:
    demographicCriteriaValidator.shape.stringValue.transform(undefinedToNull),
})
const demographicCriteriaDeprecated = z.object({
  booleanValue: z.boolean().optional().nullable(),
  code: z.string(),
  codeKey: z.string().optional().nullable(),
  codeValue: z.string().optional().nullable(),
  criteriaCode: z.string(),
  demographicCriteriaId: demographicCriteriaIdDeprecated,
  effectiveDate: z.string().date(),
  keyType: z.enum(['USERFIELD', 'ACCOUNTFIELD']),
  priceListCode: z.string(),
  stringKey: z.string().optional().nullable(),
  stringValue: z.string().optional().nullable(),
  valueCode: z.string(),
})

const demographicPriceListValidator = z.object({
  code: z.string(),
  currency: z.string(),
  effectiveDate: z.string().date(),
  isLeadPriceList: z.boolean(),
  name: z.string().optional().nullable(),
})
const demographicPriceListTransformer = z.object({
  ...demographicPriceListValidator.shape,
  name: demographicPriceListValidator.shape.name.transform(undefinedToNull),
})
const demographicPriceListDeprecated = z.object({
  code: z.string(),
  currency: z.string(),
  effectiveDate: z.string().date(),
  isLeadPriceList: z.boolean(),
  name: z.string().optional().nullable(),
})

const demographicPriceListRankingValidator = z.object({
  effectiveDate: z.string().date(),
  ranking: z.string(),
})
const demographicPriceListRankingTransformer =
  demographicPriceListRankingValidator
const demographicPriceListRankingDeprecated = z.object({
  effectiveDate: z.string().date(),
  ranking: z.string(),
})

const demographicPriceListWithCountValidator = z.object({
  code: z.string(),
  currency: z.string(),
  effectiveDate: z.string().date(),
  isLeadPriceList: z.boolean(),
  name: z.string().optional().nullable(),
  serviceCount: z.number().int(),
})
const demographicPriceListWithCountTransformer = z.object({
  ...demographicPriceListWithCountValidator.shape,
  name: demographicPriceListWithCountValidator.shape.name.transform(
    undefinedToNull,
  ),
})
const demographicPriceListWithCountDeprecated = z.object({
  code: z.string(),
  currency: z.string(),
  effectiveDate: z.string().date(),
  isLeadPriceList: z.boolean(),
  name: z.string().optional().nullable(),
  serviceCount: z.number().int(),
})

const demographicUpdateRecordEntityValidator = z.object({
  aasAccountType: z.string().optional().nullable(),
  accountNumber: z.string().optional().nullable(),
  accountStatus: z.string().optional().nullable(),
  alternatePrimaryOfficer: z.string().optional().nullable(),
  alternateSecondaryOfficer: z.string().optional().nullable(),
  alternateTreasuryOfficer: z.string().optional().nullable(),
  applicationId: z.string().optional().nullable(),
  bankNumber: z.string().optional().nullable(),
  blendOverrideIndicator: z.string().optional().nullable(),
  branchNumber: z.string().optional().nullable(),
  closeDate: z.string().date().optional().nullable(),
  control: z.string().optional().nullable(),
  costCenter: z.string().optional().nullable(),
  cpsAasIndicator: z.string().optional().nullable(),
  createdAt: z.string().optional().nullable(),
  currencyCode: z.string().optional().nullable(),
  customerPricingIndicator: z.string().optional().nullable(),
  dailyOffsetIndicator: z.string().optional().nullable(),
  depositAccountType: z.string().optional().nullable(),
  depositCategory: z.string().optional().nullable(),
  effectiveDate: z.string().date().optional().nullable(),
  fileType: z
    .enum([
      'CORE_UNPACKED',
      'IBS_CORE_PACKED',
      'ARPPA',
      'ACH_TRACKER',
      'CASH_MANAGER',
      'D1B',
      'DIRECT_LINK_MERCHANT',
      'EWIRE',
    ])
    .optional()
    .nullable(),
  id: z.string().optional().nullable(),
  inboundRecordStatus: z
    .enum([
      'PARSED',
      'CONVERSION_ERROR',
      'VALIDATION_ERROR',
      'PERSIST_ERROR',
      'PROCESSED_CREATE',
      'PROCESSED_UPDATE',
      'PROCESSED_SKIP',
    ])
    .optional()
    .nullable(),
  openDate: z.string().date().optional().nullable(),
  postingDate: z.string().date().optional().nullable(),
  primaryOfficer: z.string().optional().nullable(),
  processingIndicator: z.string().optional().nullable(),
  secondaryOfficer: z.string().optional().nullable(),
  settleAtAccountIndicator: z.string().optional().nullable(),
  shortName: z.string().optional().nullable(),
  source: z.string().optional().nullable(),
  sourceFilePath: z.string().optional().nullable(),
  treasuryOfficer: z.string().optional().nullable(),
  updatedAt: z.string().optional().nullable(),
})
const demographicUpdateRecordEntityTransformer = z.object({
  ...demographicUpdateRecordEntityValidator.shape,
  aasAccountType:
    demographicUpdateRecordEntityValidator.shape.aasAccountType.transform(
      undefinedToNull,
    ),
  accountNumber:
    demographicUpdateRecordEntityValidator.shape.accountNumber.transform(
      undefinedToNull,
    ),
  accountStatus:
    demographicUpdateRecordEntityValidator.shape.accountStatus.transform(
      undefinedToNull,
    ),
  alternatePrimaryOfficer:
    demographicUpdateRecordEntityValidator.shape.alternatePrimaryOfficer.transform(
      undefinedToNull,
    ),
  alternateSecondaryOfficer:
    demographicUpdateRecordEntityValidator.shape.alternateSecondaryOfficer.transform(
      undefinedToNull,
    ),
  alternateTreasuryOfficer:
    demographicUpdateRecordEntityValidator.shape.alternateTreasuryOfficer.transform(
      undefinedToNull,
    ),
  applicationId:
    demographicUpdateRecordEntityValidator.shape.applicationId.transform(
      undefinedToNull,
    ),
  bankNumber:
    demographicUpdateRecordEntityValidator.shape.bankNumber.transform(
      undefinedToNull,
    ),
  blendOverrideIndicator:
    demographicUpdateRecordEntityValidator.shape.blendOverrideIndicator.transform(
      undefinedToNull,
    ),
  branchNumber:
    demographicUpdateRecordEntityValidator.shape.branchNumber.transform(
      undefinedToNull,
    ),
  closeDate:
    demographicUpdateRecordEntityValidator.shape.closeDate.transform(
      undefinedToNull,
    ),
  control:
    demographicUpdateRecordEntityValidator.shape.control.transform(
      undefinedToNull,
    ),
  costCenter:
    demographicUpdateRecordEntityValidator.shape.costCenter.transform(
      undefinedToNull,
    ),
  cpsAasIndicator:
    demographicUpdateRecordEntityValidator.shape.cpsAasIndicator.transform(
      undefinedToNull,
    ),
  createdAt:
    demographicUpdateRecordEntityValidator.shape.createdAt.transform(
      undefinedToNull,
    ),
  currencyCode:
    demographicUpdateRecordEntityValidator.shape.currencyCode.transform(
      undefinedToNull,
    ),
  customerPricingIndicator:
    demographicUpdateRecordEntityValidator.shape.customerPricingIndicator.transform(
      undefinedToNull,
    ),
  dailyOffsetIndicator:
    demographicUpdateRecordEntityValidator.shape.dailyOffsetIndicator.transform(
      undefinedToNull,
    ),
  depositAccountType:
    demographicUpdateRecordEntityValidator.shape.depositAccountType.transform(
      undefinedToNull,
    ),
  depositCategory:
    demographicUpdateRecordEntityValidator.shape.depositCategory.transform(
      undefinedToNull,
    ),
  effectiveDate:
    demographicUpdateRecordEntityValidator.shape.effectiveDate.transform(
      undefinedToNull,
    ),
  fileType:
    demographicUpdateRecordEntityValidator.shape.fileType.transform(
      undefinedToNull,
    ),
  id: demographicUpdateRecordEntityValidator.shape.id.transform(
    undefinedToNull,
  ),
  inboundRecordStatus:
    demographicUpdateRecordEntityValidator.shape.inboundRecordStatus.transform(
      undefinedToNull,
    ),
  openDate:
    demographicUpdateRecordEntityValidator.shape.openDate.transform(
      undefinedToNull,
    ),
  postingDate:
    demographicUpdateRecordEntityValidator.shape.postingDate.transform(
      undefinedToNull,
    ),
  primaryOfficer:
    demographicUpdateRecordEntityValidator.shape.primaryOfficer.transform(
      undefinedToNull,
    ),
  processingIndicator:
    demographicUpdateRecordEntityValidator.shape.processingIndicator.transform(
      undefinedToNull,
    ),
  secondaryOfficer:
    demographicUpdateRecordEntityValidator.shape.secondaryOfficer.transform(
      undefinedToNull,
    ),
  settleAtAccountIndicator:
    demographicUpdateRecordEntityValidator.shape.settleAtAccountIndicator.transform(
      undefinedToNull,
    ),
  shortName:
    demographicUpdateRecordEntityValidator.shape.shortName.transform(
      undefinedToNull,
    ),
  source:
    demographicUpdateRecordEntityValidator.shape.source.transform(
      undefinedToNull,
    ),
  sourceFilePath:
    demographicUpdateRecordEntityValidator.shape.sourceFilePath.transform(
      undefinedToNull,
    ),
  treasuryOfficer:
    demographicUpdateRecordEntityValidator.shape.treasuryOfficer.transform(
      undefinedToNull,
    ),
  updatedAt:
    demographicUpdateRecordEntityValidator.shape.updatedAt.transform(
      undefinedToNull,
    ),
})
const demographicUpdateRecordEntityDeprecated = z.object({
  aasAccountType: z.string().optional().nullable(),
  accountNumber: z.string().optional().nullable(),
  accountStatus: z.string().optional().nullable(),
  alternatePrimaryOfficer: z.string().optional().nullable(),
  alternateSecondaryOfficer: z.string().optional().nullable(),
  alternateTreasuryOfficer: z.string().optional().nullable(),
  applicationId: z.string().optional().nullable(),
  bankNumber: z.string().optional().nullable(),
  blendOverrideIndicator: z.string().optional().nullable(),
  branchNumber: z.string().optional().nullable(),
  closeDate: z.string().date().optional().nullable(),
  control: z.string().optional().nullable(),
  costCenter: z.string().optional().nullable(),
  cpsAasIndicator: z.string().optional().nullable(),
  createdAt: z.string().optional().nullable(),
  currencyCode: z.string().optional().nullable(),
  customerPricingIndicator: z.string().optional().nullable(),
  dailyOffsetIndicator: z.string().optional().nullable(),
  depositAccountType: z.string().optional().nullable(),
  depositCategory: z.string().optional().nullable(),
  effectiveDate: z.string().date().optional().nullable(),
  fileType: z
    .enum([
      'CORE_UNPACKED',
      'IBS_CORE_PACKED',
      'ARPPA',
      'ACH_TRACKER',
      'CASH_MANAGER',
      'D1B',
      'DIRECT_LINK_MERCHANT',
      'EWIRE',
    ])
    .optional()
    .nullable(),
  id: z.string().optional().nullable(),
  inboundRecordStatus: z
    .enum([
      'PARSED',
      'CONVERSION_ERROR',
      'VALIDATION_ERROR',
      'PERSIST_ERROR',
      'PROCESSED_CREATE',
      'PROCESSED_UPDATE',
      'PROCESSED_SKIP',
    ])
    .optional()
    .nullable(),
  openDate: z.string().date().optional().nullable(),
  postingDate: z.string().date().optional().nullable(),
  primaryOfficer: z.string().optional().nullable(),
  processingIndicator: z.string().optional().nullable(),
  secondaryOfficer: z.string().optional().nullable(),
  settleAtAccountIndicator: z.string().optional().nullable(),
  shortName: z.string().optional().nullable(),
  source: z.string().optional().nullable(),
  sourceFilePath: z.string().optional().nullable(),
  treasuryOfficer: z.string().optional().nullable(),
  updatedAt: z.string().optional().nullable(),
})

const earningsCreditDefinitionValidator = z.object({
  balanceTiers: z.array(balanceTierValidator),
  baseBalanceType: z.enum([
    'AVERAGE_COLLECTED',
    'AVERAGE_LEDGER',
    'AVERAGE_NEGATIVE_COLLECTED',
    'AVERAGE_NEGATIVE_LEDGER',
    'AVERAGE_POSITIVE_COLLECTED',
    'AVERAGE_POSITIVE_LEDGER',
    'AVERAGE_UNCOLLECTED_FUNDS',
    'COMPENSATING_BALANCE',
    'END_OF_MONTH_LEDGER',
    'INVESTABLE_BALANCE',
    'AVERAGE_FLOAT',
    'AVERAGE_CLEARINGHOUSE_FLOAT',
    'REQUIRED_BALANCE',
  ]),
  code: z.string(),
  description: z.string(),
  effectiveDate: z.string().date(),
  indexRateCode: z.string().optional().nullable(),
  maxRateInclusive: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  minRateInclusive: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  rateSource: z.enum(['INDEX_RATE', 'MANUAL']),
  tierMethod: z.enum(['THRESHOLD', 'PARTITIONED']),
})
const earningsCreditDefinitionTransformer = z.object({
  ...earningsCreditDefinitionValidator.shape,
  balanceTiers: z.array(balanceTierTransformer),
  indexRateCode:
    earningsCreditDefinitionValidator.shape.indexRateCode.transform(
      undefinedToNull,
    ),
  maxRateInclusive:
    earningsCreditDefinitionValidator.shape.maxRateInclusive.transform(
      undefinedToNull,
    ),
  minRateInclusive:
    earningsCreditDefinitionValidator.shape.minRateInclusive.transform(
      undefinedToNull,
    ),
})
const earningsCreditDefinitionDeprecated = z.object({
  balanceTiers: z.array(balanceTierDeprecated),
  baseBalanceType: z.enum([
    'AVERAGE_COLLECTED',
    'AVERAGE_LEDGER',
    'AVERAGE_NEGATIVE_COLLECTED',
    'AVERAGE_NEGATIVE_LEDGER',
    'AVERAGE_POSITIVE_COLLECTED',
    'AVERAGE_POSITIVE_LEDGER',
    'AVERAGE_UNCOLLECTED_FUNDS',
    'COMPENSATING_BALANCE',
    'END_OF_MONTH_LEDGER',
    'INVESTABLE_BALANCE',
    'AVERAGE_FLOAT',
    'AVERAGE_CLEARINGHOUSE_FLOAT',
    'REQUIRED_BALANCE',
  ]),
  code: z.string(),
  description: z.string(),
  effectiveDate: z.string().date(),
  indexRateCode: z.string().optional().nullable(),
  maxRateInclusive: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  minRateInclusive: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  rateSource: z.enum(['INDEX_RATE', 'MANUAL']),
  tierMethod: z.enum(['THRESHOLD', 'PARTITIONED']),
})

const effectiveDateRequestBodyValidator = z.object({
  effectiveDate: z.string().date(),
})
const effectiveDateRequestBodyTransformer = effectiveDateRequestBodyValidator
const effectiveDateRequestBodyDeprecated = z.object({
  effectiveDate: z.string().date(),
})

const getDailyBalanceHistoryRequestValidator = z.object({
  bankNumber: z.string(),
  endDateExclusive: z.string().date(),
  startDateInclusive: z.string().date(),
})
const getDailyBalanceHistoryRequestTransformer =
  getDailyBalanceHistoryRequestValidator
const getDailyBalanceHistoryRequestDeprecated = z.object({
  bankNumber: z.string(),
  endDateExclusive: z.string().date(),
  startDateInclusive: z.string().date(),
})

const hydratedAccountWithKeyAccountMappingValidator = z.object({
  child: accountWithKeyValidator,
  parent: accountWithKeyValidator.optional().nullable(),
})
const hydratedAccountWithKeyAccountMappingTransformer = z.object({
  ...hydratedAccountWithKeyAccountMappingValidator.shape,
  child: accountWithKeyTransformer,
  parent: accountWithKeyTransformer
    .optional()
    .nullable()
    .transform(undefinedToNull),
})
const hydratedAccountWithKeyAccountMappingDeprecated = z.object({
  child: accountWithKeyDeprecated,
  parent: accountWithKeyDeprecated,
})

const hydratedCategoryToCategoryMappingValidator = z.object({
  child: serviceCategoryValidator,
  parent: serviceCategoryValidator.optional().nullable(),
})
const hydratedCategoryToCategoryMappingTransformer = z.object({
  ...hydratedCategoryToCategoryMappingValidator.shape,
  child: serviceCategoryTransformer,
  parent: serviceCategoryTransformer
    .optional()
    .nullable()
    .transform(undefinedToNull),
})
const hydratedCategoryToCategoryMappingDeprecated = z.object({
  child: serviceCategoryDeprecated,
  parent: serviceCategoryDeprecated,
})

const hydratedDemographicPriceListValidator = z.object({
  demographicCriteria: z.array(demographicCriteriaValidator),
  demographicPriceList: demographicPriceListValidator,
  ranking: demographicPriceListRankingValidator,
  servicePricing: z.array(servicePriceValidator),
})
const hydratedDemographicPriceListTransformer = z.object({
  ...hydratedDemographicPriceListValidator.shape,
  demographicCriteria: z.array(demographicCriteriaTransformer),
  demographicPriceList: demographicPriceListTransformer,
  ranking: demographicPriceListRankingTransformer,
  servicePricing: z.array(servicePriceTransformer),
})
const hydratedDemographicPriceListDeprecated = z.object({
  demographicCriteria: z.array(demographicCriteriaDeprecated),
  demographicPriceList: demographicPriceListDeprecated,
  ranking: demographicPriceListRankingDeprecated,
  servicePricing: z.array(servicePriceDeprecated),
})

const hydratedServiceToCategoryMappingValidator = z.object({
  child: serviceValidator,
  parent: serviceCategoryValidator.optional().nullable(),
})
const hydratedServiceToCategoryMappingTransformer = z.object({
  ...hydratedServiceToCategoryMappingValidator.shape,
  child: serviceTransformer,
  parent: serviceCategoryTransformer
    .optional()
    .nullable()
    .transform(undefinedToNull),
})
const hydratedServiceToCategoryMappingDeprecated = z.object({
  child: serviceDeprecated,
  parent: serviceCategoryDeprecated,
})

const hydratedServiceCatalogMappingValidator = z.object({
  categoryToCategoryMappings: z.array(
    hydratedCategoryToCategoryMappingValidator,
  ),
  serviceToCategoryMappings: z.array(hydratedServiceToCategoryMappingValidator),
})
const hydratedServiceCatalogMappingTransformer = z.object({
  ...hydratedServiceCatalogMappingValidator.shape,
  categoryToCategoryMappings: z.array(
    hydratedCategoryToCategoryMappingTransformer,
  ),
  serviceToCategoryMappings: z.array(
    hydratedServiceToCategoryMappingTransformer,
  ),
})
const hydratedServiceCatalogMappingDeprecated = z.object({
  categoryToCategoryMappings: z.array(
    hydratedCategoryToCategoryMappingDeprecated,
  ),
  serviceToCategoryMappings: z.array(
    hydratedServiceToCategoryMappingDeprecated,
  ),
})

const indexRateValidator = z.object({
  code: z.string(),
  effectiveDate: z.string().date(),
  indexRate: z.number().transform((v) => v.toFixed(4)),
  name: z.string(),
})
const indexRateTransformer = indexRateValidator
const indexRateDeprecated = z.object({
  code: z.string(),
  effectiveDate: z.string().date(),
  indexRate: z.number().transform((v) => v.toFixed(4)),
  name: z.string(),
})

const hydratedServicePriceValidator = z.object({
  cycleDefinition: cycleDefinitionValidator.optional().nullable(),
  indexRate: indexRateValidator.optional().nullable(),
  servicePrice: servicePriceValidator.optional().nullable(),
})
const hydratedServicePriceTransformer = z.object({
  ...hydratedServicePriceValidator.shape,
  cycleDefinition: cycleDefinitionTransformer
    .optional()
    .nullable()
    .transform(undefinedToNull),
  indexRate: indexRateTransformer
    .optional()
    .nullable()
    .transform(undefinedToNull),
  servicePrice: servicePriceTransformer
    .optional()
    .nullable()
    .transform(undefinedToNull),
})
const hydratedServicePriceDeprecated = z.object({
  cycleDefinition: cycleDefinitionDeprecated,
  indexRate: indexRateDeprecated,
  servicePrice: servicePriceDeprecated,
})

const statementPackageValidator = z.object({
  accountCode: accountCodeValidator,
  addressCode: addressCodeValidator,
  effectiveDate: z.string().date(),
  packageDelivery: z.enum([
    'ELECTRONIC',
    'ELECTRONIC_AND_PRINT',
    'PRINT',
    'NO_STATEMENT',
  ]),
  packageType: z.enum([
    'ALL_ACCOUNTS',
    'COMPOSITE_ACCOUNTS',
    'DEPOSIT_ACCOUNTS',
    'SELECTED_ACCOUNTS',
  ]),
  statementPackageNumber: z.number().int(),
})
const statementPackageTransformer = z.object({
  ...statementPackageValidator.shape,
  accountCode: accountCodeTransformer,
  addressCode: addressCodeTransformer,
})
const statementPackageDeprecated = z.object({
  accountCode: accountCodeDeprecated,
  addressCode: addressCodeDeprecated,
  effectiveDate: z.string().date(),
  packageDelivery: z.enum([
    'ELECTRONIC',
    'ELECTRONIC_AND_PRINT',
    'PRINT',
    'NO_STATEMENT',
  ]),
  packageType: z.enum([
    'ALL_ACCOUNTS',
    'COMPOSITE_ACCOUNTS',
    'DEPOSIT_ACCOUNTS',
    'SELECTED_ACCOUNTS',
  ]),
  statementPackageNumber: z.number().int(),
})

const hydratedStatementPackageValidator = z.object({
  address: addressValidator,
  selectedAccounts: z.array(accountCodeValidator),
  statementPackage: statementPackageValidator,
})
const hydratedStatementPackageTransformer = z.object({
  ...hydratedStatementPackageValidator.shape,
  address: addressTransformer,
  selectedAccounts: z.array(accountCodeTransformer),
  statementPackage: statementPackageTransformer,
})
const hydratedStatementPackageDeprecated = z.object({
  address: addressDeprecated,
  selectedAccounts: z.array(accountCodeDeprecated),
  statementPackage: statementPackageDeprecated,
})

const userFieldDropdownOptionCreateValidator = z.object({
  value: z.string(),
})
const userFieldDropdownOptionCreateTransformer =
  userFieldDropdownOptionCreateValidator
const userFieldDropdownOptionCreateDeprecated = z.object({
  value: z.string(),
})

const userFieldDropdownOptionUpdateValidator = z.object({
  code: z.number().int(),
  value: z.string(),
})
const userFieldDropdownOptionUpdateTransformer =
  userFieldDropdownOptionUpdateValidator
const userFieldDropdownOptionUpdateDeprecated = z.object({
  code: z.number().int(),
  value: z.string(),
})

const hydratedUserFieldConfigurationValidator = z.object({
  availableForPriceList: z.boolean(),
  code: z.number().int(),
  createdDate: z.string().date().optional().nullable(),
  fieldType: z.enum(['FREEFORM', 'DROPDOWN', 'BOOLEAN']),
  lastUpdatedDate: z.string().date().optional().nullable(),
  name: z.string(),
  newDropdownOptions: z
    .array(userFieldDropdownOptionCreateValidator)
    .optional()
    .nullable(),
  updatedDropdownOptions: z
    .array(userFieldDropdownOptionUpdateValidator)
    .optional()
    .nullable(),
})
const hydratedUserFieldConfigurationTransformer = z.object({
  ...hydratedUserFieldConfigurationValidator.shape,
  createdDate:
    hydratedUserFieldConfigurationValidator.shape.createdDate.transform(
      undefinedToNull,
    ),
  lastUpdatedDate:
    hydratedUserFieldConfigurationValidator.shape.lastUpdatedDate.transform(
      undefinedToNull,
    ),
  newDropdownOptions: z
    .array(userFieldDropdownOptionCreateTransformer)
    .optional()
    .nullable()
    .transform(undefinedToNull),
  updatedDropdownOptions: z
    .array(userFieldDropdownOptionUpdateTransformer)
    .optional()
    .nullable()
    .transform(undefinedToNull),
})
const hydratedUserFieldConfigurationDeprecated = z.object({
  availableForPriceList: z.boolean(),
  code: z.number().int(),
  createdDate: z.string().date().optional().nullable(),
  fieldType: z.enum(['FREEFORM', 'DROPDOWN', 'BOOLEAN']),
  lastUpdatedDate: z.string().date().optional().nullable(),
  name: z.string(),
  newDropdownOptions: z.array(userFieldDropdownOptionCreateDeprecated),
  updatedDropdownOptions: z.array(userFieldDropdownOptionUpdateDeprecated),
})

const hydratedUserFieldConfigurationCreateValidator = z.object({
  availableForPriceList: z.boolean(),
  fieldType: z.enum(['FREEFORM', 'DROPDOWN', 'BOOLEAN']),
  name: z.string(),
  newDropdownOptions: z
    .array(userFieldDropdownOptionCreateValidator)
    .optional()
    .nullable(),
})
const hydratedUserFieldConfigurationCreateTransformer = z.object({
  ...hydratedUserFieldConfigurationCreateValidator.shape,
  newDropdownOptions: z
    .array(userFieldDropdownOptionCreateTransformer)
    .optional()
    .nullable()
    .transform(undefinedToNull),
})
const hydratedUserFieldConfigurationCreateDeprecated = z.object({
  availableForPriceList: z.boolean(),
  fieldType: z.enum(['FREEFORM', 'DROPDOWN', 'BOOLEAN']),
  name: z.string(),
  newDropdownOptions: z.array(userFieldDropdownOptionCreateDeprecated),
})

const hydratedUserFieldConfigurationUpdateValidator = z.object({
  availableForPriceList: z.boolean(),
  code: z.number().int(),
  fieldType: z.enum(['FREEFORM', 'DROPDOWN', 'BOOLEAN']),
  name: z.string(),
  newDropdownOptions: z
    .array(userFieldDropdownOptionCreateValidator)
    .optional()
    .nullable(),
  updatedDropdownOptions: z
    .array(userFieldDropdownOptionUpdateValidator)
    .optional()
    .nullable(),
})
const hydratedUserFieldConfigurationUpdateTransformer = z.object({
  ...hydratedUserFieldConfigurationUpdateValidator.shape,
  newDropdownOptions: z
    .array(userFieldDropdownOptionCreateTransformer)
    .optional()
    .nullable()
    .transform(undefinedToNull),
  updatedDropdownOptions: z
    .array(userFieldDropdownOptionUpdateTransformer)
    .optional()
    .nullable()
    .transform(undefinedToNull),
})
const hydratedUserFieldConfigurationUpdateDeprecated = z.object({
  availableForPriceList: z.boolean(),
  code: z.number().int(),
  fieldType: z.enum(['FREEFORM', 'DROPDOWN', 'BOOLEAN']),
  name: z.string(),
  newDropdownOptions: z.array(userFieldDropdownOptionCreateDeprecated),
  updatedDropdownOptions: z.array(userFieldDropdownOptionUpdateDeprecated),
})

const investableBalanceDefinitionValidator = z.object({
  addOtherBalanceLabel: z.string().optional().nullable(),
  baseBalanceType: z.enum([
    'AVERAGE_COLLECTED',
    'AVERAGE_LEDGER',
    'AVERAGE_NEGATIVE_COLLECTED',
    'AVERAGE_NEGATIVE_LEDGER',
    'AVERAGE_POSITIVE_COLLECTED',
    'AVERAGE_POSITIVE_LEDGER',
    'AVERAGE_UNCOLLECTED_FUNDS',
    'COMPENSATING_BALANCE',
    'END_OF_MONTH_LEDGER',
    'INVESTABLE_BALANCE',
    'AVERAGE_FLOAT',
    'AVERAGE_CLEARINGHOUSE_FLOAT',
    'REQUIRED_BALANCE',
  ]),
  code: z.string(),
  effectiveDate: z.string().date(),
  name: z.string(),
  subtractCompensatingBalance: z.boolean(),
  subtractFederalReserveRequirement: z.boolean(),
  subtractInterestPaidMonthToDate: z.boolean(),
  subtractOtherBalanceLabel: z.string().optional().nullable(),
})
const investableBalanceDefinitionTransformer = z.object({
  ...investableBalanceDefinitionValidator.shape,
  addOtherBalanceLabel:
    investableBalanceDefinitionValidator.shape.addOtherBalanceLabel.transform(
      undefinedToNull,
    ),
  subtractOtherBalanceLabel:
    investableBalanceDefinitionValidator.shape.subtractOtherBalanceLabel.transform(
      undefinedToNull,
    ),
})
const investableBalanceDefinitionDeprecated = z.object({
  addOtherBalanceLabel: z.string().optional().nullable(),
  baseBalanceType: z.enum([
    'AVERAGE_COLLECTED',
    'AVERAGE_LEDGER',
    'AVERAGE_NEGATIVE_COLLECTED',
    'AVERAGE_NEGATIVE_LEDGER',
    'AVERAGE_POSITIVE_COLLECTED',
    'AVERAGE_POSITIVE_LEDGER',
    'AVERAGE_UNCOLLECTED_FUNDS',
    'COMPENSATING_BALANCE',
    'END_OF_MONTH_LEDGER',
    'INVESTABLE_BALANCE',
    'AVERAGE_FLOAT',
    'AVERAGE_CLEARINGHOUSE_FLOAT',
    'REQUIRED_BALANCE',
  ]),
  code: z.string(),
  effectiveDate: z.string().date(),
  name: z.string(),
  subtractCompensatingBalance: z.boolean(),
  subtractFederalReserveRequirement: z.boolean(),
  subtractInterestPaidMonthToDate: z.boolean(),
  subtractOtherBalanceLabel: z.string().optional().nullable(),
})

const keyAccountMappingValidator = z.object({
  accountCode: accountCodeValidator,
  childAccountCode: accountCodeValidator,
})
const keyAccountMappingTransformer = z.object({
  ...keyAccountMappingValidator.shape,
  accountCode: accountCodeTransformer,
  childAccountCode: accountCodeTransformer,
})
const keyAccountMappingDeprecated = z.object({
  accountCode: accountCodeDeprecated,
  childAccountCode: accountCodeDeprecated,
})

const nextAvailableAccountNumberResponseValidator = z.object({
  nextAvailableAccountNumber: z.string(),
})
const nextAvailableAccountNumberResponseTransformer =
  nextAvailableAccountNumberResponseValidator
const nextAvailableAccountNumberResponseDeprecated = z.object({
  nextAvailableAccountNumber: z.string(),
})

const officerValidator = z.object({
  bankNumber: z.string(),
  code: z.string(),
  effectiveDate: z.string().date(),
  name: z.string(),
  phone: z.string(),
})
const officerTransformer = officerValidator
const officerDeprecated = z.object({
  bankNumber: z.string(),
  code: z.string(),
  effectiveDate: z.string().date(),
  name: z.string(),
  phone: z.string(),
})

const promotionalPriceListValidator = z.object({
  code: z.string(),
  currency: z.string(),
  effectiveDate: z.string().date(),
  expirationDate: z.string().date(),
  name: z.string().optional().nullable(),
  term: z.number().int(),
})
const promotionalPriceListTransformer = z.object({
  ...promotionalPriceListValidator.shape,
  name: promotionalPriceListValidator.shape.name.transform(undefinedToNull),
})
const promotionalPriceListDeprecated = z.object({
  code: z.string(),
  currency: z.string(),
  effectiveDate: z.string().date(),
  expirationDate: z.string().date(),
  name: z.string().optional().nullable(),
  term: z.number().int(),
})

const reserveRequirementDefinitionValidator = z.object({
  baseBalanceType: z.enum(['AVERAGE_COLLECTED', 'AVERAGE_POSITIVE_COLLECTED']),
  calculationMethodType: z.enum(['INDEXED', 'PERCENTAGE']),
  ceiling: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  code: z.string(),
  effectiveDate: z.string().date(),
  floor: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  indexAdjustment: z
    .number()
    .transform((v) => v.toFixed(6))
    .optional()
    .nullable(),
  indexRateCode: z.string().optional().nullable(),
  name: z.string(),
  reserveRate: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
})
const reserveRequirementDefinitionTransformer = z.object({
  ...reserveRequirementDefinitionValidator.shape,
  ceiling:
    reserveRequirementDefinitionValidator.shape.ceiling.transform(
      undefinedToNull,
    ),
  floor:
    reserveRequirementDefinitionValidator.shape.floor.transform(
      undefinedToNull,
    ),
  indexAdjustment:
    reserveRequirementDefinitionValidator.shape.indexAdjustment.transform(
      undefinedToNull,
    ),
  indexRateCode:
    reserveRequirementDefinitionValidator.shape.indexRateCode.transform(
      undefinedToNull,
    ),
  reserveRate:
    reserveRequirementDefinitionValidator.shape.reserveRate.transform(
      undefinedToNull,
    ),
})
const reserveRequirementDefinitionDeprecated = z.object({
  baseBalanceType: z.enum(['AVERAGE_COLLECTED', 'AVERAGE_POSITIVE_COLLECTED']),
  calculationMethodType: z.enum(['INDEXED', 'PERCENTAGE']),
  ceiling: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  code: z.string(),
  effectiveDate: z.string().date(),
  floor: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
  indexAdjustment: z
    .number()
    .transform((v) => v.toFixed(6))
    .optional()
    .nullable(),
  indexRateCode: z.string().optional().nullable(),
  name: z.string(),
  reserveRate: z
    .number()
    .transform((v) => v.toFixed(4))
    .optional()
    .nullable(),
})

const serviceDetailsValidator = z.object({
  service: serviceValidator,
  serviceCategory: serviceCategoryValidator.optional().nullable(),
  servicePrice: z.array(hydratedServicePriceValidator),
  servicesInServiceSet: z.array(serviceValidator).optional().nullable(),
})
const serviceDetailsTransformer = z.object({
  ...serviceDetailsValidator.shape,
  service: serviceTransformer,
  serviceCategory: serviceCategoryTransformer
    .optional()
    .nullable()
    .transform(undefinedToNull),
  servicePrice: z.array(hydratedServicePriceTransformer),
  servicesInServiceSet: z
    .array(serviceTransformer)
    .optional()
    .nullable()
    .transform(undefinedToNull),
})
const serviceDetailsDeprecated = z.object({
  service: serviceDeprecated,
  serviceCategory: serviceCategoryDeprecated,
  servicePrice: z.array(hydratedServicePriceDeprecated),
  servicesInServiceSet: z.array(serviceDeprecated),
})

const servicePriceRequestValidator = z.object({
  asOfDate: z.string().date(),
  pricingHierarchyEntryCode: z.string().optional().nullable(),
  pricingHierarchyEntryType: z
    .enum(['STANDARD', 'PRICE_LIST', 'OVERRIDE'])
    .optional()
    .nullable(),
  serviceCodes: z.array(z.string()).optional().nullable(),
})
const servicePriceRequestTransformer = z.object({
  ...servicePriceRequestValidator.shape,
  pricingHierarchyEntryCode:
    servicePriceRequestValidator.shape.pricingHierarchyEntryCode.transform(
      undefinedToNull,
    ),
  pricingHierarchyEntryType:
    servicePriceRequestValidator.shape.pricingHierarchyEntryType.transform(
      undefinedToNull,
    ),
  serviceCodes:
    servicePriceRequestValidator.shape.serviceCodes.transform(undefinedToNull),
})
const servicePriceRequestDeprecated = z.object({
  asOfDate: z.string().date(),
  pricingHierarchyEntryCode: z.string().optional().nullable(),
  pricingHierarchyEntryType: z
    .enum(['STANDARD', 'PRICE_LIST', 'OVERRIDE'])
    .optional()
    .nullable(),
  serviceCodes: z.array(z.string()),
})

const settlementProcessingOptionsResponseObjValidator = z.object({
  accountCode: accountCodeValidator,
  accountTypeOverride: accountTypeOverrideValidator.optional().nullable(),
  chargeAccountCode: z.string().optional().nullable(),
  effectiveDate: z.string().date(),
  overrideAsSettlementAccount: z.boolean().optional().nullable(),
})
const settlementProcessingOptionsResponseObjTransformer = z.object({
  ...settlementProcessingOptionsResponseObjValidator.shape,
  accountCode: accountCodeTransformer,
  accountTypeOverride: accountTypeOverrideTransformer
    .optional()
    .nullable()
    .transform(undefinedToNull),
  chargeAccountCode:
    settlementProcessingOptionsResponseObjValidator.shape.chargeAccountCode.transform(
      undefinedToNull,
    ),
  overrideAsSettlementAccount:
    settlementProcessingOptionsResponseObjValidator.shape.overrideAsSettlementAccount.transform(
      undefinedToNull,
    ),
})
const settlementProcessingOptionsResponseObjDeprecated = z.object({
  accountCode: accountCodeDeprecated,
  accountTypeOverride: accountTypeOverrideDeprecated,
  chargeAccountCode: z.string().optional().nullable(),
  effectiveDate: z.string().date(),
  overrideAsSettlementAccount: z.boolean().optional().nullable(),
})

const statementFormatPlanValidator = z.object({
  afpServiceCode: z.boolean().optional().nullable(),
  balanceSummary: z.boolean().optional().nullable(),
  balanceSummaryLabel: z.string().optional().nullable(),
  balanceSummaryLocation: z.string().optional().nullable(),
  balanceSummarySize: z.enum(['FULL', 'HALF']).optional().nullable(),
  boldServiceCategoryLabel: z.boolean().optional().nullable(),
  boldServiceCategorySubtotalLabel: z.boolean().optional().nullable(),
  code: z.string(),
  dailyBalanceSummary: z.enum(['NO', 'YES_ALL_ACCOUNTS', 'YES_COMPOSITE_ONLY']),
  dailyBalanceSummaryLabel: z.string().optional().nullable(),
  dailyBalanceSummaryLocation: z.string().optional().nullable(),
  description: z.string(),
  earningsCreditRate: z.enum(['EARNINGS_CREDIT_DEFINITION', 'CALCULATED_RATE']),
  effectiveDate: z.string().date(),
  enableServiceCategory: z.boolean().optional().nullable(),
  headerLogoFileName: z.string(),
  historicalSummaryLabel: z.string().optional().nullable(),
  historicalSummaryLocation: z.string().optional().nullable(),
  historicalSummaryType: z.enum(['ROLLING_12_MONTHS', 'YTD', 'NO']),
  includeOfficerName: z.boolean().optional().nullable(),
  includeOfficerPhone: z.boolean().optional().nullable(),
  printOfficer: z.enum([
    'PRIMARY_OFFICER',
    'SECONDARY_OFFICER',
    'TREASURY_OFFICER',
    'NO_PRINT',
  ]),
  relationshipSummary: z.boolean().optional().nullable(),
  relationshipSummaryLabel: z.string().optional().nullable(),
  relationshipSummaryLocation: z.string().optional().nullable(),
  requiredBalance: z.boolean().optional().nullable(),
  requiredBalanceMultiplier: z.boolean().optional().nullable(),
  resultsSummaryLabel: z.string(),
  resultsSummaryLocation: z.string(),
  resultsSummarySize: z.string(),
  returnAddress: z.string().optional().nullable(),
  sectionBackgroundColor: z.string(),
  sectionBorderColor: z.string(),
  sectionTextColor: z.string(),
  serviceCategoryBackgroundColor: z.string().optional().nullable(),
  serviceCategoryLevel: z.enum(['ONE', 'TWO', 'THREE']).optional().nullable(),
  serviceCategoryPieChart: z.boolean().optional().nullable(),
  serviceCategoryPieChartLabel: z.string().optional().nullable(),
  serviceCategoryPieChartLocation: z.string().optional().nullable(),
  serviceCategorySort: z
    .enum([
      'USER_DEFINED',
      'ALPHABETICAL_BY_CATEGORY_NAME',
      'BY_LOWEST_TO_HIGHEST_SERVICE_CODE',
    ])
    .optional()
    .nullable(),
  serviceCategorySubtotal: z.boolean().optional().nullable(),
  serviceCategorySubtotalBackgroundColor: z.string().optional().nullable(),
  serviceChargesDueBarChart: z.boolean().optional().nullable(),
  serviceChargesDueBarChartLabel: z.string().optional().nullable(),
  serviceChargesDueBarChartLocation: z.string().optional().nullable(),
  serviceCode: z.boolean().optional().nullable(),
  serviceDetailLabel: z.string(),
  serviceDetailLocation: z.string().optional().nullable(),
  sortServicesType: z.enum([
    'USER_DEFINED',
    'ALPHABETICAL_BY_CATEGORY_NAME',
    'BY_LOWEST_TO_HIGHEST_SERVICE_CODE',
  ]),
  statementImageLocations: z.array(z.string()),
  statementMessage: z.boolean().optional().nullable(),
  statementMessageImages: z.array(z.string()),
  statementMessageLocation: z.string().optional().nullable(),
})
const statementFormatPlanTransformer = z.object({
  ...statementFormatPlanValidator.shape,
  afpServiceCode:
    statementFormatPlanValidator.shape.afpServiceCode.transform(
      undefinedToNull,
    ),
  balanceSummary:
    statementFormatPlanValidator.shape.balanceSummary.transform(
      undefinedToNull,
    ),
  balanceSummaryLabel:
    statementFormatPlanValidator.shape.balanceSummaryLabel.transform(
      undefinedToNull,
    ),
  balanceSummaryLocation:
    statementFormatPlanValidator.shape.balanceSummaryLocation.transform(
      undefinedToNull,
    ),
  balanceSummarySize:
    statementFormatPlanValidator.shape.balanceSummarySize.transform(
      undefinedToNull,
    ),
  boldServiceCategoryLabel:
    statementFormatPlanValidator.shape.boldServiceCategoryLabel.transform(
      undefinedToNull,
    ),
  boldServiceCategorySubtotalLabel:
    statementFormatPlanValidator.shape.boldServiceCategorySubtotalLabel.transform(
      undefinedToNull,
    ),
  dailyBalanceSummaryLabel:
    statementFormatPlanValidator.shape.dailyBalanceSummaryLabel.transform(
      undefinedToNull,
    ),
  dailyBalanceSummaryLocation:
    statementFormatPlanValidator.shape.dailyBalanceSummaryLocation.transform(
      undefinedToNull,
    ),
  enableServiceCategory:
    statementFormatPlanValidator.shape.enableServiceCategory.transform(
      undefinedToNull,
    ),
  historicalSummaryLabel:
    statementFormatPlanValidator.shape.historicalSummaryLabel.transform(
      undefinedToNull,
    ),
  historicalSummaryLocation:
    statementFormatPlanValidator.shape.historicalSummaryLocation.transform(
      undefinedToNull,
    ),
  includeOfficerName:
    statementFormatPlanValidator.shape.includeOfficerName.transform(
      undefinedToNull,
    ),
  includeOfficerPhone:
    statementFormatPlanValidator.shape.includeOfficerPhone.transform(
      undefinedToNull,
    ),
  relationshipSummary:
    statementFormatPlanValidator.shape.relationshipSummary.transform(
      undefinedToNull,
    ),
  relationshipSummaryLabel:
    statementFormatPlanValidator.shape.relationshipSummaryLabel.transform(
      undefinedToNull,
    ),
  relationshipSummaryLocation:
    statementFormatPlanValidator.shape.relationshipSummaryLocation.transform(
      undefinedToNull,
    ),
  requiredBalance:
    statementFormatPlanValidator.shape.requiredBalance.transform(
      undefinedToNull,
    ),
  requiredBalanceMultiplier:
    statementFormatPlanValidator.shape.requiredBalanceMultiplier.transform(
      undefinedToNull,
    ),
  returnAddress:
    statementFormatPlanValidator.shape.returnAddress.transform(undefinedToNull),
  serviceCategoryBackgroundColor:
    statementFormatPlanValidator.shape.serviceCategoryBackgroundColor.transform(
      undefinedToNull,
    ),
  serviceCategoryLevel:
    statementFormatPlanValidator.shape.serviceCategoryLevel.transform(
      undefinedToNull,
    ),
  serviceCategoryPieChart:
    statementFormatPlanValidator.shape.serviceCategoryPieChart.transform(
      undefinedToNull,
    ),
  serviceCategoryPieChartLabel:
    statementFormatPlanValidator.shape.serviceCategoryPieChartLabel.transform(
      undefinedToNull,
    ),
  serviceCategoryPieChartLocation:
    statementFormatPlanValidator.shape.serviceCategoryPieChartLocation.transform(
      undefinedToNull,
    ),
  serviceCategorySort:
    statementFormatPlanValidator.shape.serviceCategorySort.transform(
      undefinedToNull,
    ),
  serviceCategorySubtotal:
    statementFormatPlanValidator.shape.serviceCategorySubtotal.transform(
      undefinedToNull,
    ),
  serviceCategorySubtotalBackgroundColor:
    statementFormatPlanValidator.shape.serviceCategorySubtotalBackgroundColor.transform(
      undefinedToNull,
    ),
  serviceChargesDueBarChart:
    statementFormatPlanValidator.shape.serviceChargesDueBarChart.transform(
      undefinedToNull,
    ),
  serviceChargesDueBarChartLabel:
    statementFormatPlanValidator.shape.serviceChargesDueBarChartLabel.transform(
      undefinedToNull,
    ),
  serviceChargesDueBarChartLocation:
    statementFormatPlanValidator.shape.serviceChargesDueBarChartLocation.transform(
      undefinedToNull,
    ),
  serviceCode:
    statementFormatPlanValidator.shape.serviceCode.transform(undefinedToNull),
  serviceDetailLocation:
    statementFormatPlanValidator.shape.serviceDetailLocation.transform(
      undefinedToNull,
    ),
  statementMessage:
    statementFormatPlanValidator.shape.statementMessage.transform(
      undefinedToNull,
    ),
  statementMessageLocation:
    statementFormatPlanValidator.shape.statementMessageLocation.transform(
      undefinedToNull,
    ),
})
const statementFormatPlanDeprecated = z.object({
  afpServiceCode: z.boolean().optional().nullable(),
  balanceSummary: z.boolean().optional().nullable(),
  balanceSummaryLabel: z.string().optional().nullable(),
  balanceSummaryLocation: z.string().optional().nullable(),
  balanceSummarySize: z.enum(['FULL', 'HALF']).optional().nullable(),
  boldServiceCategoryLabel: z.boolean().optional().nullable(),
  boldServiceCategorySubtotalLabel: z.boolean().optional().nullable(),
  code: z.string(),
  dailyBalanceSummary: z.enum(['NO', 'YES_ALL_ACCOUNTS', 'YES_COMPOSITE_ONLY']),
  dailyBalanceSummaryLabel: z.string().optional().nullable(),
  dailyBalanceSummaryLocation: z.string().optional().nullable(),
  description: z.string(),
  earningsCreditRate: z.enum(['EARNINGS_CREDIT_DEFINITION', 'CALCULATED_RATE']),
  effectiveDate: z.string().date(),
  enableServiceCategory: z.boolean().optional().nullable(),
  headerLogoFileName: z.string(),
  historicalSummaryLabel: z.string().optional().nullable(),
  historicalSummaryLocation: z.string().optional().nullable(),
  historicalSummaryType: z.enum(['ROLLING_12_MONTHS', 'YTD', 'NO']),
  includeOfficerName: z.boolean().optional().nullable(),
  includeOfficerPhone: z.boolean().optional().nullable(),
  printOfficer: z.enum([
    'PRIMARY_OFFICER',
    'SECONDARY_OFFICER',
    'TREASURY_OFFICER',
    'NO_PRINT',
  ]),
  relationshipSummary: z.boolean().optional().nullable(),
  relationshipSummaryLabel: z.string().optional().nullable(),
  relationshipSummaryLocation: z.string().optional().nullable(),
  requiredBalance: z.boolean().optional().nullable(),
  requiredBalanceMultiplier: z.boolean().optional().nullable(),
  resultsSummaryLabel: z.string(),
  resultsSummaryLocation: z.string(),
  resultsSummarySize: z.string(),
  returnAddress: z.string().optional().nullable(),
  sectionBackgroundColor: z.string(),
  sectionBorderColor: z.string(),
  sectionTextColor: z.string(),
  serviceCategoryBackgroundColor: z.string().optional().nullable(),
  serviceCategoryLevel: z.enum(['ONE', 'TWO', 'THREE']).optional().nullable(),
  serviceCategoryPieChart: z.boolean().optional().nullable(),
  serviceCategoryPieChartLabel: z.string().optional().nullable(),
  serviceCategoryPieChartLocation: z.string().optional().nullable(),
  serviceCategorySort: z
    .enum([
      'USER_DEFINED',
      'ALPHABETICAL_BY_CATEGORY_NAME',
      'BY_LOWEST_TO_HIGHEST_SERVICE_CODE',
    ])
    .optional()
    .nullable(),
  serviceCategorySubtotal: z.boolean().optional().nullable(),
  serviceCategorySubtotalBackgroundColor: z.string().optional().nullable(),
  serviceChargesDueBarChart: z.boolean().optional().nullable(),
  serviceChargesDueBarChartLabel: z.string().optional().nullable(),
  serviceChargesDueBarChartLocation: z.string().optional().nullable(),
  serviceCode: z.boolean().optional().nullable(),
  serviceDetailLabel: z.string(),
  serviceDetailLocation: z.string().optional().nullable(),
  sortServicesType: z.enum([
    'USER_DEFINED',
    'ALPHABETICAL_BY_CATEGORY_NAME',
    'BY_LOWEST_TO_HIGHEST_SERVICE_CODE',
  ]),
  statementImageLocations: z.array(z.string()),
  statementMessage: z.boolean().optional().nullable(),
  statementMessageImages: z.array(z.string()),
  statementMessageLocation: z.string().optional().nullable(),
})

const statementMessageValidator = z.object({
  code: z.string(),
  effectiveDate: z.string().date(),
  message: z.string(),
  name: z.string(),
})
const statementMessageTransformer = statementMessageValidator
const statementMessageDeprecated = z.object({
  code: z.string(),
  effectiveDate: z.string().date(),
  message: z.string(),
  name: z.string(),
})

const versionableEntityIdValidator = z.object({
  code: z.string(),
  effectiveDate: z.string().date(),
})
const versionableEntityIdTransformer = versionableEntityIdValidator
const versionableEntityIdDeprecated = z.object({
  code: z.string(),
  effectiveDate: z.string().date(),
})

export const apiToFormSchemas = {
  account: accountTransformer,
  accountCode: accountCodeTransformer,
  accountCodeWithEffectivedateReq: accountCodeWithEffectivedateReqTransformer,
  accountCodesDateLeadAccountRequest:
    accountCodesDateLeadAccountRequestTransformer,
  accountCodesRetrieveRequest: accountCodesRetrieveRequestTransformer,
  accountEffectiveDateRequest: accountEffectiveDateRequestTransformer,
  accountEntity: accountEntityTransformer,
  accountMappingCreateRequest: accountMappingCreateRequestTransformer,
  accountMappingEntity: accountMappingEntityTransformer,
  accountMappingUpdateRequest: accountMappingUpdateRequestTransformer,
  accountType: accountTypeTransformer,
  accountTypeOverride: accountTypeOverrideTransformer,
  accountWithKey: accountWithKeyTransformer,
  address: addressTransformer,
  addressCode: addressCodeTransformer,
  addressCodeAvailabilityRequest: addressCodeAvailabilityRequestTransformer,
  addressCodeAvailabilityResponse: addressCodeAvailabilityResponseTransformer,
  addressRetrieveRequest: addressRetrieveRequestTransformer,
  analysisResultOption: analysisResultOptionTransformer,
  balanceRequirementDefinition: balanceRequirementDefinitionTransformer,
  balanceTier: balanceTierTransformer,
  bankOptions: bankOptionsTransformer,
  branch: branchTransformer,
  codeRequestBody: codeRequestBodyTransformer,
  codesAndEffectiveDateRequestBody: codesAndEffectiveDateRequestBodyTransformer,
  compositeAccountCreateRequest: compositeAccountCreateRequestTransformer,
  createOrUpdateService: createOrUpdateServiceTransformer,
  createServiceCategory: createServiceCategoryTransformer,
  cycleDefinition: cycleDefinitionTransformer,
  dailyBalanceHistory: dailyBalanceHistoryTransformer,
  demographicCriteria: demographicCriteriaTransformer,
  demographicCriteriaId: demographicCriteriaIdTransformer,
  demographicPriceList: demographicPriceListTransformer,
  demographicPriceListRanking: demographicPriceListRankingTransformer,
  demographicPriceListWithCount: demographicPriceListWithCountTransformer,
  demographicUpdateRecordEntity: demographicUpdateRecordEntityTransformer,
  earningsCreditDefinition: earningsCreditDefinitionTransformer,
  effectiveDateRequestBody: effectiveDateRequestBodyTransformer,
  getDailyBalanceHistoryRequest: getDailyBalanceHistoryRequestTransformer,
  hydratedAccountWithKeyAccountMapping:
    hydratedAccountWithKeyAccountMappingTransformer,
  hydratedCategoryToCategoryMapping:
    hydratedCategoryToCategoryMappingTransformer,
  hydratedDemographicPriceList: hydratedDemographicPriceListTransformer,
  hydratedServiceCatalogMapping: hydratedServiceCatalogMappingTransformer,
  hydratedServicePrice: hydratedServicePriceTransformer,
  hydratedServiceToCategoryMapping: hydratedServiceToCategoryMappingTransformer,
  hydratedStatementPackage: hydratedStatementPackageTransformer,
  hydratedUserFieldConfiguration: hydratedUserFieldConfigurationTransformer,
  hydratedUserFieldConfigurationCreate:
    hydratedUserFieldConfigurationCreateTransformer,
  hydratedUserFieldConfigurationUpdate:
    hydratedUserFieldConfigurationUpdateTransformer,
  indexRate: indexRateTransformer,
  investableBalanceDefinition: investableBalanceDefinitionTransformer,
  keyAccountMapping: keyAccountMappingTransformer,
  nextAvailableAccountNumberResponse:
    nextAvailableAccountNumberResponseTransformer,
  officer: officerTransformer,
  promotionalPriceList: promotionalPriceListTransformer,
  reserveRequirementDefinition: reserveRequirementDefinitionTransformer,
  service: serviceTransformer,
  serviceCategory: serviceCategoryTransformer,
  serviceDetails: serviceDetailsTransformer,
  servicePrice: servicePriceTransformer,
  servicePriceRequest: servicePriceRequestTransformer,
  settlementProcessingOptionsResponseObj:
    settlementProcessingOptionsResponseObjTransformer,
  statementFormatPlan: statementFormatPlanTransformer,
  statementMessage: statementMessageTransformer,
  statementPackage: statementPackageTransformer,
  statementPackageCreateUpdateRequest:
    statementPackageCreateUpdateRequestTransformer,
  userFieldDropdownOptionCreate: userFieldDropdownOptionCreateTransformer,
  userFieldDropdownOptionUpdate: userFieldDropdownOptionUpdateTransformer,
  userFieldSelection: userFieldSelectionTransformer,
  versionableEntityId: versionableEntityIdTransformer,
}

/**
 * @deprecated we should remove all references to these
 */
export const deprecatedApiToFormSchemas = {
  account: accountDeprecated,
  accountCode: accountCodeDeprecated,
  accountCodeWithEffectivedateReq: accountCodeWithEffectivedateReqDeprecated,
  accountCodesDateLeadAccountRequest:
    accountCodesDateLeadAccountRequestDeprecated,
  accountCodesRetrieveRequest: accountCodesRetrieveRequestDeprecated,
  accountEffectiveDateRequest: accountEffectiveDateRequestDeprecated,
  accountEntity: accountEntityDeprecated,
  accountMappingCreateRequest: accountMappingCreateRequestDeprecated,
  accountMappingEntity: accountMappingEntityDeprecated,
  accountMappingUpdateRequest: accountMappingUpdateRequestDeprecated,
  accountType: accountTypeDeprecated,
  accountTypeOverride: accountTypeOverrideDeprecated,
  accountWithKey: accountWithKeyDeprecated,
  address: addressDeprecated,
  addressCode: addressCodeDeprecated,
  addressCodeAvailabilityRequest: addressCodeAvailabilityRequestDeprecated,
  addressCodeAvailabilityResponse: addressCodeAvailabilityResponseDeprecated,
  addressRetrieveRequest: addressRetrieveRequestDeprecated,
  analysisResultOption: analysisResultOptionDeprecated,
  balanceRequirementDefinition: balanceRequirementDefinitionDeprecated,
  balanceTier: balanceTierDeprecated,
  bankOptions: bankOptionsDeprecated,
  branch: branchDeprecated,
  codeRequestBody: codeRequestBodyDeprecated,
  codesAndEffectiveDateRequestBody: codesAndEffectiveDateRequestBodyDeprecated,
  compositeAccountCreateRequest: compositeAccountCreateRequestDeprecated,
  createOrUpdateService: createOrUpdateServiceDeprecated,
  createServiceCategory: createServiceCategoryDeprecated,
  cycleDefinition: cycleDefinitionDeprecated,
  dailyBalanceHistory: dailyBalanceHistoryDeprecated,
  demographicCriteria: demographicCriteriaDeprecated,
  demographicCriteriaId: demographicCriteriaIdDeprecated,
  demographicPriceList: demographicPriceListDeprecated,
  demographicPriceListRanking: demographicPriceListRankingDeprecated,
  demographicPriceListWithCount: demographicPriceListWithCountDeprecated,
  demographicUpdateRecordEntity: demographicUpdateRecordEntityDeprecated,
  earningsCreditDefinition: earningsCreditDefinitionDeprecated,
  effectiveDateRequestBody: effectiveDateRequestBodyDeprecated,
  getDailyBalanceHistoryRequest: getDailyBalanceHistoryRequestDeprecated,
  hydratedAccountWithKeyAccountMapping:
    hydratedAccountWithKeyAccountMappingDeprecated,
  hydratedCategoryToCategoryMapping:
    hydratedCategoryToCategoryMappingDeprecated,
  hydratedDemographicPriceList: hydratedDemographicPriceListDeprecated,
  hydratedServiceCatalogMapping: hydratedServiceCatalogMappingDeprecated,
  hydratedServicePrice: hydratedServicePriceDeprecated,
  hydratedServiceToCategoryMapping: hydratedServiceToCategoryMappingDeprecated,
  hydratedStatementPackage: hydratedStatementPackageDeprecated,
  hydratedUserFieldConfiguration: hydratedUserFieldConfigurationDeprecated,
  hydratedUserFieldConfigurationCreate:
    hydratedUserFieldConfigurationCreateDeprecated,
  hydratedUserFieldConfigurationUpdate:
    hydratedUserFieldConfigurationUpdateDeprecated,
  indexRate: indexRateDeprecated,
  investableBalanceDefinition: investableBalanceDefinitionDeprecated,
  keyAccountMapping: keyAccountMappingDeprecated,
  nextAvailableAccountNumberResponse:
    nextAvailableAccountNumberResponseDeprecated,
  officer: officerDeprecated,
  promotionalPriceList: promotionalPriceListDeprecated,
  reserveRequirementDefinition: reserveRequirementDefinitionDeprecated,
  service: serviceDeprecated,
  serviceCategory: serviceCategoryDeprecated,
  serviceDetails: serviceDetailsDeprecated,
  servicePrice: servicePriceDeprecated,
  servicePriceRequest: servicePriceRequestDeprecated,
  settlementProcessingOptionsResponseObj:
    settlementProcessingOptionsResponseObjDeprecated,
  statementFormatPlan: statementFormatPlanDeprecated,
  statementMessage: statementMessageDeprecated,
  statementPackage: statementPackageDeprecated,
  statementPackageCreateUpdateRequest:
    statementPackageCreateUpdateRequestDeprecated,
  userFieldDropdownOptionCreate: userFieldDropdownOptionCreateDeprecated,
  userFieldDropdownOptionUpdate: userFieldDropdownOptionUpdateDeprecated,
  userFieldSelection: userFieldSelectionDeprecated,
  versionableEntityId: versionableEntityIdDeprecated,
}
