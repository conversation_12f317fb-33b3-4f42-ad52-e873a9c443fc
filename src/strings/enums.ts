import { ServiceForm, ServicePriceForm } from '@/api/formToApiSchema'
import { UnaryFunction } from '@/lib/functional/compose'
import { isNonNull } from '@/lib/guards/isNonNull'
import { match, Matcher } from '@/lib/unions/match'
import { IntoUnion, Union, variant } from '@/lib/unions/Union'

export const balanceTypeLabel = labelNull(
  label<ServicePriceForm['balanceType']>()({
    AVERAGE_COLLECTED: () => 'Average Collected',
    AVERAGE_LEDGER: () => 'Average Ledger',
    AVERAGE_NEGATIVE_COLLECTED: () => 'Average Negative Collected',
    AVERAGE_NEGATIVE_LEDGER: () => 'Average Negative Ledger',
    AVERAGE_POSITIVE_COLLECTED: () => 'Average Positive Collected',
    AVERAGE_POSITIVE_LEDGER: () => 'Average Positive Ledger',
    AVERAGE_UNCOLLECTED_FUNDS: () => 'Average Uncollected Funds',
    COMPENSATING_BALANCE: () => 'Compensating Balance',
    END_OF_MONTH_LEDGER: () => 'End of Month Ledger',
    INVESTABLE_BALANCE: () => 'Investable Balance',
    AVERAGE_FLOAT: () => 'Average Float',
    AVERAGE_CLEARINGHOUSE_FLOAT: () => 'Average Clearinghouse Float',
    REQUIRED_BALANCE: () => 'Required balance',
  }),
)

export const costTypeLabel = labelNull(
  label<ServicePriceForm['costType']>()({
    NO_COST: () => 'No cost',
    UNIT_COST: () => 'Unit cost',
    FLAT_COST: () => 'Flat cost',
  }),
)

export const dispositionLabel = labelNull(
  label<ServicePriceForm['disposition']>()({
    ANALYSED: () => 'Analyzed',
    HARD_CHARGE: () => 'Hard charge',
    IMMEDIATE_CHARGE: () => 'Immediate charge',
    WAIVED: () => 'Waived',
  }),
)

export const pricingHierarchyEntryTypeLabel = labelNull(
  label<ServicePriceForm['pricingHierarchyEntryType']>()({
    OVERRIDE: () => 'Account',
    PRICE_LIST: () => 'Price list',
    STANDARD: () => 'Default',
  }),
)

export const priceTypeLabel = labelNull(
  label<ServicePriceForm['priceType']>()({
    NOT_PRICED: () => 'Not priced',
    UNIT_PRICED: () => 'Unit priced',
    FLAT_FEE: () => 'Flat fee',
    THRESHOLD_TIER: () => 'Threshold tier',
    PARTITIONED_TIER: () => 'Partitioned tier',
    PERCENTAGE: () => 'Percentage',
    INDEXED: () => 'Indexed',
    OUTSIDE_PRICE: () => 'Outside price',
  }),
)

export const serviceTypeLabel = labelNull(
  label<ServiceForm['serviceType']>()({
    VOLUME_BASED: () => 'Volume based',
    RECURRING: () => 'Recurring',
    BALANCE_BASED: () => 'Balance based',
    PRE_PRICED: () => 'Pre priced',
    SERVICE_SET: () => 'Service set',
  }),
)

export function labelNull<T extends string | null, R>(
  fn: (value: NonNullable<T>) => R,
  nullLabel = '-',
): UnaryFunction<T, R | string> {
  return (value) => (isNonNull(value) ? fn(value) : nullLabel)
}

function label<T extends string | null>(): <R extends string>(
  matcher: Matcher<IntoUnion<NonNullable<T>, void>, R>,
) => UnaryFunction<NonNullable<T>, R> {
  return <R extends string>(
      matcher: Matcher<IntoUnion<NonNullable<T>, void>, R>,
    ) =>
    (value) => {
      // SAFETY: `matcher` is `Matcher<..., R>`, which always returns a R.
      return match(variant(value), matcher) as R
    }
}
