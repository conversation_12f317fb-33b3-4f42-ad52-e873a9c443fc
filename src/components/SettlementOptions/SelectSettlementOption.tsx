'use client'

import { Select } from '@/components/Input/Select'

export interface SelectSettlementOptionProps {
  field: string
  value: string
  optionsMap: Map<string, string>
  disabled?: boolean
  defaultValue?: string
  onChange: (value: string) => void
}

export function SelectSettlementOption({
  field,
  value,
  optionsMap,
  disabled = false,
  defaultValue,
  onChange,
}: SelectSettlementOptionProps) {
  if (disabled) {
    // Show default value when disabled (similar to view page)
    return (
      <div className='mt-6 min-w-96 rounded border border-gray-200 bg-gray-50 px-3 py-2 text-gray-700'>
        <div className='flex flex-row gap-2'>
          <div className='text-gray-500'>Default:</div>
          <div>{defaultValue || 'Inherited from account type'}</div>
        </div>
      </div>
    )
  }

  return (
    <Select
      className='mt-6 min-w-96'
      name={field}
      value={value}
      options={Array.from(optionsMap.keys())}
      disabled={disabled}
      onChange={onChange}
      renderOption={(option) => {
        const optionName = optionsMap.get(option)
        return `${optionName} ${option}`
      }}
      renderSelected={(option) => {
        const optionName = optionsMap.get(option)
        return (
          <div className='flex flex-row gap-2'>
            <div>{optionName}</div>
            <div>{option}</div>
          </div>
        )
      }}
    />
  )
}
