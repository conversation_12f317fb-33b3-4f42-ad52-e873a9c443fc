import {
  ColumnDef,
  ColumnFiltersState,
  ExpandedState,
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  getFilteredRowModel,
  Row,
  RowData,
  useReactTable,
} from '@tanstack/react-table'
import { useRef, useState } from 'react'
import { useVirtualizer } from '@tanstack/react-virtual'
import { clsx } from 'clsx'

export interface ColumnMeta {
  className?: string
}

export interface ExpandedTableProps<T, V> {
  data: T[]
  columns: ColumnDef<T, V>[]
  getSubRows: (row: T) => T[] | undefined
  columnFilters?: ColumnFiltersState
  columnVisibility?: Record<string, boolean>
  onRowClick?: (row: Row<T>) => void
  noDataText?: string
  initialExpandedState?: ExpandedState
}

export function ExpandedTable<T, V>({
  data,
  columns,
  getSubRows,
  columnFilters,
  columnVisibility,
  onRowClick,
  noDataText,
  initialExpandedState,
}: ExpandedTableProps<T, V>) {
  const [expanded, setExpanded] = useState<ExpandedState>(
    initialExpandedState ?? {},
  )

  const table = useReactTable({
    data,
    columns,
    state: {
      expanded,
      columnFilters,
      columnVisibility,
    },
    onExpandedChange: setExpanded,
    getSubRows: getSubRows,
    getCoreRowModel: getCoreRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
  })

  const { rows } = table.getRowModel()
  const [headerGroup] = table.getHeaderGroups()
  const tbodyRef = useRef<HTMLTableSectionElement>(null)
  const virtualizer = useVirtualizer({
    count: rows.length,
    getScrollElement: () => tbodyRef.current,
    estimateSize: () => 51,
    overscan: 100,
  })

  const getColumnMeta = <T,>(columnDef: ColumnDef<T, unknown>): ColumnMeta => {
    return (columnDef.meta as ColumnMeta | undefined) ?? {}
  }

  return (
    <div className='flex min-h-0'>
      <div
        className={clsx(
          'flex min-h-0 w-full flex-col divide-y',
          'overflow-y-scroll rounded-lg border border-zinc-200 bg-white',
        )}
        role='table'
      >
        <div
          className='sticky top-0 z-10 flex min-h-12 items-center bg-zinc-100 text-sm font-medium text-zinc-500'
          role='row'
        >
          {headerGroup.headers.map((header) => {
            const { className } = getColumnMeta(header.column.columnDef)
            return (
              <div
                key={header.id}
                className={clsx(className, 'flex px-6 text-left')}
                role='columnheader'
              >
                {flexRender(
                  header.column.columnDef.header,
                  header.getContext(),
                )}
              </div>
            )
          })}
        </div>
        {rows.length === 0 ?
          <div className='flex flex-row justify-center align-middle'>
            {noDataText ?? ''}
          </div>
        : <div className='flex w-full' ref={tbodyRef}>
            <div
              className='flex w-full flex-col'
              style={{ height: `${virtualizer.getTotalSize()}px` }}
            >
              {virtualizer.getVirtualItems().map((virtualRow, index) => {
                const row = rows[virtualRow.index]
                return (
                  <div
                    key={row.id}
                    className='flex cursor-pointer items-center border-b hover:bg-indigo-200 active:bg-indigo-300'
                    style={{
                      height: `${virtualRow.size}px`,
                      transform: `translateY(${virtualRow.start - index * virtualRow.size}px)`,
                    }}
                    role='row'
                    onClick={() => {
                      onRowClick && onRowClick(row)
                    }}
                  >
                    {row.getVisibleCells().map((cell) => {
                      const { className } = getColumnMeta(cell.column.columnDef)
                      return (
                        <div
                          key={cell.id}
                          className={clsx(className, 'flex px-6 text-left')}
                          role='cell'
                        >
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext(),
                          )}
                        </div>
                      )
                    })}
                  </div>
                )
              })}
            </div>
          </div>
        }
      </div>
    </div>
  )
}
