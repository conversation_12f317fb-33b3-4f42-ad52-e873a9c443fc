import { z } from 'zod'
import { Form<PERSON><PERSON>, ReactFormApi } from '@tanstack/react-form'

import { toServerFormat } from '@/lib/date'
import { formToApiSchemas } from '@/api/formToApiSchema'
import { apiToFormSchemas } from '@/api/apiToFormSchema'

export const indexRateFormInput = z.object({
  ...apiToFormSchemas.indexRate.shape,
  effectiveDate: z.date(),
  indexRate: z.string(),
})
type IndexRateInfoState = z.infer<typeof indexRateFormInput>

// TODO create composite zod schema instead (e.g., updateIndexRateSchema) and infer this type from that schema.
// This new composite schema could also be used as onChange validator in useForm in UpdateIndexRateForm to reduce
// need to define validators.onChange on every form field.
export type UpdateIndexRateFormState = {
  indexRateInfo: IndexRateInfoState
}

export const indexRateForm = z.object({
  indexRateInfo: indexRateFormInput,
})

export type UpdateIndexRateFormApi = ReactFormApi<UpdateIndexRateFormState> &
  FormApi<UpdateIndexRateFormState>

export const indexRateFormToServer = z.object({
  ...formToApiSchemas.indexRate.shape,
  effectiveDate: z.date().transform((d) => toServerFormat(d)),
})
