'use client'

import Link from 'next/link'
import { useQuery } from '@tanstack/react-query'
import { useRouter } from 'next/navigation'
import { ChevronRightIcon } from '@heroicons/react/24/outline'

import { Button } from '@/components/Button'
import {
  DetailsSection,
  DetailsSectionTitle,
  DetailsSectionItemsRow,
  DetailsSectionItem,
} from '@/components/DetailsSection'
import { VersionTimeline } from '@/app/[bankId]/configuration/_components/VersionTimeline'

import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '@/app/[bankId]/configuration/routing'
import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { indexRateQueries } from '../../../queries'

export default function ViewIndexRate() {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/index-rates/[effectiveDate]/view/[indexRateCode]',
  )!
  const { data, status, error } = useQuery(
    indexRateQueries('/getIndexRatesByCode', {
      code: route.params.indexRateCode,
    }),
  )
  if (status === 'pending') return 'Loading...'
  if (!data || error) {
    return <div>404 Not Found</div>
  }

  const effectiveDate = route.params.effectiveDate

  const { indexRate: IndexRateType } = apiToFormSchemas
  const indexRates = data
    ?.map((item) => {
      const parsed = IndexRateType.parse(item)
      return {
        ...parsed,
        indexRate: item.indexRate,
      }
    })
    .sort((a, b) => {
      const newDateA = new Date(a.effectiveDate)
      const newDateB = new Date(b.effectiveDate)
      return newDateB.getTime() - newDateA.getTime()
    })

  let indexRate = indexRates.find(
    (item) => item.effectiveDate === effectiveDate,
  )
  if (!indexRate) {
    indexRate = indexRates[0]
  }

  const onEditClick = () => {
    router.push(
      routeTo(
        '/configuration/index-rates/[effectiveDate]/edit/[indexRateCode]',
        {
          ...route.params,
          effectiveDate: indexRate.effectiveDate,
          indexRateCode: indexRate.code,
        },
      ),
    )
  }

  const linkFormatter = (effectiveDate: string, indexRateCode: string) => {
    return routeTo(
      '/configuration/index-rates/[effectiveDate]/view/[indexRateCode]',
      { ...route.params, effectiveDate, indexRateCode },
    )
  }

  return (
    <div className='w-full overflow-hidden overflow-y-scroll px-8 py-10'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-2'>
          <Link
            href={routeTo(
              '/configuration/index-rates/[effectiveDate]',
              route.params,
            )}
            className='font-bold'
          >
            ...
          </Link>
          <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
          <div className='text-md text-zinc-500'>
            <Link
              href={routeTo(
                '/configuration/index-rates/[effectiveDate]',
                route.params,
              )}
              className='font-bold'
            >
              Account type
            </Link>
          </div>
          <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
          <div className='text-lg'>{indexRate.name}</div>
          <div className='text-sm text-zinc-500'>{indexRate.code}</div>
        </div>
        <Button className='btn-primary text-white' onClick={onEditClick}>
          Edit index rate
        </Button>
      </div>
      <div className='mt-8 flex gap-4'>
        <div className='flex grow flex-col'>
          <DetailsSection>
            <DetailsSectionTitle>Index rate information</DetailsSectionTitle>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Effective date *'}
                info={indexRate.effectiveDate!}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem label={'Name *'} info={indexRate.name} />
              <DetailsSectionItem label={'Code *'} info={indexRate.code} />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Rate *'}
                info={`${indexRate.indexRate}%`}
              />
            </DetailsSectionItemsRow>
          </DetailsSection>
        </div>
        <div className='flex max-w-72 flex-col rounded-lg border bg-white p-6'>
          <VersionTimeline
            versions={indexRates}
            currentEffectiveDate={route.params.effectiveDate}
            linkFormatter={linkFormatter}
            shouldParseToDateLevel
          />
        </div>
      </div>
    </div>
  )
}
