'use client'

import { useFormTextInput } from '@/components/Form/useFormTextInput'
import { useFormMonthPicker } from '@/components/Form/useFormMonthPicker'
import {
  reserveRequirementInfoSchema,
  UpdateReserveRequirementFormApi,
  UpdateReserveRequirementFormState,
} from './UpdateReserveRequirementFormTypes'

interface ReserveRequirementInfoProps {
  form: UpdateReserveRequirementFormApi
  isEditMode?: boolean
}
export default function ReserveRequirementInfo({
  form,
  isEditMode,
}: ReserveRequirementInfoProps) {
  const FormInput = useFormTextInput<UpdateReserveRequirementFormState>({
    form,
  })
  const FormMonthPicker = useFormMonthPicker<UpdateReserveRequirementFormState>(
    {
      form,
    },
  )

  // todo: make call to get reserve requirement code. If list is empty, code is unique.
  return (
    <div className='mt-10 flex w-full flex-col rounded-lg border bg-white p-6'>
      <div className='font-semibold'>Reserve requirement information</div>
      <div className='mt-3 flex gap-9'>
        <div className='flex flex-1 flex-col gap-2'>
          <FormMonthPicker
            name='reserveRequirementInfo.effectiveDate'
            label='Effective date *'
          />
        </div>
        <div className='flex-1'></div>
      </div>
      <div className='mt-3 flex gap-9'>
        <FormInput
          name='reserveRequirementInfo.name'
          label='Name'
          required
          // TODO can be replaced with validator at useForm-level
          validators={{
            onChange: reserveRequirementInfoSchema.shape.name,
          }}
        />
        {/* todo: check reserve requirement code is unique */}
        <FormInput
          name='reserveRequirementInfo.code'
          label='Code'
          required
          // TODO can be replaced with validator at useForm-level
          validators={{
            onChange: reserveRequirementInfoSchema.shape.code,
          }}
          placeholder='Max 5 digits'
          type='number'
          invalidChars={['e', 'E']}
          readonly={(field) =>
            field.value != null &&
            field.value.length > 0 &&
            field.meta.isPristine &&
            !!isEditMode
          }
        />
      </div>
    </div>
  )
}
