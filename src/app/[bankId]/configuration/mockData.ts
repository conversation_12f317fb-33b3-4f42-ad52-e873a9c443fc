import {
  CalculationMethodOptions,
  TieringMethodType,
  UserFieldTypes,
} from './types'
import { ServicePriceTier } from '../services/[effectiveDate]/(service)/UpdateServiceForm'

export const mockAccountTypes = [
  {
    name: 'Blended Account Type',
    code: '12',
    effectiveDate: '2025-10',
  },
  {
    name: 'Commercial',
    code: '2',
    effectiveDate: '2025-10',
  },
  {
    name: 'Default Account Type',
    code: '99999',
    effectiveDate: '2025-10',
  },
  {
    name: 'Lead Account Type',
    code: '120',
    effectiveDate: '2025-10',
  },
  {
    name: 'Small Business',
    code: '4',
    effectiveDate: '2025-10',
  },
]

export const mockAccountType = {
  accountTypeInfo: {
    code: '2',
    name: 'Commercial',
    effectiveDate: '2025-01',
  },
  statementPlans: {
    statementFormat: 'Statement commercial plan - 99999',
    statementMessage: 'Statement summary - 12',
    statementSchedule: 'Biannual - 2',
  },
  analysisPlans: {
    investableBalance: 'Average positive collected less reserves - 99999',
    earningsCredit: '--',
    requiredBalance: 'Investable Balance with no adjustments - 99999',
    reserveRequirement: '--',
  },
  resultPlans: {
    settlementSchedule: '1',
    analysisResultOptions: 'Debit Charges and Waive Credits - 99999',
  },
}

export const mockAccountType2 = {
  accountTypeInfo: {
    code: '99999',
    name: 'Mock Account Type',
    effectiveDate: '2023-01',
  },
  statementPlans: {
    statementFormat: 'Statement commercial plan - 99999',
    statementMessage: '--',
    statementSchedule: 'Biannual - 2',
  },
  analysisPlans: {
    investableBalance: 'Average positive collected less reserves - 99999',
    earningsCredit: '--',
    requiredBalance: 'Investable Balance with no adjustments - 99999',
    reserveRequirement: '--',
  },
  resultPlans: {
    settlementSchedule: '1',
    analysisResultOptions: 'Debit Charges and Waive Credits - 99999',
  },
}
export const mockAccountType3 = {
  accountTypeInfo: {
    code: '99999',
    name: 'Mock Account Type',
    effectiveDate: '2021-01',
  },
  statementPlans: {
    statementFormat: 'Statement commercial plan - 99999',
    statementMessage: '--',
    statementSchedule: 'Biannual - 2',
  },
  analysisPlans: {
    investableBalance: 'Average positive collected less reserves - 99999',
    earningsCredit: '--',
    requiredBalance: 'Investable Balance with no adjustments - 99999',
    reserveRequirement: '--',
  },
  resultPlans: {
    settlementSchedule: '1',
    analysisResultOptions: 'Debit Charges and Waive Credits - 99999',
  },
}

export const mockScheduleDefinitions = [
  {
    name: 'Annual',
    code: '1',
    effectiveDate: '2025-01',
  },
  {
    name: 'Biannual',
    code: '2',
    effectiveDate: '2025-01',
  },
  {
    name: 'Custom - Annual Every March',
    code: '120',
    effectiveDate: '2025-01',
  },
  {
    name: 'Monthly',
    code: '12',
    effectiveDate: '2025-01',
  },
  {
    name: 'Quarterly',
    code: '4',
    effectiveDate: '2025-01',
  },
]

export const mockScheduleDefinitionA = {
  scheduleDefinitionInfo: {
    code: '1',
    name: 'Annual',
    effectiveDate: '2025-01',
  },
  scheduleDefinitionMonths: {
    monthsSelected: ['03', '09'],
  },
}

export const mockReserveRequirements = [
  {
    name: '10% reserve',
    code: '1',
    effectiveDate: '2025-10',
  },
  {
    name: '15% reserve',
    code: '2',
    effectiveDate: '2025-10',
  },
  {
    name: 'Floor/ceiling',
    code: '99999',
    effectiveDate: '2025-10',
  },
  {
    name: 'Floor/ceiling 2.0',
    code: '120',
    effectiveDate: '2025-10',
  },
  {
    name: 'Milwaukee commercial',
    code: '4',
    effectiveDate: '2025-10',
  },
]

export const mockReserveRequirement1 = {
  reserveRequirementInfo: {
    code: '1',
    name: '10% reserve',
    effectiveDate: '2025-02',
  },
  reserveRequirement: {
    baseBalanceType: 'Average collected',
  },
  calculationMethod: {
    calculationMethodType: CalculationMethodOptions['indexed'],
    // conditional fields based on calculation method:
    indexRateCode: '1',
    indexAdjustment: null,
    floor: null,
    ceiling: null,
    reserveRate: null,
  },
}

export const mockStatementFormatOptions = [
  { name: 'Business Statement Format', code: 1 },
  { name: 'Commercial Statement Format', code: 99999 },
  { name: 'Small Business Statement Format', code: 12 },
  { name: 'Standard plan', code: 4 },
]
export const mockStatementMessageOptions = [
  { name: 'Holiday', code: 1 },
  { name: 'New Customer message', code: 2 },
  { name: 'Statement Summary', code: 12 },
  { name: 'Upcoming Bank Merger', code: 4 },
]
export const mockStatementScheduleOptions = [
  { name: 'Annual', code: 1 },
  { name: 'Biannual', code: 2 },
  { name: 'Monthly', code: 12 },
  { name: 'Quarterly', code: 4 },
]
export const mockInvestableBalanceOptions = [
  { name: 'Average positive collected less reserves', code: 99999 },
  { name: 'Average collected less reserves', code: 2 },
  { name: 'Average positive collected less reserves', code: 12 },
  { name: 'Collected reserves', code: 4 },
]
export const mockEarningsCreditOptions = [
  { name: 'Blend value tests', code: 12 },
  { name: 'Index 101', code: 20 },
  { name: 'Index 101 Ave Coll', code: 99999 },
  { name: 'New earnings credit', code: 4 },
]
export const mockRequiredBalanceOptions = [
  { name: 'BASE= INV/E', code: 12 },
  { name: 'BASE= COL/E', code: 20 },
  { name: 'Investable Balance with no adjustments', code: 99999 },
  { name: 'INV + COMP', code: 4 },
]
export const mockReserveRequirementOptions = [
  { name: '10% reserve', code: 1 },
  { name: '15% reserve', code: 2 },
  { name: 'Floor/ceiling', code: 12 },
  { name: 'Floor/ceiling 2.0', code: 4 },
]
export const mockSettlementScheduleOptions = [
  { name: 'Annual', code: 1 },
  { name: 'Biannual', code: 2 },
  { name: 'Monthly', code: 12 },
  { name: 'Quarterly', code: 4 },
]
export const mockAnalysisResultOptions = [
  {
    name: 'Debit Charges and Waive Credits',
    code: '99999',
    effectiveDate: '2025-02',
    minChargeWaiveAmount: 10000,
    maxChargeWaiveAmount: 20000,
    analysisChargeType: 'Direct debt', //"enum": ["DIRECT_DEBIT","WAIVE"]
    analysisDirectDebitTrailer: 'Direct debt',
    hardCharge: 'Direct debt', //"enum": ["DIRECT_DEBIT","WAIVE"]
    hardDirectDebitTrailer: '',
    excessCredits: 'Waive', //"enum": ["WAIVE"]
    markdownRate: 2,
    markdownStatementLabel: 'Markdown rate',
    markupRate: 10,
    markupStatementLabel: 'Markup rate',
    waiveCycle: 0, //"format": "int32","type": "integer"
    settlementOverrideType: 'Same date each month', //"enum": ["SAME_DATE_EACH_MONTH","SPECIFIC_DAYS"]
    settlementOverrideDateEachMonth: 15,
    delay: 0,
    daysAfter: null,
  },
  {
    name: 'Detailed results',
    code: '20',
    effectiveDate: '2025-02',
    minChargeWaiveAmount: null,
    maxChargeWaiveAmount: null,
    analysisChargeType: 'Direct debt',
    analysisDirectDebitTrailer: 'Direct debt',
    hardCharge: 'Direct debt',
    hardDirectDebitTrailer: '',
    excessCredits: 'Waive',
    markdownRate: 2,
    markdownStatementLabel: 'Markdown rate',
    markupRate: 10,
    markupStatementLabel: 'Markup rate',
    waiveCycle: 3,
    settlementOverrideType: 'Same date each month',
    settlementOverrideDateEachMonth: 15,
    delay: 3,
    daysAfter: 1,
  },
  {
    name: 'Direct result',
    code: '12',
    effectiveDate: '2025-02',
    minChargeWaiveAmount: 5000,
    maxChargeWaiveAmount: 100000,
    analysisChargeType: 'Direct debt',
    analysisDirectDebitTrailer: 'Direct debt',
    hardCharge: 'Direct debt',
    hardDirectDebitTrailer: '',
    excessCredits: 'Waive',
    markdownRate: 2,
    markdownStatementLabel: 'Markdown rate',
    markupRate: 10,
    markupStatementLabel: 'Markup rate',
    waiveCycle: 5,
    settlementOverrideType: 'Same date each month',
    settlementOverrideDateEachMonth: 15,
    delay: 1,
    daysAfter: null,
  },
  {
    name: 'Result test plan',
    code: '4',
    effectiveDate: '2025-02',
    minChargeWaiveAmount: 2000,
    maxChargeWaiveAmount: 10000,
    analysisChargeType: 'Direct debt',
    analysisDirectDebitTrailer: 'Direct debt',
    hardCharge: 'Direct debt',
    hardDirectDebitTrailer: '',
    excessCredits: 'Waive',
    markdownRate: 2,
    markdownStatementLabel: 'Markdown rate',
    markupRate: 10,
    markupStatementLabel: 'Markup rate',
    waiveCycle: 7,
    settlementOverrideType:
      'Specific number of days after preliminary analysis',
    settlementOverrideDateEachMonth: null,
    delay: 12,
    daysAfter: 5,
  },
]
export const mockAnalysisResultOptionA = {
  name: 'Debit Charges and Waive Credits',
  code: '99999',
  effectiveDate: '2025-02',
  minChargeWaiveAmount: 100,
  maxChargeWaiveAmount: 10000,
  analysisChargeType: 'Direct debt',
  analysisDirectDebitTrailer: 'Direct debt',
  hardCharge: 'Direct debt',
  hardDirectDebitTrailer: '',
  excessCredits: 'Waive',
  markdownRate: 0.0,
  markdownStatementLabel: '',
  markupRate: 0.0,
  markupStatementLabel: '',
  waiveCycle: 5,
  settlementOverrideType: 'Same date each month',
  settlementOverrideDateEachMonth: 15,
  delay: 5,
  daysAfter: null,
}
export const mockAnalysisResultOptionB = {
  name: 'Debit Charges and Waive Credits',
  code: '99999',
  effectiveDate: '2024-01',
  minChargeWaiveAmount: 100,
  maxChargeWaiveAmount: '',
  analysisChargeType: 'Direct debt',
  analysisDirectDebitTrailer: 'Direct debt',
  hardCharge: 'Direct debt',
  hardDirectDebitTrailer: '',
  excessCredits: 'Waive',
  markdownRate: 0.0,
  markdownStatementLabel: '',
  markupRate: 0.0,
  markupStatementLabel: '',
  waiveCycle: 5,
  settlementOverrideType: 'Specific number of days after preliminary analysis',
  settlementOverrideDateEachMonth: null,
  delay: 5,
  daysAfter: 5,
}

export const mockUserFields = [
  {
    name: 'Internet banking',
    fieldType: UserFieldTypes.boolean,
    code: '123',
    createdAt: '2024-02-01',
    updatedAt: '2024-03-15',
    availablePriceList: false,
  },
  {
    name: 'ACH Services',
    fieldType: UserFieldTypes.boolean,
    code: '124',
    createdAt: '2024-03-01',
    updatedAt: '2024-04-15',
    availablePriceList: false,
  },
  {
    name: 'Phone number',
    fieldType: UserFieldTypes.freeform,
    code: '125',
    createdAt: '2024-04-01',
    updatedAt: '2024-05-15',
    availablePriceList: false,
  },
  {
    name: 'Risk level',
    fieldType: UserFieldTypes['drop-down'],
    code: '126',
    createdAt: '2024-05-01',
    updatedAt: '2024-06-15',
    availablePriceList: true,
    dropDownValues: ['Low', 'Medium', 'High'],
  },
]

export const mockIndexRateA = {
  code: '1',
  name: '30-Day Average',
  effectiveDate: '2025-01-23',
  indexRate: 2.89,
}
export const mockIndexRateB = {
  code: '1',
  name: '30-Day Average',
  effectiveDate: '2025-01-01',
  indexRate: 2.89,
}
export const mockIndexRates = [
  {
    name: '30-Day Average',
    code: '2',
    indexRate: 4.2,
    effectiveDate: '2025-01-23',
  },
  {
    name: 'Bank Prime',
    code: '3',
    indexRate: 2.89,
    effectiveDate: '2025-01-23',
  },
  {
    name: 'International Libor',
    code: '6',
    indexRate: 4.0,
    effectiveDate: '2025-01-23',
  },
  {
    name: 'Large Commercial',
    code: '4',
    indexRate: 8.000009,
    effectiveDate: '2025-01-23',
  },
  {
    name: 'National Bank',
    code: '1',
    indexRate: 35.789545,
    effectiveDate: '2025-01-23',
  },
]

export const mockEarningsCredits = [
  {
    name: 'Blend value tests',
    code: '9',
    effectiveDate: '2025-01',
  },
  {
    name: 'Index 101',
    code: '2',
    effectiveDate: '2025-01',
  },
  {
    name: 'Index 101 Ave Coll',
    code: '4',
    effectiveDate: '2025-01',
  },
  {
    name: 'New earnings credit',
    code: '12',
    effectiveDate: '2025-01',
  },
  {
    name: 'Western region plan',
    code: '120',
    effectiveDate: '2025-01',
  },
]

const tiers = [
  {
    priceValue: '1', // null, or zero
    tierMinVolumeInclusive: '0', // null, or zero
    tierMaxVolumeExclusive: '0', // null, or zero
    tierMinBalanceInclusive: '0', // server
    tierMaxBalanceExclusive: '5', // server
    indexAdjustment: '12', // server
  },
  {
    priceValue: '2',
    tierMinVolumeInclusive: '0',
    tierMaxVolumeExclusive: '0',
    tierMinBalanceInclusive: '6',
    tierMaxBalanceExclusive: '25',
    indexAdjustment: '24',
  },
  {
    priceValue: '3',
    tierMinVolumeInclusive: '0',
    tierMaxVolumeExclusive: '999999999',
    tierMinBalanceInclusive: '26',
    tierMaxBalanceExclusive: '999999999',
    indexAdjustment: '36',
  },
] as ServicePriceTier[]

export const mockEarningsCreditA = {
  earningsCreditInfo: {
    name: 'Blend value tests',
    code: '9',
    effectiveDate: '2025-01',
  },
  earningsCreditCalculation: {
    basedBalance: 'Investible balance',
    tieringMethod: TieringMethodType.threshold, // 'Partitioned
  },
  earningsCreditRate: {
    rateSource: 'Use index rate', // 'Manually Enter rate'
    indexRateOption: 'National average',
    minimumRate: 0.3,
    maximumRate: 6.5,
    subPriceType: 'INDEXED',
    tiers,
  },
  pricingTiers: tiers,
}

export const mockStatementFormats = [
  {
    name: 'Commercial Statement Format',
    code: '99999',
    effectiveDate: '2025-02',
    logo: 'company_logo_green.svg',
    sectionBorderColor: '#000000',
    sectionBackgroundColor: '#D9D9D9',
    sectionTextColor: '#000000',
    includeOfficerName: true,
    includeOfficerPhone: false,
    returnAddress: '226 E 14th St New York, NY 10003',
    statementMessage: true,
    statementMessageLocation: 'Top',
    statementMessageImages: [
      'statement_msg_img_1.svg',
      'statement_msg_img_2.svg',
      'statement_msg_img_3.svg',
    ],
    statementImageLocations: ['Top', 'Right', 'Left'],
    relationshipSummary: true,
    relationshipSummaryLabel: 'Relationship summary',
    relationshipSummaryLocation: 'Top',
    balanceSummary: true,
    balanceSummaryLabel: 'Balance summary',
    balanceSummaryLocation: 'Top',
    balanceSummarySize: 'Half page width',
    resultsSummaryLabel: 'Results summary',
    resultsSummaryLocation: 'Top',
    resultsSummarySize: 'Full page width',
    earningsCreditRate: 'Earnings credit definition',
    historicalSummaryType: 'Rolling 12 months',
    historicalSummaryLabel: 'Historical Analysis',
    historicalSummaryLocation: 'Bottom',
    dailyBalanceSummary: 'Yes - all accounts',
    dailyBalanceSummaryLabel: 'Daily balance summary',
    dailyBalanceSummaryLocation: 'Top',
    serviceDetailLabel: 'Service detail',
    serviceDetailLocation: 'Top',
    afpServiceCode: true,
    serviceCode: true,
    sortServicesType: 'By lowest to highest service code',
    requiredBalance: true,
    requiredBalanceMultiplier: true,
    servicesCategory: true,
    serviceCategorySort: 'Alphabetical based on service category name',
    serviceCategoryBackgroundColor: '#000000',
    serviceCategoryLevel: 1,
    boldServiceCategoryLabel: true,
    serviceCategorySubtotal: true,
    serviceCategorySubtotalBackgroundColor: '#FFFFFF',
    boldServiceCategorySubtotalLabel: true,
    serviceChargesDueBarChart: true,
    serviceChargesDueBarChartLabel: 'Service charges due bar chart',
    serviceChargesDueBarChartLocation: 'Left',
    serviceCategoryPieChart: true,
    serviceCategoryPieChartLabel: 'Service category pie chart',
    serviceCategoryPieChartLocation: 'Right',
  },
  {
    name: 'Standard plan',
    code: '12',
    effectiveDate: '2025-02',
    logo: 'company_logo_green.svg',
    sectionBorderColor: '#000000',
    sectionBackgroundColor: '#D9D9D9',
    sectionTextColor: '#000000',
    includeOfficerName: true,
    includeOfficerPhone: false,
    returnAddress: '226 E 14th St New York, NY 10003',
    statementMessage: true,
    statementMessageLocation: 'Top',
    statementMessageImages: [
      'statement_msg_img_1.svg',
      'statement_msg_img_2.svg',
      'statement_msg_img_3.svg',
    ],
    statementImageLocations: ['Top', 'Right', 'Left'],
    relationshipSummary: true,
    relationshipSummaryLabel: 'Relationship summary',
    relationshipSummaryLocation: 'Top',
    balanceSummary: true,
    balanceSummaryLabel: 'Balance summary',
    balanceSummaryLocation: 'Top',
    balanceSummarySize: 'Half page width',
    resultsSummaryLabel: 'Results summary',
    resultsSummaryLocation: 'Top',
    resultsSummarySize: 'Full page width',
    earningsCreditRate: 'Earnings credit definition',
    historicalSummaryType: 'Rolling 12 months',
    historicalSummaryLabel: 'Historical Analysis',
    historicalSummaryLocation: 'Bottom',
    dailyBalanceSummary: 'Yes - all accounts',
    dailyBalanceSummaryLabel: 'Daily balance summary',
    dailyBalanceSummaryLocation: 'Top',
    serviceDetailLabel: 'Service detail',
    serviceDetailLocation: 'Top',
    afpServiceCode: true,
    serviceCode: true,
    sortServicesType: 'By lowest to highest service code',
    requiredBalance: true,
    requiredBalanceMultiplier: true,
    servicesCategory: true,
    serviceCategorySort: 'Alphabetical based on service category name',
    serviceCategoryBackgroundColor: '#000000',
    serviceCategoryLevel: 1,
    boldServiceCategoryLabel: true,
    serviceCategorySubtotal: true,
    serviceCategorySubtotalBackgroundColor: '#FFFFFF',
    boldServiceCategorySubtotalLabel: true,
    serviceChargesDueBarChart: true,
    serviceChargesDueBarChartLabel: 'Service charges due bar chart',
    serviceChargesDueBarChartLocation: 'Left',
    serviceCategoryPieChart: true,
    serviceCategoryPieChartLabel: 'Service category pie chart',
    serviceCategoryPieChartLocation: 'Right',
  },
  {
    name: 'Business Statement Format',
    code: '120',
    effectiveDate: '2025-02',
    logo: 'company_logo_green.svg',
    sectionBorderColor: '#000000',
    sectionBackgroundColor: '#D9D9D9',
    sectionTextColor: '#000000',
    includeOfficerName: true,
    includeOfficerPhone: false,
    returnAddress: '226 E 14th St New York, NY 10003',
    statementMessage: true,
    statementMessageLocation: 'Top',
    statementMessageImages: [
      'statement_msg_img_1.svg',
      'statement_msg_img_2.svg',
      'statement_msg_img_3.svg',
    ],
    statementImageLocations: ['Top', 'Right', 'Left'],
    relationshipSummary: true,
    relationshipSummaryLabel: 'Relationship summary',
    relationshipSummaryLocation: 'Top',
    balanceSummary: true,
    balanceSummaryLabel: 'Balance summary',
    balanceSummaryLocation: 'Top',
    balanceSummarySize: 'Half page width',
    resultsSummaryLabel: 'Results summary',
    resultsSummaryLocation: 'Top',
    resultsSummarySize: 'Full page width',
    earningsCreditRate: 'Earnings credit definition',
    historicalSummaryType: 'Rolling 12 months',
    historicalSummaryLabel: 'Historical Analysis',
    historicalSummaryLocation: 'Bottom',
    dailyBalanceSummary: 'Yes - all accounts',
    dailyBalanceSummaryLabel: 'Daily balance summary',
    dailyBalanceSummaryLocation: 'Top',
    serviceDetailLabel: 'Service detail',
    serviceDetailLocation: 'Top',
    afpServiceCode: true,
    serviceCode: true,
    sortServicesType: 'By lowest to highest service code',
    requiredBalance: true,
    requiredBalanceMultiplier: true,
    servicesCategory: true,
    serviceCategorySort: 'Alphabetical based on service category name',
    serviceCategoryBackgroundColor: '#000000',
    serviceCategoryLevel: 1,
    boldServiceCategoryLabel: true,
    serviceCategorySubtotal: true,
    serviceCategorySubtotalBackgroundColor: '#FFFFFF',
    boldServiceCategorySubtotalLabel: true,
    serviceChargesDueBarChart: true,
    serviceChargesDueBarChartLabel: 'Service charges due bar chart',
    serviceChargesDueBarChartLocation: 'Left',
    serviceCategoryPieChart: true,
    serviceCategoryPieChartLabel: 'Service category pie chart',
    serviceCategoryPieChartLocation: 'Right',
  },
  {
    name: 'Small Business Statement Format',
    code: '4',
    effectiveDate: '2025-02',
    logo: 'company_logo_green.svg',
    sectionBorderColor: '#000000',
    sectionBackgroundColor: '#D9D9D9',
    sectionTextColor: '#000000',
    includeOfficerName: true,
    includeOfficerPhone: false,
    returnAddress: '226 E 14th St New York, NY 10003',
    statementMessage: true,
    statementMessageLocation: 'Top',
    statementMessageImages: [
      'statement_msg_img_1.svg',
      'statement_msg_img_2.svg',
      'statement_msg_img_3.svg',
    ],
    statementImageLocations: ['Top', 'Right', 'Left'],
    relationshipSummary: true,
    relationshipSummaryLabel: 'Relationship summary',
    relationshipSummaryLocation: 'Top',
    balanceSummary: true,
    balanceSummaryLabel: 'Balance summary',
    balanceSummaryLocation: 'Top',
    balanceSummarySize: 'Half page width',
    resultsSummaryLabel: 'Results summary',
    resultsSummaryLocation: 'Top',
    resultsSummarySize: 'Full page width',
    earningsCreditRate: 'Earnings credit definition',
    historicalSummaryType: 'Rolling 12 months',
    historicalSummaryLabel: 'Historical Analysis',
    historicalSummaryLocation: 'Bottom',
    dailyBalanceSummary: 'Yes - all accounts',
    dailyBalanceSummaryLabel: 'Daily balance summary',
    dailyBalanceSummaryLocation: 'Top',
    serviceDetailLabel: 'Service detail',
    serviceDetailLocation: 'Top',
    afpServiceCode: true,
    serviceCode: true,
    sortServicesType: 'By lowest to highest service code',
    requiredBalance: true,
    requiredBalanceMultiplier: true,
    servicesCategory: true,
    serviceCategorySort: 'Alphabetical based on service category name',
    serviceCategoryBackgroundColor: '#000000',
    serviceCategoryLevel: 1,
    boldServiceCategoryLabel: true,
    serviceCategorySubtotal: true,
    serviceCategorySubtotalBackgroundColor: '#FFFFFF',
    boldServiceCategorySubtotalLabel: true,
    serviceChargesDueBarChart: true,
    serviceChargesDueBarChartLabel: 'Service charges due bar chart',
    serviceChargesDueBarChartLocation: 'Left',
    serviceCategoryPieChart: true,
    serviceCategoryPieChartLabel: 'Service category pie chart',
    serviceCategoryPieChartLocation: 'Right',
  },
]

export const mockStatementMessages = [
  {
    code: '12',
    effectiveDate: '2025-01',
    name: 'Holiday',
    message: '<p>Happy holidays from the bank!</p>',
  },
  {
    code: '1',
    effectiveDate: '2025-01',
    name: 'New Customer Message',
    message: '<p>New Customer Message Here</p>',
  },
  {
    code: '2',
    effectiveDate: '2025-01',
    name: 'Statement Summary',
    message: '<p>Statement Summary Text Here</p>',
  },
  {
    code: '120',
    effectiveDate: '2025-01',
    name: 'Upcoming Bank Merger',
    message: '<p>Happy holidays from the bank!</p>',
  },
  {
    code: '12345',
    effectiveDate: '2025-01',
    name: 'Welcome',
    message:
      '<p dir="ltr"><span data-lexical-text="true">Welcome to First National Bank of Southeastern Wisconsin. Your trusted banking partner for over 100 years.</span></p><p><br></p><p dir="ltr"><span data-lexical-text="true">We are proud to announce pricing reductions in response to the economic situation. Please call </span><strong class="font-bold" data-lexical-text="true">**************</strong><span data-lexical-text="true"> or email </span><u><EMAIL></u><span data-lexical-text="true"> for a free consultation.</span></p>',
  },
]

export const mockOfficers = [
  {
    name: 'Annalise Gray',
    code: '2',
    effectiveDate: '2025-01-23',
    bank: '098 - XAA National Bank',
    phone: '(234)345-1234',
  },
  {
    name: 'Austin Smith',
    code: '3',
    effectiveDate: '2025-01-23',
    bank: '098 - XAA National Bank',
    phone: '(234)345-1234',
  },
  {
    name: 'Ben Drake',
    code: '6',
    effectiveDate: '2025-01-23',
    bank: '098 - XAA National Bank',
    phone: '(234)345-1234',
  },
  {
    name: 'Emmett Fitzgerald',
    code: '4',
    effectiveDate: '2025-01-23',
    bank: '098 - XAA National Bank',
    phone: '(234)345-1234',
  },
  {
    name: 'Jane Doe',
    code: '1',
    effectiveDate: '2025-01-23',
    bank: '098 - XAA National Bank',
    phone: '(234)345-1234',
  },
]

export const mockBranches = [
  {
    name: 'Austen Office',
    code: '2',
    effectiveDate: '2025-01-23',
    bank: '098 - XAA National Bank',
    accountNumber: '234516879403256839456',
    addressLine1: '1 E Alder St',
    addressLine2: 'Austen, TX 78701-1904',
    addressLine3: '',
    abaNumbers: ['1234'],
    phone: '(234)345-1234',
  },
  {
    name: 'Boston Office',
    code: '3',
    effectiveDate: '2025-01-23',
    bank: '098 - XAA National Bank',
    accountNumber: '234516879403256839456',
    addressLine1: '1 E Alder St',
    addressLine2: 'Boston, MA 02021-1904',
    addressLine3: '',
    abaNumbers: ['1234'],
    phone: '(234)345-1234',
  },
  {
    name: 'Chicago Office',
    code: '1',
    effectiveDate: '2025-01-23',
    bank: '098 - XAA National Bank',
    accountNumber: '234516879403256839456',
    addressLine1: '1 E Alder St',
    addressLine2: 'Chicago, IL 60601-1904',
    addressLine3: '',
    abaNumbers: ['1234'],
    phone: '(234)345-1234',
  },
  {
    name: 'Miami Office',
    code: '6',
    effectiveDate: '2025-01-23',
    bank: '098 - XAA National Bank',
    accountNumber: '234516879403256839456',
    addressLine1: '1 E Alder St',
    addressLine2: 'Miami, FL 33178-1904',
    addressLine3: '',
    abaNumbers: ['1234'],
    phone: '(234)345-1234',
  },
  {
    name: 'Seattle Office',
    code: '4',
    effectiveDate: '2025-01-23',
    bank: '098 - XAA National Bank',
    accountNumber: '234516879403256839456',
    addressLine1: '1 E Alder St',
    addressLine2: 'Seattle, WA 98101-1904',
    addressLine3: '',
    abaNumbers: ['1234'],
    phone: '(234)345-1234',
  },
]

/**
 * TODO: Remove mock constants once more info is provided by CSF, and integration and BE is complete
 * The statement-logo, statement-images, and *-locations are provided by CSF
 * In the meantime, the options for these fields are hardcoded on the FE
 */
export const mockLogoFileNames = ['logo-file-1.svg', 'logo-file-2.svg']
export const mockStatementImages = [
  'statement-image-1.svg',
  'statement-image-2.svg',
  'statement-image-3.svg',
  'statement-image-4.svg',
]
export const mockLocations = [
  'CSF-location-1',
  'CSF-location-2',
  'CSF-location-3',
  'CSF-location-4',
  'CSF-location-5',
  'CSF-location-6',
]
/**
 *  TODO: Remove mock address options below once Configurations for Branch creation is completed
 *  Branches must be created and the Branch Name & Address can be fetched
 */
export const mockReturnAddresses = [
  'Branch address 1 - 123 Main Street, San Diego, CA 92101',
  'Branch address 2 - 123 Gold Street, New York, NY 10010',
]
