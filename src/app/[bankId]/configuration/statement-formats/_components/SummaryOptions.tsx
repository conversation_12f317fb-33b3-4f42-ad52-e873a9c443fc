'use client'
import { useStore } from '@tanstack/react-form'
import { InformationCircleIcon } from '@heroicons/react/24/outline'

import { Checkbox } from '@/components/Checkbox'
import { useFormSelect } from '@/components/Form/useFormSelect'
import { useFormTextInput } from '@/components/Form/useFormTextInput'
import { Tooltip } from '@/components/Tooltip'
import { If } from '@/components/If'
import {
  summaryOptionsSchema,
  UpdateStatementFormatsFormApi,
  UpdateStatementFormatsFormState,
  historicalSummaryTypeShape,
  historicalSummaryTypeLabel,
  earningsCreditRateLabel,
  earningsCreditRateShape,
  dailyBalanceSummaryShape,
  dailyBalanceSummaryLabel,
  balanceSummarySizeLabel,
  balanceSummarySizeShape,
} from './UpdateStatementFormatsFormTypes'
import { mockLocations } from '../../mockData'

interface SummaryOptionsProps {
  form: UpdateStatementFormatsFormApi
}
export default function SummaryOptions({ form }: SummaryOptionsProps) {
  const Select = useFormSelect<UpdateStatementFormatsFormState>({ form })
  const FormInput = useFormTextInput<UpdateStatementFormatsFormState>({
    form,
  })

  const [
    relationshipSummary,
    balanceSummary,
    historicalSummaryType,
    dailyBalanceSummary,
  ] = useStore(form.store, (state) => [
    state.values.summaryOptions.relationshipSummary,
    state.values.summaryOptions.balanceSummary,
    state.values.summaryOptions.historicalSummaryType,
    state.values.summaryOptions.dailyBalanceSummary,
  ])
  return (
    <div className='flex w-full flex-col rounded-lg border bg-white p-6'>
      <div className='font-semibold'>Summary options</div>
      <div className='mt-3 flex gap-1'>
        <form.Field name='summaryOptions.relationshipSummary'>
          {({ state: { value }, handleChange }) => (
            <Checkbox
              label='Relationship summary'
              onChange={handleChange}
              checked={value}
            />
          )}
        </form.Field>
        <Tooltip className='size-4 self-center' content='Relationship summary'>
          <InformationCircleIcon />
        </Tooltip>
      </div>
      <If true={relationshipSummary === true}>
        <div className='mt-3 flex gap-9'>
          <FormInput
            name='summaryOptions.relationshipSummaryLabel'
            label='Label'
            // TODO can be replaced with validator at useForm-level
            validators={{
              onChange: summaryOptionsSchema.shape.relationshipSummaryLabel,
            }}
            required
          />
          <Select
            name='summaryOptions.relationshipSummaryLocation'
            label='Location'
            // TODO can be replaced with validator at useForm-level
            validators={{
              onChange: summaryOptionsSchema.shape.relationshipSummaryLocation,
            }}
            /**
             * TODO: Update location options once CSF integration with BE is complete
             * Locations are provided by CSF
             */
            options={mockLocations}
            required
          />
        </div>
      </If>
      <div className='mt-2 flex gap-1'>
        <form.Field name='summaryOptions.balanceSummary'>
          {({ state: { value }, handleChange }) => (
            <Checkbox
              label='Balance summary'
              onChange={handleChange}
              checked={value}
            />
          )}
        </form.Field>
      </div>
      <If true={balanceSummary === true}>
        <div className='mt-3 flex gap-9'>
          <FormInput
            name='summaryOptions.balanceSummaryLabel'
            label='Label'
            // TODO can be replaced with validator at useForm-level
            validators={{
              onChange: summaryOptionsSchema.shape.balanceSummaryLabel,
            }}
            required
          />
          <Select
            name='summaryOptions.balanceSummarySize'
            label='Size'
            // TODO can be replaced with validator at useForm-level
            validators={{
              onChange: summaryOptionsSchema.shape.balanceSummarySize,
            }}
            options={balanceSummarySizeShape.options}
            renderOption={balanceSummarySizeLabel}
            renderSelected={balanceSummarySizeLabel}
            required
          />
        </div>
        <div className='mt-1 flex gap-9'>
          <Select
            name='summaryOptions.balanceSummaryLocation'
            label='Location'
            // TODO can be replaced with validator at useForm-level
            validators={{
              onChange: summaryOptionsSchema.shape.balanceSummaryLocation,
            }}
            /**
             * TODO: Update location options once CSF integration with BE is complete
             * Locations are provided by CSF
             */
            options={mockLocations}
            required
          />
          <div className='flex-1'></div>
        </div>
      </If>
      <div className='mt-2 flex gap-9'>
        <FormInput
          name='summaryOptions.resultsSummaryLabel'
          label='Results summary label'
          // TODO can be replaced with validator at useForm-level
          validators={{
            onChange: summaryOptionsSchema.shape.resultsSummaryLabel,
          }}
          required
        />
        <Select
          name='summaryOptions.resultsSummarySize'
          label='Size'
          // TODO can be replaced with validator at useForm-level
          validators={{
            onChange: summaryOptionsSchema.shape.resultsSummarySize,
          }}
          options={['Full page width', 'Half page width']}
          required
        />
      </div>
      <div className='mt-1 flex gap-9'>
        <Select
          name='summaryOptions.resultsSummaryLocation'
          label='Location'
          // TODO can be replaced with validator at useForm-level
          validators={{
            onChange: summaryOptionsSchema.shape.resultsSummaryLocation,
          }}
          /**
           * TODO: Update location options once CSF integration with BE is complete
           * Locations are provided by CSF
           */
          options={mockLocations}
          required
        />
        <div className='flex-1'></div>
      </div>
      <div className='mt-2 flex gap-9'>
        <Select
          tooltip='Earnings credit row in Results Summary will only be shown for account types where an earnings credit plan is assigned.'
          name='summaryOptions.earningsCreditRate'
          label='Earnings credit rate'
          // TODO can be replaced with validator at useForm-level
          validators={{
            onChange: summaryOptionsSchema.shape.earningsCreditRate,
          }}
          options={earningsCreditRateShape.options}
          renderOption={earningsCreditRateLabel}
          renderSelected={earningsCreditRateLabel}
          required
        />
        <div className='flex-1'></div>
      </div>

      <div className='mt-1 flex gap-9'>
        <Select
          name='summaryOptions.historicalSummaryType'
          label='Historical analysis summary'
          // TODO can be replaced with validator at useForm-level
          validators={{
            onChange: summaryOptionsSchema.shape.historicalSummaryType,
          }}
          options={historicalSummaryTypeShape.options}
          renderSelected={historicalSummaryTypeLabel}
          renderOption={historicalSummaryTypeLabel}
          required
        />
        <div className='flex-1'></div>
      </div>
      <If true={!!historicalSummaryType && historicalSummaryType !== 'NO'}>
        <div className='mt-1 flex gap-9'>
          <FormInput
            name='summaryOptions.historicalSummaryLabel'
            label='Label'
            // TODO can be replaced with validator at useForm-level
            validators={{
              onChange: summaryOptionsSchema.shape.historicalSummaryLabel,
            }}
            required
          />
          <Select
            name='summaryOptions.historicalSummaryLocation'
            label='Location'
            // TODO can be replaced with validator at useForm-level
            validators={{
              onChange: summaryOptionsSchema.shape.historicalSummaryLocation,
            }}
            options={mockLocations}
            required
          />
        </div>
      </If>

      <div className='mt-1 flex gap-9'>
        <Select
          name='summaryOptions.dailyBalanceSummary'
          label='Daily balance summary'
          // TODO can be replaced with validator at useForm-level
          validators={{
            onChange: summaryOptionsSchema.shape.dailyBalanceSummary,
          }}
          options={dailyBalanceSummaryShape.options}
          renderSelected={dailyBalanceSummaryLabel}
          renderOption={dailyBalanceSummaryLabel}
          required
        />
        <div className='flex-1'></div>
      </div>
      <If true={!!dailyBalanceSummary && dailyBalanceSummary !== 'NO'}>
        <div className='mt-1 flex gap-9'>
          <FormInput
            name='summaryOptions.dailyBalanceSummaryLabel'
            label='Label'
            // TODO can be replaced with validator at useForm-level
            validators={{
              onChange: summaryOptionsSchema.shape.dailyBalanceSummaryLabel,
            }}
            required
          />
          <Select
            name='summaryOptions.dailyBalanceSummaryLocation'
            label='Location'
            // TODO can be replaced with validator at useForm-level
            validators={{
              onChange: summaryOptionsSchema.shape.dailyBalanceSummaryLocation,
            }}
            /**
             * TODO: Update location options once CSF integration with BE is complete
             * Locations are provided by CSF
             */
            options={mockLocations}
            required
          />
        </div>
      </If>
    </div>
  )
}
