'use client'

import { Checkbox } from '@/components/Checkbox'
import { useFormTextInput } from '@/components/Form/useFormTextInput'
import { useFormSelect } from '@/components/Form/useFormSelect'
import { useFormColorPicker } from '@/components/Form/useFormColorPicker'
import {
  headerAndVisualFormattingSchema,
  UpdateStatementFormatsFormApi,
  UpdateStatementFormatsFormState,
} from './UpdateStatementFormatsFormTypes'
import { mockLogoFileNames, mockReturnAddresses } from '../../mockData'
interface HeaderAndVisualFormattingProps {
  form: UpdateStatementFormatsFormApi
}
export default function HeaderAndVisualFormatting({
  form,
}: HeaderAndVisualFormattingProps) {
  const FormInput = useFormTextInput<UpdateStatementFormatsFormState>({
    form,
  })
  const Select = useFormSelect<UpdateStatementFormatsFormState>({ form })
  const ColorPicker = useFormColorPicker<UpdateStatementFormatsFormState>({
    form,
  })
  return (
    <div className='flex w-full flex-col rounded-lg border bg-white p-6'>
      <div className='font-semibold'>Header and visual formatting</div>
      <div className='mt-3 flex gap-9'>
        <Select
          name='headerAndVisualFormatting.headerLogoFileName'
          label='Logo'
          // TODO can be replaced with validator at useForm-level
          validators={{
            onChange: headerAndVisualFormattingSchema.shape.headerLogoFileName,
          }}
          /**
           *  TODO: Update logo options below once BE integration with CSF is completed
           */
          options={mockLogoFileNames}
          required
        />
        <div className='flex-1'></div>
      </div>

      <div className='flex gap-9'>
        <ColorPicker
          name='headerAndVisualFormatting.sectionBorderColor'
          label='Section border color'
          // TODO can be replaced with validator at useForm-level
          validators={{
            onChange: headerAndVisualFormattingSchema.shape.sectionBorderColor,
          }}
          required
        />
        <ColorPicker
          name='headerAndVisualFormatting.sectionBackgroundColor'
          label='Section background color'
          // TODO can be replaced with validator at useForm-level
          validators={{
            onChange:
              headerAndVisualFormattingSchema.shape.sectionBackgroundColor,
          }}
          required
        />
      </div>
      <div className='flex gap-9'>
        <ColorPicker
          name='headerAndVisualFormatting.sectionTextColor'
          label='Section text color'
          // TODO can be replaced with validator at useForm-level
          validators={{
            onChange: headerAndVisualFormattingSchema.shape.sectionTextColor,
          }}
          required
        />
        <div className='flex-1'></div>
      </div>
      <div className=''>
        <form.Field name='headerAndVisualFormatting.includeOfficerName'>
          {({ state: { value }, handleChange }) => (
            <Checkbox
              label='Include officer name'
              onChange={handleChange}
              checked={value}
            />
          )}
        </form.Field>
      </div>
      <div className='mt-3'>
        <form.Field name='headerAndVisualFormatting.includeOfficerPhone'>
          {({ state: { value }, handleChange }) => (
            <Checkbox
              label='Include officer phone'
              onChange={handleChange}
              checked={value}
            />
          )}
        </form.Field>
      </div>
      <div className='mt-3 flex gap-9'>
        <Select
          name='headerAndVisualFormatting.returnAddress'
          label='Return address'
          // TODO can be replaced with validator at useForm-level
          validators={{
            onChange: headerAndVisualFormattingSchema.shape.returnAddress,
          }}
          /**
           *  TODO: Update address options below once Configurations for Branch creation is completed
           *  Branch is currently view-only, and can not be created in the UI
           *  Branches must be created and the Branch Name & Address can be fetched
           */
          options={mockReturnAddresses}
        />
        <div className='flex-1'></div>
      </div>
    </div>
  )
}
