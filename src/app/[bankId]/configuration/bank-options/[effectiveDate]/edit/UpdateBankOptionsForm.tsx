import {
  DetailsSection,
  DetailsSectionItemsRow,
  DetailsSectionTitle,
} from '@/components/DetailsSection'
import { useFormMonthPicker } from '@/components/Form/useFormMonthPicker'
import { useFormSelect } from '@/components/Form/useFormSelect'
import { useFormTextInput } from '@/components/Form/useFormTextInput'
import { useForm, useStore } from '@tanstack/react-form'
import { enumOptionRenderers, enumOptions } from '../../helpers'
import { BankOptionsForm, BankOptionsFormZodSchema } from '../../types'
import { Checkbox } from '@/components/Checkbox'

interface UpdateBankOptionsFormProps {
  defaultValues: BankOptionsForm
  onSubmit: (state: BankOptionsForm) => void
}

export function UpdateBankOptionsForm({
  defaultValues,
  onSubmit,
  children,
}: React.PropsWithChildren<UpdateBankOptionsFormProps>) {
  const form = useForm({
    defaultValues,
    validators: {
      onChange: BankOptionsFormZodSchema,
    },
    onSubmit: ({ value }) => {
      onSubmit(value)
    },
  })

  const FormMonthPicker = useFormMonthPicker<BankOptionsForm>({
    form,
  })

  const FormInput = useFormTextInput<BankOptionsForm>({
    form,
  })

  const Select = useFormSelect<BankOptionsForm>({
    form,
  })

  const finalAnalysis = useStore(
    form.store,
    (state) => state.values.finalAnalysis,
  )

  return (
    <form
      className='flex min-h-screen flex-col'
      onSubmit={async (event) => {
        form.reset(form.state.values)
        event.preventDefault()
        event.stopPropagation()
        await form.handleSubmit()
      }}
    >
      <div className='mx-8 mt-8 flex flex-col gap-4'>
        <div className='flex grow flex-col'>
          <DetailsSection className='pb-0'>
            <DetailsSectionTitle>Bank options information</DetailsSectionTitle>
            <DetailsSectionItemsRow className='gap-9'>
              <div className='flex flex-1 flex-col gap-2'>
                <FormMonthPicker
                  name='effectiveDate'
                  label='Effective date'
                  required
                />
              </div>
              <div className='flex-1'></div>
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow className='gap-9'>
              <FormInput name='name' label='Name' required />
              <FormInput name='code' label='Code' required disabled />
            </DetailsSectionItemsRow>
          </DetailsSection>
        </div>
        <div className='flex grow flex-col'>
          <DetailsSection>
            <DetailsSectionTitle>Analysis processing dates</DetailsSectionTitle>
            <DetailsSectionItemsRow className='gap-9'>
              <Select
                name='prelimAnalysis'
                label='Preliminary analysis day each month'
                required
                options={enumOptions.prelimAnalysis}
                renderOption={enumOptionRenderers.prelimAnalysis}
                renderSelected={enumOptionRenderers.prelimAnalysis}
              />
              <div className='flex-1'></div>
            </DetailsSectionItemsRow>

            <DetailsSectionItemsRow className='!mt-0 gap-9'>
              <Select
                name='finalAnalysis'
                label='Final analysis'
                required
                options={enumOptions.finalAnalysis}
                renderOption={enumOptionRenderers.finalAnalysis}
                renderSelected={enumOptionRenderers.finalAnalysis}
              />
              <div className='flex-1'></div>
            </DetailsSectionItemsRow>

            <DetailsSectionItemsRow className='!mt-0 gap-9'>
              <FormInput
                name='finalAnalysisDays'
                label={
                  finalAnalysis === 'SAME_DATE_EACH_MONTH' ? 'Date each month'
                  : 'Number of days after prelims'
                }
                required
              />
              <div className='flex-1'></div>
            </DetailsSectionItemsRow>

            <DetailsSectionItemsRow className='!mt-0 gap-9'>
              <form.Field name='automaticPrelimAnalysis'>
                {(field) => (
                  <Checkbox
                    checked={field.state.value}
                    label='Automatic daily analysis of accounts in preliminary status'
                    onChange={(checked: boolean) => {
                      field.setValue(checked)
                      field.handleChange(checked)
                    }}
                  />
                )}
              </form.Field>
              <div className='flex-1'></div>
            </DetailsSectionItemsRow>
          </DetailsSection>
        </div>
        <div className='flex grow flex-col'>
          <DetailsSection className='pb-0'>
            <DetailsSectionTitle>Balance calculations</DetailsSectionTitle>
            <DetailsSectionItemsRow className='gap-9'>
              <Select
                name='balanceCycleDays'
                label='Balance cycle days'
                required
                options={enumOptions.balanceCycleDays}
                renderOption={enumOptionRenderers.balanceCycleDays}
                renderSelected={enumOptionRenderers.balanceCycleDays}
              />
              <Select
                name='calculatingBalanceFeeBasis'
                label='Basis for calculating balance fee'
                required
                options={enumOptions.calculatingBalanceFeeBasis}
                renderOption={enumOptionRenderers.calculatingBalanceFeeBasis}
                renderSelected={enumOptionRenderers.calculatingBalanceFeeBasis}
              />
            </DetailsSectionItemsRow>
          </DetailsSection>
        </div>
        <div className='flex grow flex-col'>
          <DetailsSection className='pb-0'>
            <DetailsSectionTitle>
              Earnings credit calculations
            </DetailsSectionTitle>
            <DetailsSectionItemsRow className='gap-9'>
              <Select
                name='earningsCycleDays'
                label='Earnings credit cycle days'
                required
                options={enumOptions.earningsCycleDays}
                renderOption={enumOptionRenderers.earningsCycleDays}
                renderSelected={enumOptionRenderers.earningsCycleDays}
              />
              <Select
                name='calculatingEarningsCreditBasis'
                label='Basis for calculating earnings credit'
                required
                options={enumOptions.calculatingEarningsCreditBasis}
                renderOption={
                  enumOptionRenderers.calculatingEarningsCreditBasis
                }
                renderSelected={
                  enumOptionRenderers.calculatingEarningsCreditBasis
                }
              />
            </DetailsSectionItemsRow>
          </DetailsSection>
        </div>
        <div className='flex grow flex-col'>
          <DetailsSection>
            <DetailsSectionTitle>
              Live link with key account
            </DetailsSectionTitle>
            <DetailsSectionItemsRow className='gap-9'>
              <form.Field name='copyAccountTypeOfKeyAccount'>
                {(field) => (
                  <Checkbox
                    checked={field.state.value}
                    label='Copy analysis type from key account'
                    onChange={(checked: boolean) => {
                      field.setValue(checked)
                      field.handleChange(checked)
                    }}
                  />
                )}
              </form.Field>
              <div className='flex-1'></div>
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow className='gap-9'>
              <form.Field name='copyUserFieldsFromKeyAccount'>
                {(field) => (
                  <Checkbox
                    checked={field.state.value}
                    label='Copy user fields from key account'
                    onChange={(checked: boolean) => {
                      field.setValue(checked)
                      field.handleChange(checked)
                    }}
                  />
                )}
              </form.Field>
              <div className='flex-1'></div>
            </DetailsSectionItemsRow>
          </DetailsSection>
        </div>
        <div className='flex grow flex-col'>
          <DetailsSection className='pb-0'>
            <DetailsSectionTitle>Currency</DetailsSectionTitle>
            <DetailsSectionItemsRow className='gap-9'>
              <Select
                name='defaultCurrency'
                label='Default currency'
                required
                disabled
                options={enumOptions.defaultCurrency}
                renderOption={enumOptionRenderers.defaultCurrency}
                renderSelected={enumOptionRenderers.defaultCurrency}
              />
              <div className='flex-1'></div>
            </DetailsSectionItemsRow>
          </DetailsSection>
        </div>
        <div className='flex grow flex-col'>
          <DetailsSection className='pb-0'>
            <DetailsSectionTitle>History retention</DetailsSectionTitle>
            <DetailsSectionItemsRow className='gap-9'>
              <FormInput
                name='retentionMonths'
                label='Number of months'
                required
              />
              <div className='flex-1'></div>
            </DetailsSectionItemsRow>
          </DetailsSection>
        </div>
        <div className='flex grow flex-col'>
          <DetailsSection className='pb-0'>
            <DetailsSectionTitle>Statement archiving</DetailsSectionTitle>
            <DetailsSectionItemsRow className='gap-9'>
              <Select
                name='statementArchivingFrequency'
                label='Frequency'
                required
                options={enumOptions.statementArchivingFrequency}
                renderOption={enumOptionRenderers.statementArchivingFrequency}
                renderSelected={enumOptionRenderers.statementArchivingFrequency}
              />
              <div className='flex-1'></div>
            </DetailsSectionItemsRow>
          </DetailsSection>
        </div>
        <div className='flex grow flex-col'>
          <DetailsSection>
            <DetailsSectionTitle>Account number structure</DetailsSectionTitle>
            <DetailsSectionItemsRow className='gap-9'>
              <FormInput
                name='accountNumberDigits'
                label='Number of digits'
                required
              />
              <FormInput
                name='accountNumberPattern'
                label='Number Format'
                required
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow className='!mt-0 gap-9'>
              <form.Field name='accountNumberMasking'>
                {(field) => (
                  <Checkbox
                    checked={field.state.value}
                    label='Mask account number on statements'
                    onChange={(checked: boolean) => {
                      field.setValue(checked)
                      field.handleChange(checked)
                    }}
                  />
                )}
              </form.Field>
              <div className='flex-1'></div>
            </DetailsSectionItemsRow>
          </DetailsSection>
        </div>
        <div className='mb-6 flex grow flex-col'>
          <DetailsSection className='pb-0'>
            <DetailsSectionTitle>Miscellaneous</DetailsSectionTitle>
            <DetailsSectionItemsRow className='gap-9'>
              <form.Field name='assignDefaultStatementPackageToDepositAccount'>
                {(field) => (
                  <Checkbox
                    checked={field.state.value}
                    label='Assign default statement package to deposit accounts'
                    onChange={(checked: boolean) => {
                      field.setValue(checked)
                      field.handleChange(checked)
                    }}
                  />
                )}
              </form.Field>
              <div className='flex-1'></div>
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow className='gap-9'>
              <FormInput
                name='defaultTermInMonthsForPromoAndOverrides'
                label='Default term in months for promotions and overrides'
                required
              />
              <div className='flex-1'></div>
            </DetailsSectionItemsRow>
          </DetailsSection>
        </div>
      </div>
      <div className='sticky bottom-0 mt-auto flex justify-end gap-4 border-t bg-white px-12 py-10'>
        {children}
      </div>
    </form>
  )
}
