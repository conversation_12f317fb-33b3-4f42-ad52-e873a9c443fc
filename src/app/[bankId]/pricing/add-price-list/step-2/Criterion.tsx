import { Select, SelectOption } from '@/components/Input/Select'
import { FieldApi } from '@tanstack/react-form'
import { useQuery } from '@tanstack/react-query'
import { query } from '../../[effectiveDate]/queries'
import { isUnique } from '@/lib/functional/isUnique'
import { isArrayEqual } from '@/lib/functional/isArrayEqual'
import { CriteriaFieldDefinition, CriterionField } from './CriterionField'
import { ChevronRightIcon, TrashIcon } from '@heroicons/react/24/outline'
import { Disclosure, DisclosureButton } from '@headlessui/react'
import { If } from '@/components/If'
import { Fragment, useMemo } from 'react'
import { HydratedDemographicPriceListForm } from '@/api/formToApiSchema'
import { branchQueries } from '@/app/[bankId]/configuration/branches/queries'
import { getLastDayOfMonthString } from '@/lib/date'
import { accountQueries } from '@/app/[bankId]/accounts/queries'

export type CriterionProps = {
  field: FieldApi<HydratedDemographicPriceListForm, 'demographicCriteria'>
  priceListCode: HydratedDemographicPriceListForm['demographicCriteria'][number]['priceListCode']
  criteriaCode: HydratedDemographicPriceListForm['demographicCriteria'][number]['criteriaCode']
  effectiveDate: string
  name: string
  onDelete?: (criteriaCode: string) => void
  // viewOnly: for view criteria i.e., read only mode.
  viewOnly?: boolean
}

export function Criterion({
  field,
  priceListCode,
  criteriaCode,
  effectiveDate,
  name,
  onDelete,
  viewOnly = false,
}: CriterionProps) {
  //TODO add deposit Type and Bank code when those features are supported based off of this PRD:
  // https://fisglobal.sharepoint.com/:w:/r/teams/PlatformsandEnterpriseProductOrg-AFEngineering/_layouts/15/Doc.aspx?sourcedoc=%7B39108E22-8D50-490B-A3B8-CF2A2DBD3E9A%7D&file=Price%20List%20based%20on%20Demographics%20-%20Configuration.docx&nav=********************&action=default&mobileredirect=true
  const {
    data: userFields,
    error,
    status,
  } = useQuery(query('/getUserFieldsConfigurations'))

  const accountTypesResult = useQuery(
    query('/getAccountTypes', { effectiveDate }),
  )

  const branchesResult = useQuery(
    branchQueries('/getBranches', {
      effectiveDate: getLastDayOfMonthString(effectiveDate),
    }),
  )

  // costCenter is a string
  const accountsResult = useQuery(
    accountQueries('/getAccounts', {
      effectiveDate: effectiveDate,
    }),
  )

  if (
    !accountTypesResult.isSuccess ||
    !branchesResult.isSuccess ||
    !accountsResult.isSuccess
  ) {
    return null
  }

  if (status === 'error') return <div>{error.message}</div>
  if (status === 'pending') return

  // We have a hardcoded list of Account fields and a dynamic list of UserFields
  // (loaded from an API), and together these form a list of all the fields the
  // user can choose from when adding conditions to this criterion.

  const criteriaFieldDefinitions: CriteriaFieldDefinition[] = userFields
    .filter((f) => f.availableForPriceList)
    .map(
      ({ fieldType, code, name, updatedDropdownOptions }) =>
        ({
          keyType: 'USERFIELD' as const,
          stringKey: null,
          codeKey: `${code}`,
          name: name ?? '',
          fieldType: fieldType ?? null,
          options: (updatedDropdownOptions ?? []).map(({ value, code }) => ({
            value: value ?? null,
            code: String(code),
          })),
        }) as CriteriaFieldDefinition,
    )
    .concat({
      keyType: 'ACCOUNTFIELD' as const,
      stringKey: 'accountType',
      codeKey: null,
      name: 'Account Type',
      fieldType: 'DROPDOWN',
      options: accountTypesResult.data.map(
        ({ accountTypeCode, description }) => ({
          value: description,
          code: accountTypeCode,
        }),
      ),
      selected: [],
    })
    .concat({
      keyType: 'ACCOUNTFIELD' as const,
      stringKey: 'branch',
      codeKey: null,
      name: 'Branch',
      fieldType: 'DROPDOWN',
      options: branchesResult.data.map(({ code, branchName }) => ({
        value: branchName,
        code: code,
      })),
      selected: [],
    })
    .concat({
      keyType: 'ACCOUNTFIELD' as const,
      stringKey: 'costCenter',
      codeKey: null,
      name: 'Cost Center',
      fieldType: 'DROPDOWN',
      options: Array.from(
        new Set(accountsResult.data.map(({ costCenter }) => costCenter)),
      ).map((costCenter) => ({
        value: costCenter,
        code: costCenter,
      })),
      selected: [],
    })
    .concat({
      keyType: 'ACCOUNTFIELD' as const,
      stringKey: 'accountRiskLevel',
      codeKey: null,
      name: 'Account Risk Level',
      fieldType: 'BOOLEAN',
    })

  // Each Criterion is composed of multiple fields, each corresponding to a
  // particular UserField or Account demographic field.
  const criterionFields = field.state.value
    // We will produce a list of these fields by first selecting only the criteria
    // for our specific criteriaCode.
    ?.filter((criterion) => criterion.criteriaCode === criteriaCode)

    // Then we generate a list of unique fields -- these are identified by a
    // combination of the keyType (UserField or Account field), codeKey (identifying
    // a particular UserField), and stringKey (identifying a particular Account field).
    //
    // Note that only one of codeKey or stringKey will be defined.
    ?.map(
      ({ keyType, codeKey, stringKey }) =>
        [keyType, codeKey, stringKey] as const,
    )
    ?.filter(isUnique(isArrayEqual()))

    // Now we need to look up into our list of field definitions to find some
    // additional info about each field -- its name, type (e.g. freeform text,
    // dropdown, boolean), and, in the case of dropdowns, its list of options.
    ?.map(([keyType, codeKey, stringKey]) => {
      const fieldDefinition = criteriaFieldDefinitions?.find((f) => {
        return (
          f.keyType === keyType &&
          f.codeKey === codeKey &&
          f.stringKey === stringKey
        )
      })
      console.log(criteriaFieldDefinitions, keyType, codeKey, stringKey)
      if (!fieldDefinition) {
        throw new Error(
          `No fieldType found for criterion ${keyType}-${codeKey}-${stringKey}`,
        )
      }
      return {
        keyType,
        codeKey,
        stringKey,
        name: fieldDefinition.name,
        fieldType: fieldDefinition.fieldType,
        options: fieldDefinition.options,
      }
    })

  /**
   * Remove all criteria matching this component's criteriaCode.
   */
  const deleteCriterion = () => {
    const newValue = field.state.value.filter(
      (c) => c.criteriaCode !== criteriaCode,
    )

    newValue.forEach((criterion, index) => {
      criterion.code = `${index}`
    })

    field.setValue(newValue)
    criteriaCode && onDelete?.(criteriaCode)
  }

  return (
    <div className='my-4 rounded-lg border p-4'>
      <Disclosure defaultOpen={!viewOnly}>
        {({ open }) => (
          <div>
            <div className='mb-6 mt-2 flex'>
              <DisclosureButton className='group mr-4' disabled={viewOnly}>
                <ChevronRightIcon className='size-5 group-data-[open]:rotate-90' />
              </DisclosureButton>
              <h3 className='flex-1'>{name}</h3>
              {!viewOnly && (
                <button
                  className='flex items-center text-sm font-bold text-app-color-button-primary-bg'
                  onClick={deleteCriterion}
                >
                  <TrashIcon className='mr-2 size-5' />
                  Delete
                </button>
              )}
            </div>
            <div>
              {criterionFields.map((props, i) => (
                <Fragment
                  key={`${props.keyType}-${props.codeKey}-${props.stringKey}`}
                >
                  <CriterionField
                    field={field}
                    criteriaCode={criteriaCode}
                    readonly={!open || viewOnly}
                    viewOnly={viewOnly}
                    {...props}
                  />
                  <If true={open || i < criterionFields.length - 1}>
                    <p className='mb-2 mt-3 text-sm font-bold'>AND</p>
                  </If>
                </Fragment>
              ))}
              <If true={open}>
                <Select
                  name='addCriterionField'
                  label='Select demographic or user field'
                  value={null as CriteriaFieldDefinition | null}
                  required={field.state.value.length == 0}
                  onChange={(value) => {
                    if (value === null) return
                    const { keyType, codeKey, stringKey } = value
                    field.pushValue({
                      code: `${field.state.value.length}`,
                      demographicCriteriaId: {
                        priceListCode,
                        criteriaCode,
                        keyType,
                        codeKey,
                        stringKey,
                        valueCode: '0',
                      },
                      effectiveDate,
                      priceListCode,
                      criteriaCode,
                      keyType,
                      codeKey,
                      stringKey,
                      valueCode: '0',
                      booleanValue: null,
                      codeValue: null,
                      stringValue: null,
                    })
                  }}
                >
                  {criteriaFieldDefinitions
                    // Remove fields that have already been added to this criterion.
                    .filter((definition) => {
                      return (
                        field.state.value.find((field) => {
                          return (
                            criteriaCode === field.criteriaCode &&
                            definition.keyType === field.keyType &&
                            definition.codeKey === field.codeKey &&
                            definition.stringKey === field.stringKey
                          )
                        }) === undefined
                      )
                    })
                    .map((fieldDefinition) => (
                      <SelectOption
                        key={`${fieldDefinition.keyType}-${fieldDefinition.name}`}
                        value={fieldDefinition}
                      >
                        <p>{fieldDefinition.name}</p>
                        <p className='text-sm text-app-color-secondary'>
                          {fieldDefinition.keyType === 'ACCOUNTFIELD' ?
                            'Demographic info'
                          : 'User field'}
                        </p>
                      </SelectOption>
                    ))}
                </Select>
              </If>
            </div>
          </div>
        )}
      </Disclosure>
    </div>
  )
}
