'use client'

import {
  InfoSection,
  InfoSectionDescription,
  InfoSectionTitle,
} from '@/components/InfoSection'
import { useAddPriceListContext } from '../context'
import { useQuery } from '@tanstack/react-query'
import { query } from '../../[effectiveDate]/queries'
import { useRoute } from '../../routing'
import { data } from '@/lib/unions/Union'
import { components } from '@/api/schema'
import { Typeahead, TypeaheadOption } from '@/components/Input/Typeahead'
import { isDefined } from '@/lib/guards/isDefined'
import {
  Modal,
  ModalCancelButton,
  ModalConfirmButton,
  ModalFooter,
  ModalTitle,
  ModalWindow,
  useModal,
} from '@/components/Modal'
import { useEffect, useMemo, useState } from 'react'
import { useForm, useStore } from '@tanstack/react-form'
import { fromEntries } from '@/lib/functional/fromEntries'
import { ServicePrice as ServicePriceComponent } from '@/components/ServicePrice/ServicePrice'
import { ExpandedTable } from '@/components/Table/ExpandedTable'
import {
  ChevronRightIcon,
  PencilIcon,
  TrashIcon,
} from '@heroicons/react/24/outline'
import { clsx } from 'clsx'
import {
  formToApiSchemas,
  Service,
  ServiceForm,
  ServicePriceForm,
} from '@/api/formToApiSchema'
import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { typeSafeParse } from '@/lib/validation/typeSafeParse'
import {
  dispositionLabel,
  priceTypeLabel,
  serviceTypeLabel,
} from '@/strings/enums'
import { formatCurrency } from '@/strings/currency'
import { z } from 'zod'
import { includes } from '@/lib/functional/includes'

const fromToApiServicePriceExt = formToApiSchemas.servicePrice.extend({
  pricingTiers: z.array(formToApiSchemas.servicePrice),
})

const apiToFormServicePriceExt = apiToFormSchemas.servicePrice.extend({
  pricingTiers: z.array(apiToFormSchemas.servicePrice),
})

// type FormToApiServicePriceFormExt = z.input<typeof fromToApiServicePriceExt>
export type ApiToFromServicePriceFormExt = z.output<
  typeof apiToFormServicePriceExt
>

type ServiceTableCategoryRow = {
  type: 'category'
  name: string
  code: string
  children: ServiceTableRow[]
}

type ServiceTableServiceRow = {
  type: 'service'
  name: string
  code: string
  serviceType: ServiceForm['serviceType']
  priceType: ServicePriceForm['priceType']
  priceValue: string
  disposition: ServicePriceForm['disposition']
}

type ServiceTableRow = ServiceTableCategoryRow | ServiceTableServiceRow

export default function AddPriceListStep3Page() {
  // SAFETY: we can assert this is always defined, because the parent layout
  // provides a defined initial state to AddPriceListContextProvider.
  const form = useAddPriceListContext()!
  const [effectiveDate, priceListCode, servicePrices] = useStore(
    form.store,
    (state) => [
      state.values.demographicPriceList.effectiveDate!,
      state.values.demographicPriceList.code,
      state.values.servicePricing ?? [],
    ],
  )
  const pricedServices = new Set(servicePrices.map((s) => s.serviceCode))
  const [activeService, setActiveService] = useState<Service | null>(null)
  const [isEdit, setIsEdit] = useState(true)

  const { data: defaultServicePrices } = useQuery({
    ...query('/getServicePricesAsOf', {
      asOfDate: effectiveDate,
      serviceCodes: activeService?.code ? [activeService.code] : [],
    }),
    enabled: activeService?.code !== undefined,
  })

  useEffect(() => {
    if (defaultServicePrices) {
      const tiers = defaultServicePrices
        .map((p) => apiToFormSchemas.servicePrice.parse(p))
        .filter((price) =>
          includes(price.priceType, ['PARTITIONED_TIER', 'THRESHOLD_TIER']),
        )
        .sort((a, b) => a.tierNumber - b.tierNumber)

      form.setFieldValue(
        'pricingTiers',
        isEdit ?
          tiers.filter((t) => t.pricingHierarchyEntryCode === priceListCode)
        : tiers.filter((t) => t.pricingHierarchyEntryType === 'STANDARD'),
      )
    }
  }, [defaultServicePrices, form, isEdit, priceListCode])

  const activeServiceDefaultPrice: ApiToFromServicePriceFormExt | undefined =
    useMemo(() => {
      if (!defaultServicePrices) return
      const defaultServicePrice = defaultServicePrices[0]
      const parsed = typeSafeParse(
        // apiToFormSchemas.servicePrice,
        apiToFormServicePriceExt,
        {
          ...defaultServicePrice,
          pricingTiers: defaultServicePrices.filter((price) =>
            includes(price.priceType, ['PARTITIONED_TIER', 'THRESHOLD_TIER']),
          ),
        },
      )

      return parsed.success ? parsed.data : undefined
    }, [defaultServicePrices])

  const servicePriceForm = useForm<ApiToFromServicePriceFormExt>({
    onSubmit: ({ value }) => {
      const index = servicePrices.findIndex(
        ({ serviceCode }) => serviceCode === value.serviceCode,
      )

      const servicePrice: ServicePriceForm = {
        ...value,
        effectiveDate,
        pricingHierarchyEntryType: 'PRICE_LIST',
        pricingHierarchyEntryCode: priceListCode,
        pricingTires: servicePrices.filter((price) =>
          includes(price.priceType, ['PARTITIONED_TIER', 'THRESHOLD_TIER']),
        ),
      } as any

      if (index >= 0)
        form.replaceFieldValue('servicePricing', index, servicePrice)
      else form.pushFieldValue('servicePricing', servicePrice)
    },
  })

  useEffect(() => {
    if (isEdit || !activeServiceDefaultPrice) return

    servicePriceForm.reset(activeServiceDefaultPrice)
  }, [activeServiceDefaultPrice, isEdit, servicePriceForm])

  const modalState = useModal()

  const route = data(useRoute()!)
  const {
    data: catalog,
    error,
    status,
  } = useQuery(
    query('/getServiceCatalogMapping', {
      effectiveDate: effectiveDate,
    }),
  )

  if (status === 'error') {
    return (
      <div className='flex h-full w-full items-center justify-center text-app-color-fg-error-primary'>
        {error.message}
      </div>
    )
  }
  if (status === 'pending') {
    return (
      <div className='flex h-full w-full items-center justify-center text-2xl text-app-color-secondary'>
        Loading...
      </div>
    )
  }

  const allServices = fromEntries(
    catalog.serviceToCategoryMappings
      ?.map(({ child }) => child)
      .filter(isDefined)
      .map((service) => [service.code!, service] as const) ?? [],
  )
  const allCategories = fromEntries(
    catalog.serviceToCategoryMappings
      ?.map(({ parent, child }) =>
        parent && child ? { parent, child } : undefined,
      )
      .filter(isDefined)
      .map(({ parent, child }) => [child.code!, parent]) ?? [],
  )

  const serviceTableRows = constructTableRows(catalog, servicePrices)

  return (
    <>
      <InfoSection>
        <InfoSectionTitle>Services and pricing</InfoSectionTitle>
        <InfoSectionDescription>
          Select services to include in this price list and configure their
          pricing.
        </InfoSectionDescription>
        <Typeahead<Service, false>
          className='max-w-[30rem]'
          placeholder='Add services by searching for their name or code.'
          filter={(query) => {
            const caseInsensitiveQuery = query.toLowerCase()
            const results = Object.values(allServices).filter(
              ({ code, description }) => {
                // Filter out services for which we've already added pricing.
                return (
                  code &&
                  !pricedServices.has(code) &&
                  (code?.toLowerCase().includes(caseInsensitiveQuery) ||
                    description?.toLowerCase().includes(caseInsensitiveQuery))
                )
              },
            )
            return results ?? []
          }}
          onChange={(service) => {
            setIsEdit(false)
            setActiveService(service)
            if (service === null) modalState.close()
            else modalState.show()
          }}
          immediate
        >
          {(results) =>
            results.map((service) => (
              <TypeaheadOption key={service.code} value={service}>
                <div className='flex'>
                  <span className='flex-1 truncate'>{service.description}</span>
                  <span className='ml-2 truncate text-app-color-secondary'>
                    {service.code}
                  </span>
                </div>
              </TypeaheadOption>
            ))
          }
        </Typeahead>
        <ExpandedTable
          data={serviceTableRows}
          columns={[
            {
              header: 'Name',
              accessorKey: 'name',
              meta: { className: 'flex-[4_1_0]' },
              cell: ({ row, getValue }) => {
                const props: Record<string, string> = {}
                if (row.getIsExpanded()) props['data-open'] = ''
                return (
                  <div
                    className='flex items-center'
                    style={{
                      marginLeft: `${row.depth * 2}rem`,
                    }}
                  >
                    {row.getCanExpand() && (
                      <button
                        className='group mr-3'
                        onClick={row.getToggleExpandedHandler()}
                        {...props}
                      >
                        <ChevronRightIcon className='size-5 group-data-[open]:rotate-90' />
                      </button>
                    )}
                    <span
                      className={clsx(
                        row.original.type === 'category' && 'font-bold',
                      )}
                    >
                      {getValue() as string}
                    </span>
                  </div>
                )
              },
            },
            {
              header: 'Code',
              accessorKey: 'code',
              meta: { className: 'flex-1' },
            },
            {
              header: 'Service type',
              accessorKey: 'serviceType',
              meta: { className: 'flex-1' },
              cell: ({ row, getValue }) => {
                if (row.original.type === 'category') return ''
                const serviceType = getValue() as ServiceForm['serviceType']
                return serviceTypeLabel(serviceType)
              },
            },
            {
              header: 'Price type',
              accessorKey: 'priceType',
              meta: { className: 'flex-1' },
              cell: ({ row, getValue }) => {
                if (row.original.type === 'category') return ''
                const priceType = getValue() as ServicePriceForm['priceType']
                return priceTypeLabel(priceType)
              },
            },
            {
              header: 'Price',
              accessorKey: 'priceValue',
              meta: { className: 'flex-1' },
              cell: ({ row, getValue }) => {
                if (row.original.type === 'category') return ''
                return formatCurrency(getValue() as string, '$')
              },
            },
            {
              header: 'Disposition',
              accessorKey: 'disposition',
              meta: { className: 'flex-1' },
              cell: ({ row, getValue }) => {
                if (row.original.type === 'category') return ''
                const disposition =
                  getValue() as ServicePriceForm['disposition']
                return dispositionLabel(disposition)
              },
            },
            {
              // The width is calculated to match the width of the two icons (and
              // for the header we also add the width of the scroll bar), so this
              // column always has the same width.
              header: () => <div className='w-[4.1875rem]'></div>,
              id: 'actions',
              meta: { className: 'grow-0' },
              cell: ({ row }) => {
                const price = servicePrices.find(
                  ({ serviceCode }) => serviceCode === row.original.code,
                ) as ApiToFromServicePriceFormExt

                if (!price) return <div className='w-[3.25rem]'></div>

                price.pricingTiers =
                  activeServiceDefaultPrice?.pricingTiers ?? []

                return (
                  <div className='flex items-center gap-3'>
                    <button
                      onClick={() => {
                        console.debug('resetting:', price)
                        isEdit && setIsEdit(true)
                        setActiveService(allServices[price.serviceCode!])
                        servicePriceForm.reset(
                          price as ApiToFromServicePriceFormExt,
                        )
                        modalState.show()
                      }}
                    >
                      <PencilIcon className='size-5' />
                    </button>
                    <button
                      onClick={() => {
                        form.removeFieldValue(
                          'servicePricing',
                          servicePrices.indexOf(price),
                        )
                      }}
                    >
                      <TrashIcon className='size-5' />
                    </button>
                  </div>
                )
              },
            },
          ]}
          getSubRows={(row) => (row.type === 'category' ? row.children : [])}
          initialExpandedState={true}
        />
      </InfoSection>
      <Modal modalState={modalState}>
        <ModalWindow className='flex h-full w-full flex-col rounded-none bg-zinc-100 px-40'>
          <ModalTitle>Add pricing changes</ModalTitle>
          <InfoSection className='my-4'>
            <InfoSectionTitle>Service information</InfoSectionTitle>
            <div className='flex'>
              <div className='flex flex-1 flex-col'>
                <p className='text-sm font-medium text-app-color-secondary'>
                  Service category:
                </p>
                <p className='font-medium'>
                  {activeService?.code ?
                    allCategories[activeService.code]?.name
                  : '-'}
                </p>
              </div>
              <div className='flex flex-1 flex-col'>
                <p className='text-sm font-medium text-app-color-secondary'>
                  Service name:
                </p>
                <p className='font-medium'>{activeService?.description}</p>
              </div>
              <div className='flex flex-1 flex-col'>
                <p className='text-sm font-medium text-app-color-secondary'>
                  Service code:
                </p>
                <p className='font-medium'>{activeService?.code}</p>
              </div>
              <div className='flex flex-1 flex-col'>
                <p className='text-sm font-medium text-app-color-secondary'>
                  Service type:
                </p>
                <p className='font-medium'>
                  {activeService?.serviceType ?
                    serviceTypeLabel(activeService.serviceType)
                  : '-'}
                </p>
              </div>
            </div>
          </InfoSection>
          <InfoSection className='flex-1 overflow-hidden'>
            <InfoSectionTitle>Pricing changes</InfoSectionTitle>
            <InfoSectionDescription>
              Select pricing attributes to apply to this price list and
              configure their values.
            </InfoSectionDescription>
            {activeServiceDefaultPrice && (
              <ServicePriceComponent
                isEdit={isEdit}
                className='min-h-0'
                form={servicePriceForm}
                serviceType={activeService?.serviceType!}
                compareTo={activeServiceDefaultPrice}
                compareToService={activeService}
              />
            )}
          </InfoSection>
          <ModalFooter className='justify-between'>
            <ModalCancelButton className='min-w-40 flex-initial'>
              Cancel
            </ModalCancelButton>
            <ModalConfirmButton
              className='btn-primary min-w-40 flex-initial'
              onClick={() => {
                modalState.close()
                if (!activeService) return
                servicePriceForm.handleSubmit()
              }}
            >
              Save
            </ModalConfirmButton>
          </ModalFooter>
        </ModalWindow>
      </Modal>
    </>
  )
}

function constructTableRows(
  catalog: components['schemas']['HydratedServiceCatalogMapping'],
  prices: ServicePriceForm[],
): ServiceTableRow[] {
  const rows: Record<string, ServiceTableRow> = {
    root: { type: 'category', name: 'root', code: 'root', children: [] },
  }
  const root = rows.root as ServiceTableCategoryRow

  const services = fromEntries(
    catalog.serviceToCategoryMappings
      ?.map(({ parent, child }) =>
        parent && child ? { parent, child } : undefined,
      )
      .filter(isDefined)
      .map(
        ({ parent, child: service }) =>
          [service.code!, { parent, service }] as const,
      ) ?? [],
  )

  const categories = fromEntries(
    catalog.categoryToCategoryMappings
      ?.map(({ parent, child }) => (child ? { parent, child } : undefined))
      .filter(isDefined)
      .map(
        ({ parent, child: category }) =>
          [category.code!, { parent, category }] as const,
      ) ?? [],
  )

  for (const price of prices) {
    if (!price.serviceCode) continue
    const { parent, service } = services[price.serviceCode]
    let current: ServiceTableRow = {
      type: 'service',
      name: service.description!,
      code: service.code!,
      serviceType: service.serviceType!,
      priceType: price.priceType!,
      priceValue: price.priceValue!,
      disposition: price.disposition!,
    }
    let ancestor: typeof parent | undefined = parent
    while (ancestor?.code) {
      const row = rows[ancestor.code]
      if (!row) {
        rows[ancestor.code] = {
          type: 'category' as const,
          name: ancestor.name!,
          code: ancestor.code!,
          children: [current],
        }
      } else if (row.type === 'category') {
        if (current.type === 'category') row.children.push(current)
        else {
          const index = row.children.findIndex(
            ({ type }) => type === 'category',
          )
          if (index < 0) row.children.push(current)
          else row.children.splice(index, 0, current)
        }
      }
      current = rows[ancestor.code]
      ancestor = categories[ancestor.code].parent
    }
    if (!root.children.includes(current)) root.children.push(current)
  }

  return root.children.sort((a, b) => {
    if (a.type === 'service' && b.type === 'category') return 1
    if (a.type === b.type) return 0
    return -1
  })
}
