import { Catalog } from '@/app/[bankId]/services/_hooks/useServiceCatalog'
import {
  PricingCatalogCategory,
  RowType,
  ServiceAndPrice,
  ServiceAndPriceObj,
  ServicePrice,
  SubRows,
} from './types'

//build the row structure for rendering on the table
const formatRow = (service: ServiceAndPriceObj): RowType => {
  return {
    name: service?.current?.description ?? '',
    serviceCode: service?.serviceCode ?? '',
    serviceType: service?.current?.serviceType ?? '',
    priceType: service?.priceType ?? '',
    price: service?.priceValue ?? 0,
    disposition: service?.disposition ?? '',
    subRows: [],
    serviceByCategoryCodes: [service?.current?.description ?? ''],
    parentCatalog: [],
    childrenCategories: [],
    priceTypes: [service?.priceType ?? ''],
    serviceTypes: [service?.current?.serviceType ?? ''],
    isService: true,
    baseFee: service?.baseFee ?? 0,
    maximumFee: service?.maximumFee,
    minimumFee: service?.minimumFee,
    costType: service?.costType,
    cost: service?.costValue,
    parent: service?.parent,
  }
}

//construct the row hierarchy
const buildRows = (
  categoryByCode: PricingCatalogCategory[],
  servicesCatalog: Catalog['serviceByCode'],
  serviceByCode: ServiceAndPrice,
  priceData: ServicePrice[],
) => {
  //build subRows for each row
  const buildHierarchy = (
    row: PricingCatalogCategory,
    childCategories: string[],
  ): SubRows => {
    if (!row) return null

    //construct each service leaf node
    const services = (row.serviceCodes ?? [])
      .map((serviceCode) => {
        const service = serviceByCode[serviceCode]
        //add each service leaf node
        return service ?
            {
              ...formatRow(service),
              parentCatalog: [...childCategories],
              priceTypes: [service.priceType || ''],
            }
          : null
      })
      .filter(Boolean) //remove null

    // get all services from categories recursively
    const children = row.categoryCodes
      ?.map((childCode: string) =>
        buildHierarchy(
          categoryByCode.find(
            (cat: PricingCatalogCategory) => cat.current.code === childCode,
          )!,
          [...childCategories, childCode],
        ),
      )
      .filter(Boolean)

    // filter out null services and categories
    if (!services.length && !children.length) {
      return null
    }
    // return all the sub row
    return {
      ...row.current,
      serviceByCategoryCodes: row.serviceByCategoryCodes,
      parentCatalog: [...row.categoryCodes, ...childCategories],
      priceTypes: [
        ...(row.priceTypes ?? []),
        ...(row.serviceByCategoryCodes
          ?.map(
            (item) =>
              priceData.find((data) => data.serviceCode === item)
                ?.priceType as string,
          )
          .filter(Boolean) ?? []),
      ],
      serviceTypes: [
        ...(row.serviceTypes ?? []),
        ...(row.serviceByCategoryCodes
          ?.map((item) => servicesCatalog[item]?.current?.serviceType as string)
          .filter(Boolean) ?? []),
      ],
      subRows: [...services, ...children],
    }
  }

  // start from the top category and construct the tree
  return categoryByCode
    .filter((node: PricingCatalogCategory) => !node.parent)
    .map((node) => buildHierarchy(node, [node.current.code!]))
    .filter(Boolean)
}

export const transformData = (
  catalog: Catalog,
  priceData: ServicePrice[],
): SubRows[] => {
  // get the List of all the categories from the catalog
  const catalogCategories = new Map(Object.entries(catalog?.categoryByCode))

  // list of all service names
  const getServiceNames = (codes: string[]): string[] => {
    return codes.map(
      (code) => catalog?.serviceByCode[code]?.current.description!,
    )
  }

  //get all services under each category
  const groupServices = (
    category: PricingCatalogCategory,
    pricingCatalogCategoryByCode = catalog?.categoryByCode,
  ) => {
    const allServices = new Set(category.serviceCodes)

    category?.categoryCodes.forEach((cat: string) => {
      if (pricingCatalogCategoryByCode[cat]) {
        const subCategory = pricingCatalogCategoryByCode[cat]
        const childServiceCodes = groupServices(
          subCategory,
          pricingCatalogCategoryByCode,
        )
        childServiceCodes.forEach((code) => allServices.add(code))
      }
    })
    category.serviceByCategoryCodes =
      getServiceNames(Array.from(allServices)) ?? []
    category.parentCatalog = category.categoryCodes
    category.priceTypes = [
      ...(Array.from(allServices!)
        ?.map(
          (item) =>
            priceData.find((data) => data.serviceCode === item)
              ?.priceType as string,
        )
        .filter(Boolean) ?? []),
    ]
    category.serviceTypes = [
      ...(Array.from(allServices!)
        ?.map(
          (item) => catalog?.serviceByCode[item].current.serviceType as string,
        )
        .filter(Boolean) ?? []),
    ]
    return allServices
  }

  const categoryByCode: PricingCatalogCategory[] =
    Array.from(catalogCategories.values()) ?? {}

  const serviceByCode: ServiceAndPrice = {}

  priceData?.forEach((item: ServicePrice) => {
    if (item.serviceCode && catalog?.serviceByCode[item.serviceCode]) {
      let tableRow = { ...item, ...catalog?.serviceByCode[item.serviceCode] }
      serviceByCode[item.serviceCode] = { ...tableRow }
    }
  })
  catalogCategories.forEach((cat) => {
    //adds all children services to the category
    groupServices(cat)
  })

  const catalogRows: SubRows[] = buildRows(
    categoryByCode,
    catalog.serviceByCode,
    serviceByCode,
    priceData,
  )
  //return all the subrows of root "parent"
  return catalogRows?.[0]?.subRows ?? []
}

//returns all catalogs for filtering
export const getCatalogFilterOptions = (
  catalogData: Catalog,
  formattedTableData: SubRows[],
  optionsMap = new Map(),
) => {
  if (!optionsMap.size) {
    optionsMap.set('', { code: '', label: 'All Categories' })
  }
  const categories = catalogData?.categoryByCode
  const filterMetaArray = formattedTableData.map((item) => {
    return item?.name
  })
  formattedTableData.forEach((row) => {
    if (row && row.parentCatalog) {
      row.parentCatalog.forEach((catalog: string) => {
        if (
          filterMetaArray.includes(categories[catalog]?.current?.name) &&
          catalog !== 'root' &&
          !optionsMap.has(catalog)
        ) {
          optionsMap.set(catalog, {
            code: catalog,
            label: categories[catalog]?.current?.name,
          })
        }
      })
    }
    if (row?.subRows) {
      getCatalogFilterOptions(catalogData, row.subRows, optionsMap)
    }
  })
  return optionsMap
}

//returns all price types, service types for filtering
export const getFilterOptions = (
  key: 'priceTypes' | 'serviceTypes',
  tableData: SubRows[],
  filter: Set<string>,
): Set<string> => {
  const filterMetaArray = tableData.map((item) => {
    return item?.[key === 'serviceTypes' ? 'serviceType' : 'priceType']
  })
  tableData.forEach((row) => {
    if (row && row[key]) {
      row[key]?.forEach((item) => {
        if (filterMetaArray.includes(item)) {
          filter.add(item)
        }
      })
    }
    if (row && row.subRows) {
      getFilterOptions(key, row.subRows, filter)
    }
    return
  })

  return filter
}

export const convertToSentenceCase = (s: string | undefined) =>
  s?.replace(/^_*(.)|_+(.)/g, (s, c, d) =>
    c ? c.toUpperCase() : ' ' + d.toUpperCase(),
  )
