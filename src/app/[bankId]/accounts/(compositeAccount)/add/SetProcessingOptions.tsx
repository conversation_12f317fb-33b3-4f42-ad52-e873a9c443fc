'use client'

import {
  InfoSectionDescription,
  InfoSubSectionTitle,
} from '@/components/InfoSection'
import { CreateCompositeAccountFormSchemaType } from '../../types'
import { SortedTable } from '@/components/Table/SortedTable'
import { ColumnDef } from '@tanstack/react-table'
import { useMemo } from 'react'
import { useStore } from '@tanstack/react-form'
import { Select } from '@/components/Input/Select'
import { Checkbox } from '@/components/Checkbox'
import { MonthPicker } from '@/components/Filter/MonthPicker'
import { formatToServerString, toUTCDateString } from '@/lib/date'
import { useConfigurations } from '@/app/[bankId]/configuration/_hooks/useConfigurations'

type ProcessingOptionItem = {
  field:
    | 'earningsCreditDefinitionCode'
    | 'investableBalanceDefinitionCode'
    | 'balanceRequirementDefinitionCode'
    | 'statementCyclePlanCode'
    | 'statementFormatPlanCode'
  name: string
}

const processingOptions: ProcessingOptionItem[] = [
  {
    field: 'earningsCreditDefinitionCode',
    name: 'Earnings credit',
  },
  {
    field: 'investableBalanceDefinitionCode',
    name: 'Investable balance',
  },
  {
    field: 'balanceRequirementDefinitionCode',
    name: 'Required balance',
  },
  {
    field: 'statementCyclePlanCode',
    name: 'Statement cycle',
  },
  {
    field: 'statementFormatPlanCode',
    name: 'Statement format',
  },
]

type processingOptionsColumnDef = ColumnDef<ProcessingOptionItem>

interface SetProcessingOptionsProps {
  form: CreateCompositeAccountFormSchemaType
}

export function SetProcessingOptions({ form }: SetProcessingOptionsProps) {
  const [accountTypeOverride, effectiveDate] = useStore(form.store, (state) => [
    state.values.accountTypeOverride,
    state.values.accountInfo.effectiveDate,
  ])

  const {
    earningsCreditDefinitions: { data: earningsCreditDefinitions },
    investableBalanceDefinitions: { data: investableBalanceDefinitions },
    requiredBalanceDefinitions: { data: requiredBalanceDefinitions },
    cycleDefinitions: { data: cycleDefinitions },
    statementFormatPlans: { data: statementFormatPlans },
  } = useConfigurations({ effectiveDate })

  const earningsCreditOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    earningsCreditDefinitions!.forEach((option) => {
      const code = `${option.code}`
      const name = `${option.description}`
      optionsMap.set(code, name)
    })
    return optionsMap
  }, [earningsCreditDefinitions])

  const investableBalanceOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    investableBalanceDefinitions!.forEach((option) => {
      const code = `${option.code}`
      const name = `${option.name}`
      optionsMap.set(code, name)
    })
    return optionsMap
  }, [investableBalanceDefinitions])

  const requiredBalanceOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    requiredBalanceDefinitions!.forEach((option) => {
      const code = `${option.code}`
      const name = `${option.name}`
      optionsMap.set(code, name)
    })
    return optionsMap
  }, [requiredBalanceDefinitions])

  const statementCycleOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    cycleDefinitions!.forEach((option) => {
      const code = `${option.code}`
      const name = `${option.description}`
      if (option.cycleType === 'STATEMENT') {
        optionsMap.set(code, name)
      }
    })
    return optionsMap
  }, [cycleDefinitions])

  const statementFormatOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    statementFormatPlans!.forEach((option) => {
      const code = `${option.code}`
      const name = `${option.description}`
      optionsMap.set(code, name)
    })
    return optionsMap
  }, [statementFormatPlans])

  const allOptionsMaps = useMemo<
    Map<ProcessingOptionItem['field'], Map<string, string>>
  >(() => {
    const optionsMap: Map<
      ProcessingOptionItem['field'],
      Map<string, string>
    > = new Map()
    optionsMap.set('earningsCreditDefinitionCode', earningsCreditOptionsMap)
    optionsMap.set(
      'investableBalanceDefinitionCode',
      investableBalanceOptionsMap,
    )
    optionsMap.set(
      'balanceRequirementDefinitionCode',
      requiredBalanceOptionsMap,
    )
    optionsMap.set('statementCyclePlanCode', statementCycleOptionsMap)
    optionsMap.set('statementFormatPlanCode', statementFormatOptionsMap)
    return optionsMap
  }, [
    earningsCreditOptionsMap,
    investableBalanceOptionsMap,
    requiredBalanceOptionsMap,
    statementCycleOptionsMap,
    statementFormatOptionsMap,
  ])

  const columns = useMemo<processingOptionsColumnDef[]>(() => {
    return [
      {
        header: 'Settlement setting',
        accessorKey: 'name',
        meta: {
          className: 'ml-4 basis-full',
        },
      },
      {
        header: 'Plan',
        meta: {
          className: 'basis-full',
        },
        cell: ({ row }) => {
          const field = row.original.field as ProcessingOptionItem['field']
          const fieldValue = accountTypeOverride[field]
          return (
            <div className='min-w-80'>
              <SelectProcessingOption
                field={field}
                value={fieldValue ? fieldValue : ''}
                optionsMap={allOptionsMaps.get(field)!}
                onChange={(value) => {
                  form.setFieldValue(`accountTypeOverride.${field}`, value)
                }}
              />
            </div>
          )
        },
      },
      {
        header: 'Expiration date',
        meta: {
          className: 'ml-4 basis-full',
        },
        cell: ({ row }) => {
          const field = row.original.field
          const fieldValue = accountTypeOverride[field]
          const expiryValue = accountTypeOverride[`${field}Expiry`]
          return (
            <SetProcessingOptionExpirationDate
              isVisible={!!fieldValue}
              expiry={expiryValue}
              onCheckboxChange={(checked: boolean) => {
                form.setFieldValue(
                  `accountTypeOverride.${field}Expiry`,
                  checked ? undefined : toUTCDateString(new Date()),
                )
              }}
              onMonthPickerChange={(date: string) => {
                form.setFieldValue(
                  `accountTypeOverride.${field}Expiry`,
                  formatToServerString(date),
                )
              }}
            />
          )
        },
      },
    ]
  }, [allOptionsMaps, accountTypeOverride, form])

  return (
    <>
      <InfoSubSectionTitle>Processing options</InfoSubSectionTitle>
      <InfoSectionDescription className='-mt-3'>
        These options are inherited from the account type of the key account.
      </InfoSectionDescription>

      <SortedTable
        data={processingOptions}
        columns={columns}
        columnFilters={[]}
      />
    </>
  )
}

interface SelectProcessingOptionProps {
  field: string
  value: string
  optionsMap: Map<string, string>
  onChange: (value: string) => void
}

function SelectProcessingOption({
  field,
  value,
  optionsMap,
  onChange,
}: SelectProcessingOptionProps) {
  return (
    <Select
      className='mt-6 min-w-96'
      name={field}
      value={value}
      options={Array.from(optionsMap.keys())}
      onChange={onChange}
      renderOption={(option) => {
        const optionName = optionsMap.get(option)
        return `${optionName} ${option}`
      }}
      renderSelected={(option) => {
        const optionName = optionsMap.get(option)
        return (
          <div className='flex flex-row gap-2'>
            <div>{optionName}</div>
            <div>{option}</div>
          </div>
        )
      }}
    />
  )
}

interface SetProcessingOptionExpirationDateProps {
  expiry?: string
  isVisible: boolean
  onCheckboxChange: (checked: boolean) => void
  onMonthPickerChange: (date: string) => void
}

function SetProcessingOptionExpirationDate({
  expiry,
  isVisible,
  onCheckboxChange,
  onMonthPickerChange,
}: SetProcessingOptionExpirationDateProps) {
  return (
    isVisible && (
      <div className='flex flex-row gap-3'>
        <Checkbox
          checked={!expiry}
          label='No expiration'
          onChange={onCheckboxChange}
        />
        {expiry && (
          <MonthPicker
            className='my-1 h-9 min-w-32 bg-white ring-1 ring-inset ring-gray-300'
            initialDate={expiry}
            onDateChange={onMonthPickerChange}
          />
        )}
      </div>
    )
  )
}
