'use client'

import {
  InfoSectionDescription,
  InfoSubSectionTitle,
} from '@/components/InfoSection'
import { useFormSelect } from '@/components/Form/useFormSelect'
import { useFormTextInput } from '@/components/Form/useFormTextInput'
import { SelectOption } from '@/components/Input/Select'
import { useStore } from '@tanstack/react-form'
import {
  CompositeAccountFormSchema,
  CreateCompositeAccountFormSchemaType,
  DepositCategoryLabels,
} from '../../types'
import {
  selectOptionMapper,
  useCodesAndRenderOption,
} from '../../_hooks/useCodesAndRenderOption'
import { NameCode } from '../../_components/NameCode'
import { useAccountRelatedEntities } from '../../_hooks/useAccountRelatedEntities'
import { useBankOptions } from '@/app/[bankId]/configuration/bank-options/_hooks/useBankOptions'
import { useRoute } from './routing'
import { data } from '@/lib/unions/Union'
import { DetailsSectionItem } from '@/components/DetailsSection'

interface ConfigureDemographicsProps {
  form: CreateCompositeAccountFormSchemaType
}

export function ConfigureDemographics({ form }: ConfigureDemographicsProps) {
  const route = useRoute()!
  const routeParams = data(route).params
  const bankId = routeParams.bankId

  const [
    effectiveDate,
    keyAccountCode,
    analysisAccountTypeCode,
    costCenter,
    branchCode,
    currencyCode,
    depositAccountTypeCode,
    depositCategory,
    primaryOfficerCode,
    secondaryOfficerCode,
    treasuryOfficerCode,
  ] = useStore(form.store, (state) => [
    state.values.accountInfo.effectiveDate,
    state.values.demographics.keyAccountCode,
    state.values.demographics.analysisAccountTypeCode,
    state.values.demographics.costCenter,
    state.values.demographics.branchCode,
    state.values.demographics.currencyCode,
    state.values.demographics.depositAccountTypeCode,
    state.values.demographics.depositCategory,
    state.values.demographics.primaryOfficerCode,
    state.values.demographics.secondaryOfficerCode,
    state.values.demographics.treasuryOfficerCode,
  ])

  const {
    accountTypes: { data: accountTypes },
    branches: { data: branches },
    officers: { data: officers },
  } = useAccountRelatedEntities({ effectiveDate })

  const FormInput = useFormTextInput<CompositeAccountFormSchema>({
    form,
  })

  const [accountTypeCodes, renderAccountTypeOption] = useCodesAndRenderOption(
    accountTypes ?? [],
    (accountType) => accountType.accountTypeCode,
    (accountType) => (
      <NameCode
        name={accountType.description}
        code={accountType.accountTypeCode}
      />
    ),
  )

  const [branchCodes, renderBranchOption] = useCodesAndRenderOption(
    branches ?? [],
    (branch) => branch.code,
    (branch) => <NameCode name={branch.branchName} code={branch.code} />,
  )

  const [officerCodes, renderOfficerOption] = useCodesAndRenderOption(
    officers ?? [],
    (officer) => officer.code,
    (officer) => <NameCode name={officer.name} code={officer.code} />,
  )

  const { bankOptions } = useBankOptions({ bankId, effectiveDate })
  console.log(bankOptions)
  const Select = useFormSelect<CompositeAccountFormSchema>({ form })

  return (
    <>
      <InfoSubSectionTitle>Demographics</InfoSubSectionTitle>
      <InfoSectionDescription className='-mt-3'>
        These settings are automatically synced with the key account.
      </InfoSectionDescription>

      {bankOptions ?
        <div className='mt-6 flex flex-col gap-6'>
          <div className='flex gap-9'>
            {keyAccountCode && bankOptions.copyAccountTypeOfKeyAccount ?
              <DetailsSectionItem
                className='gap-1'
                label='Account type'
                info={renderAccountTypeOption(analysisAccountTypeCode)}
              />
            : <Select
                name='demographics.analysisAccountTypeCode'
                label='Account type'
                placeholder='Select account type'
                required
                renderSelected={renderAccountTypeOption}
                renderErrors={false}
              >
                {accountTypeCodes.map(
                  selectOptionMapper(renderAccountTypeOption),
                )}
              </Select>
            }

            {keyAccountCode ?
              <DetailsSectionItem
                className='gap-1'
                label='Cost center'
                info={costCenter}
              />
            : <FormInput
                name='demographics.costCenter'
                label='Cost center'
                placeholder='Must contain at most 7 digits'
                required
              />
            }
          </div>

          <div className='flex gap-9'>
            {keyAccountCode ?
              <DetailsSectionItem
                className='gap-1'
                label='Branch'
                info={renderBranchOption(branchCode)}
              />
            : <Select
                name='demographics.branchCode'
                label='Branch'
                placeholder='Select account branch'
                required
                renderSelected={renderBranchOption}
              >
                {branchCodes.map(selectOptionMapper(renderBranchOption))}
              </Select>
            }

            {keyAccountCode ?
              <DetailsSectionItem
                className='gap-1'
                label='Currency'
                info={currencyCode}
              />
            : <FormInput
                name='demographics.currencyCode'
                label='Currency'
                disabled={true}
              />
            }
          </div>

          <div className='flex gap-9'>
            {keyAccountCode ?
              <DetailsSectionItem
                className='gap-1'
                label='Deposit type'
                info={depositAccountTypeCode}
              />
            : <FormInput
                name='demographics.depositAccountTypeCode'
                label='Deposit type'
                disabled={true}
              />
            }

            {keyAccountCode ?
              <DetailsSectionItem
                className='gap-1'
                label='Deposit type'
                info={depositCategory && DepositCategoryLabels[depositCategory]}
              />
            : <FormInput
                // TODO: Turn this into a Select component for depositCategory once we are required to make that field editable
                // name='depositCategory'
                name='demographics.depositCategory'
                label='Deposit category'
                disabled={true}
              />
            }
          </div>

          <div className='flex gap-9'>
            {keyAccountCode ?
              <DetailsSectionItem
                className='gap-1'
                label='Primary officer'
                info={renderOfficerOption(primaryOfficerCode)}
              />
            : <Select
                name='demographics.primaryOfficerCode'
                label='Primary officer'
                placeholder='Select primary officer'
                required
                renderSelected={renderOfficerOption}
              >
                {officerCodes.map(selectOptionMapper(renderOfficerOption))}
              </Select>
            }

            {keyAccountCode ?
              <DetailsSectionItem
                className='gap-1'
                label='Secondary officer'
                info={
                  secondaryOfficerCode &&
                  renderOfficerOption(secondaryOfficerCode)
                }
              />
            : <Select
                name='demographics.secondaryOfficerCode'
                label='Secondary officer'
                placeholder='Select secondary officer'
                renderSelected={renderOfficerOption}
              >
                {secondaryOfficerCode && (
                  <SelectOption key='null' value={null}>
                    None
                  </SelectOption>
                )}
                {officerCodes.map(selectOptionMapper(renderOfficerOption))}
              </Select>
            }
          </div>

          <div className='flex gap-9'>
            {keyAccountCode ?
              <DetailsSectionItem
                className='gap-1'
                label='Treasury officer'
                info={
                  treasuryOfficerCode &&
                  renderOfficerOption(treasuryOfficerCode)
                }
              />
            : <Select
                name='demographics.treasuryOfficerCode'
                label='Treasury officer'
                placeholder='Select treasury officer'
                renderSelected={renderOfficerOption}
              >
                {treasuryOfficerCode && (
                  <SelectOption key='null' value={null}>
                    None
                  </SelectOption>
                )}
                {officerCodes.map(selectOptionMapper(renderOfficerOption))}
              </Select>
            }
            <div className='flex-1'></div>
          </div>
        </div>
      : <>
          No bank options were found for bank {bankId} with the effective date{' '}
          {effectiveDate}
        </>
      }
    </>
  )
}
