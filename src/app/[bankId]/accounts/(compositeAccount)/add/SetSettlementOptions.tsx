'use client'

import {
  InfoSectionDescription,
  InfoSubSectionTitle,
} from '@/components/InfoSection'
import { CreateCompositeAccountFormSchemaType } from '../../types'
import { ColumnDef } from '@tanstack/react-table'
import { useMemo } from 'react'
import { SortedTable } from '@/components/Table/SortedTable'
import { Select } from '@/components/Input/Select'
import { useStore } from '@tanstack/react-form'
import { Checkbox } from '@/components/Checkbox'
import { MonthPicker } from '@/components/Filter/MonthPicker'
import { formatToServerString, toUTCDateString } from '@/lib/date'
import { useConfigurations } from '@/app/[bankId]/configuration/_hooks/useConfigurations'

type SettlementOptionItem = {
  field: 'analysisResultOptionsPlanCode' | 'settlementCyclePlanCode'
  name: string
}

const settlementOptions: SettlementOptionItem[] = [
  {
    field: 'analysisResultOptionsPlanCode',
    name: 'Analysis result options',
  },
  {
    field: 'settlementCyclePlanCode',
    name: 'Settlement cycle',
  },
]

type SettlementOptionsColumnDef = ColumnDef<SettlementOptionItem>

interface SetSettlementOptionsProps {
  form: CreateCompositeAccountFormSchemaType
}

type SettlementOptionItemWithId = {
  id: string
  name: string
  field: string
  override: boolean
  plan: string
  expiry: string | null
}

export function SetSettlementOptions({ form }: SetSettlementOptionsProps) {
  const [accountTypeOverride, effectiveDate] = useStore(form.store, (state) => [
    state.values.accountTypeOverride,
    state.values.accountInfo.effectiveDate,
  ])

  const {
    analysisResultOptions: { data: analysisResultOptions },
    cycleDefinitions: { data: cycleDefinitions },
  } = useConfigurations({ effectiveDate })

  const analysisResultOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    analysisResultOptions!.forEach((option) => {
      const code = `${option.code}`
      const name = `${option.name}`
      optionsMap.set(code, name)
    })
    return optionsMap
  }, [analysisResultOptions])

  const settlementCycleOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    cycleDefinitions!.forEach((option) => {
      const code = `${option.code}`
      const name = `${option.description}`
      if (option.cycleType === 'SETTLEMENT') {
        optionsMap.set(code, name)
      }
    })
    return optionsMap
  }, [cycleDefinitions])

  const allOptionsMaps = useMemo<
    Map<SettlementOptionItem['field'], Map<string, string>>
  >(() => {
    const optionsMap: Map<
      SettlementOptionItem['field'],
      Map<string, string>
    > = new Map()
    optionsMap.set('analysisResultOptionsPlanCode', analysisResultOptionsMap)
    optionsMap.set('settlementCyclePlanCode', settlementCycleOptionsMap)
    return optionsMap
  }, [analysisResultOptionsMap, settlementCycleOptionsMap])

  const columns = useMemo<SettlementOptionsColumnDef[]>(() => {
    return [
      {
        header: 'Settlement setting',
        accessorKey: 'name',
        meta: {
          className: 'ml-4 basis-full',
        },
      },
      {
        header: 'Plan',
        meta: {
          className: 'basis-full',
        },
        cell: ({ row }) => {
          const field = row.original.field as SettlementOptionItem['field']
          const fieldValue = accountTypeOverride[field]
          return (
            <div className='min-w-80'>
              <SelectSettlementOption
                field={field}
                value={fieldValue ? fieldValue : ''}
                optionsMap={allOptionsMaps.get(field)!}
                onChange={(value) => {
                  form.setFieldValue(`accountTypeOverride.${field}`, value)
                }}
              />
            </div>
          )
        },
      },
      {
        header: 'Expiration date',
        meta: {
          className: 'ml-4 basis-full',
        },
        cell: ({ row }) => {
          const field = row.original.field
          const fieldValue = accountTypeOverride[field]
          const expiryValue = accountTypeOverride[`${field}Expiry`]
          return (
            <SetSettlementOptionExpirationDate
              isVisible={!!fieldValue}
              expiry={expiryValue}
              onCheckboxChange={(checked: boolean) => {
                form.setFieldValue(
                  `accountTypeOverride.${field}Expiry`,
                  checked ? undefined : toUTCDateString(new Date()),
                )
              }}
              onMonthPickerChange={(date: string) => {
                form.setFieldValue(
                  `accountTypeOverride.${field}Expiry`,
                  formatToServerString(date),
                )
              }}
            />
          )
        },
      },
    ]
  }, [allOptionsMaps, accountTypeOverride, form])
  return (
    <>
      <InfoSubSectionTitle>Settlement options</InfoSubSectionTitle>
      <InfoSectionDescription className='-mt-3'>
        These options are inherited from the account type of the key account.
      </InfoSectionDescription>

      <SortedTable
        data={settlementOptions}
        columns={columns}
        columnFilters={[]}
      />
    </>
  )
}

interface SelectSettlementOptionProps {
  field: string
  value: string
  optionsMap: Map<string, string>
  onChange: (value: string) => void
}

function SelectSettlementOption({
  field,
  value,
  optionsMap,
  onChange,
}: SelectSettlementOptionProps) {
  return (
    <Select
      className='mt-6 min-w-96'
      name={field}
      value={value}
      options={Array.from(optionsMap.keys())}
      onChange={onChange}
      renderOption={(option) => {
        const optionName = optionsMap.get(option)
        return `${optionName} ${option}`
      }}
      renderSelected={(option) => {
        const optionName = optionsMap.get(option)
        return (
          <div className='flex flex-row gap-2'>
            <div>{optionName}</div>
            <div>{option}</div>
          </div>
        )
      }}
    />
  )
}

interface SetSettlementOptionExpirationDateProps {
  expiry?: string
  isVisible: boolean
  onCheckboxChange: (checked: boolean) => void
  onMonthPickerChange: (date: string) => void
}

function SetSettlementOptionExpirationDate({
  expiry,
  isVisible,
  onCheckboxChange,
  onMonthPickerChange,
}: SetSettlementOptionExpirationDateProps) {
  return (
    isVisible && (
      <div className='flex flex-row gap-3'>
        <Checkbox
          checked={!expiry}
          label='No expiration'
          onChange={onCheckboxChange}
        />
        {expiry && (
          <MonthPicker
            className='my-1 h-9 min-w-32 bg-white ring-1 ring-inset ring-gray-300'
            initialDate={expiry}
            onDateChange={onMonthPickerChange}
          />
        )}
      </div>
    )
  )
}
