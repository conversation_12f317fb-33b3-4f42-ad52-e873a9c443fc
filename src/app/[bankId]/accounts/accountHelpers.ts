import { apiToFormSchemas } from '@/api/apiToFormSchema'
import {
  HydratedAccountWithKeyAccountMapping,
  HydratedAccountWithKeyAccountMappingForm,
  HydratedUserFieldConfigurationForm,
  StatementPackageCreateUpdateRequest,
  UserFieldSelectionForm,
} from '@/api/formToApiSchema'
import { z } from 'zod'
import { AccountType } from '../configuration/types'
import {
  Account,
  AccountCode,
  AccountListItem,
  AccountSearchItem,
  AccountZodSchema,
  Address,
  AddressCode,
  AddressFormSchema,
  BoundedAccountListItem,
  CompositeAccountCreateRequestFormToApiSchema,
  CompositeAccountFormSchema,
  HydratedStatementPackage,
  ProcessingOptionItem,
  SelectAccountFormSchemaType,
  SettlementOptionItem,
  SettlementProcessingOptionItemWithId,
  StateCode,
  StatementPackageFormSchema,
  UserFieldListItem,
} from './types'
import { toUTCDateString } from '@/lib/date'

const { accountWithKey: AccountApiToFormSchema } = apiToFormSchemas

export function getAccountTypeDescription(
  accountTypes: AccountType[] | undefined,
  accountTypeCode: string | undefined,
) {
  if (!accountTypes) {
    return ''
  }
  const matches = accountTypes.filter(
    (accountType) => accountType.accountTypeCode === accountTypeCode,
  )
  return matches.length > 0 ? matches[0].description : ''
}

/**
 * Strips down an Account-like object down to just the fields in AccountCode
 *
 * @param account an Account-like object
 * @returns an AccountCode with no extra fields
 */
export function accountToAccountCode(
  account: Pick<Account, 'applicationId' | 'bankNumber' | 'accountNumber'>,
): AccountCode {
  return {
    applicationId: account.applicationId,
    bankNumber: account.bankNumber,
    accountNumber: account.accountNumber,
  }
}

/**
 * Replicates string representation in backend (not for frontend display)
 *
 * @param accountCode account code
 * @returns backend string representation of account code
 */
export function accountCodeToString(accountCode: AccountCode): string {
  return `${accountCode.bankNumber}${accountCode.applicationId}${accountCode.accountNumber}`
}

export function toAccountCodeString(
  account: Pick<Account, 'applicationId' | 'bankNumber' | 'accountNumber'>,
): string {
  return accountCodeToString(accountToAccountCode(account))
}

/**
 * Replicates string representation in backend (not for frontend display)
 *
 * @param addressCode address code
 * @returns backend string representation of address code
 */
export function addressCodeToString(addressCode: AddressCode): string {
  return `${accountCodeToString(addressCode.accountCode!)}-${addressCode.applicationId}-${addressCode.addressNumber}`
}

/**
 * Format account code for frontend display
 *
 * @param accountCode
 * @returns string representation appropriate for frontend display
 */
export function formatAccountCode(accountCode: AccountCode): string {
  return `${accountCode.applicationId} - ${accountCode.accountNumber}`
}

export function formatAccountCodeForRoute(accountCode: AccountCode): string {
  return `${accountCode.applicationId}-${accountCode.bankNumber}-${accountCode.accountNumber}`
}

export function formatAccountDisplay(
  account: Pick<
    Account,
    'shortName' | 'bankNumber' | 'applicationId' | 'accountNumber'
  >,
): string {
  return `${account.shortName} (${account.bankNumber}) ${account.applicationId} - ${account.accountNumber}`
}

export function accountCodesAreEqual(
  accountCode1: AccountCode,
  accountCode2: AccountCode,
): boolean {
  return (
    accountCode1.accountNumber === accountCode2.accountNumber &&
    accountCode1.applicationId === accountCode2.applicationId &&
    accountCode1.bankNumber === accountCode2.bankNumber
  )
}

export function addressToAddressCode(
  address: Pick<Address, 'accountCode' | 'applicationId' | 'addressNumber'>,
): AddressCode {
  return {
    accountCode: address.accountCode,
    applicationId: address.applicationId,
    addressNumber: address.addressNumber,
  }
}

export function formToStatementPackageRequest(
  form: StatementPackageFormSchema,
  accountCode: AccountCode,
): StatementPackageCreateUpdateRequest {
  return {
    accountApplicationId: accountCode.applicationId,
    accountNumber: accountCode.accountNumber,
    bankNumber: accountCode.bankNumber,
    statementPackageNumber: form.statementPackageNumber!,
    effectiveDate: form.effectiveDate,
    addressAccountApplicationId: form.addressCode.accountCode.applicationId,
    addressAccountNumber: form.addressCode.accountCode.accountNumber,
    addressApplicationId: form.addressCode.applicationId,
    addressNumber: form.addressCode.addressNumber,
    packageDelivery: form.packageDelivery,
    packageType: form.packageType,
    selectedAccounts: form.selectedAccounts,
  }
}

export function requestToHydratedStatementPackage(
  request: StatementPackageCreateUpdateRequest,
  addresses: Address[],
): HydratedStatementPackage {
  const addressCode: AddressCode = {
    accountCode: {
      applicationId: request.addressAccountApplicationId,
      accountNumber: request.addressAccountNumber,
      bankNumber: request.bankNumber,
    },
    applicationId: request.addressApplicationId,
    addressNumber: request.addressNumber,
  }
  return {
    statementPackage: {
      accountCode: {
        applicationId: request.accountApplicationId,
        accountNumber: request.accountNumber,
        bankNumber: request.bankNumber,
      },
      statementPackageNumber: request.statementPackageNumber,
      addressCode,
      effectiveDate: request.effectiveDate,
      packageDelivery: request.packageDelivery,
      packageType: request.packageType,
    },
    selectedAccounts: request.selectedAccounts,
    address: addresses.find(
      (address) =>
        addressCodeToString(addressToAddressCode(address)) ===
        addressCodeToString(addressCode),
    )!,
  }
}

export function formToCompositeAccountCreateRequest(
  form: CompositeAccountFormSchema,
): z.infer<typeof CompositeAccountCreateRequestFormToApiSchema> {
  const { useNextAvailableAccountNumber, ...restAccountInfo } = form.accountInfo
  const { userFields, keyAccountCode, ...restDemographics } = form.demographics

  return CompositeAccountCreateRequestFormToApiSchema.parse({
    account: {
      ...restAccountInfo,
      ...restDemographics,
      processingIndicator: 'A', // always ANALYZED_ACCOUNT as opposed to BILLING_ONLY_ACCOUNT
      closeDate: null,
      accountStatus: null,
      chargeAccountCode: '',
    },
    childAccountCodes: form.subAccounts.map(accountToAccountCode),
    userFieldSelections: userFields.map((userField) => userField.selection),
    accountTypeOverride: {
      ...form.accountTypeOverride,
      accountCode: {
        applicationId: 'C',
        accountNumber: restAccountInfo.accountNumber,
        bankNumber: restAccountInfo.bankNumber,
      },
      isOverrideAsSettlementAccount:
        form.accountTypeOverride.isOverrideAsSettlementAccount,
      effectiveDate: restAccountInfo.effectiveDate,
    },
    statementPackages: form.statementPackages,
    addresses: form.addresses,
    keyChildAccountCode: keyAccountCode,
  })
}

export const formatAddress = (address: Address): string =>
  [
    address.name,
    address.addressLine2,
    address.addressLine3,
    address.addressLine4,
    address.addressLine5,
    address.addressLine6,
    address.addressLine7,
  ]
    .filter(Boolean)
    .join(', ')

export const formatAddressNumber = (
  addressNumber: AddressCode['addressNumber'],
): string => addressNumber?.toString().padStart(3, '0')

/**
 * Hacky (but necessary) conversion from Address to AddressFormSchema
 *
 * @param address the address to convert
 * @returns {AddressFormSchema} converted AddressFormSchema with streetAddress2, city, state, and zip inferred
 */
export const addressToForm = (address: Address): AddressFormSchema => {
  const hasStreetAddress2 = !address.addressLine3!.match(/, [A-Z]{2} [0-9]{5}$/)
  const cityStateZipLine =
    hasStreetAddress2 ? address.addressLine4! : address.addressLine3
  const [city, stateZipCode] = cityStateZipLine?.split(', ') ?? []
  const [state, zipCode] = stateZipCode.split(' ')

  return {
    addressNumber: address.addressNumber,
    recipient: address.name ?? '',
    streetAddress: address.addressLine2 ?? '',
    streetAddress2: hasStreetAddress2 ? address.addressLine3! : '',
    city,
    state: state as StateCode,
    zipCode,
  }
}

/**
 * Hacky (but necessary) conversion from AddressFormSchema to Address
 *
 * @param form the AddressFormSchema to convert
 * @returns {Address} converted Address with steetAddress2, city, state, and zip written to appropriate lines
 */
export const formToAddress = (
  form: AddressFormSchema,
): Omit<Address, 'accountCode' | 'effectiveDate'> => {
  const cityStateZipLine = `${form.city}, ${form.state} ${form.zipCode}`
  return {
    applicationId: 'A',
    addressNumber: form.addressNumber,
    name: form.recipient,
    addressLine2: form.streetAddress,
    addressLine3: form.streetAddress2 || cityStateZipLine,
    addressLine4: form.streetAddress2 ? cityStateZipLine : '',
    addressLine5: null,
    addressLine6: null,
    addressLine7: null,
  }
}

/**
 * Given a set of existing object numbers, calculate the next available number
 *
 * @param existingNumbers the existing object numbers
 * @returns the next available number
 */
export const getNextAvailableNumber = (existingNumbers: number[]): number => {
  if (existingNumbers.length === 0)
    // no existing numbers yet, so can default next one to 1
    return 1

  const sorted = existingNumbers.toSorted((x, y) => x - y)

  if (sorted[0] > 1)
    // smallest current number is > 1, so can default next one to 1
    return 1

  for (const { index, value } of sorted.map((value, index) => ({
    index,
    value,
  }))) {
    if (index === sorted.length - 1 || value + 1 !== sorted[index + 1])
      // we found a "gap" or are at the end, so we can use value + 1
      return value + 1
  }
  return sorted[-1] + 1
}

export function handleRemoveAccount(
  accountListItem: AccountListItem,
  form: SelectAccountFormSchemaType,
  subAccounts: BoundedAccountListItem[],
  keyAccountCode: AccountCode | null | undefined,
) {
  const newSubAccounts = subAccounts.filter(
    (account) =>
      !accountCodesAreEqual(
        account as AccountCode,
        accountListItem as AccountCode,
      ),
  )

  // Check if the key account is still in the new sub accounts tree
  let isKeyAccountStillValid = false
  const pending = [...newSubAccounts]
  while (pending.length > 0) {
    const account = pending.pop()

    if (
      account &&
      keyAccountCode &&
      accountCodesAreEqual(account, keyAccountCode)
    ) {
      isKeyAccountStillValid = true
      break
    }

    if (account?.children) {
      account.children.forEach((child) => {
        pending.push(child as BoundedAccountListItem)
      })
    }
  }

  if (!isKeyAccountStillValid) {
    form.setFieldValue('demographics.keyAccountCode', null)
  }

  form.setFieldValue('subAccounts', newSubAccounts)
}
export const getDepth = (item: BoundedAccountListItem, depth = 1): number => {
  if (!item.children || item.children.length === 0) {
    return depth
  }
  return Math.max(...item.children.map((child) => getDepth(child, depth + 1)))
}
export function mappingsToAccountsMap(
  mappings: HydratedAccountWithKeyAccountMappingForm[],
): Record<string, BoundedAccountListItem> {
  const accountsMap: Record<string, BoundedAccountListItem> = {}

  mappings.forEach((edge) => {
    const parent = edge.parent ? { ...edge.parent, children: [] } : null
    const parentCode = edge.parent ? toAccountCodeString(edge.parent) : null

    const child = { ...edge.child, children: [] }
    const childCode = toAccountCodeString(edge.child)

    if (parent && parentCode && accountsMap[parentCode] === undefined) {
      accountsMap[parentCode] = parent
    }

    if (child && childCode && accountsMap[childCode] === undefined) {
      accountsMap[childCode] = child
    }

    if (child && childCode && parent && parentCode) {
      // If we want to strictly enforce a depth limit on account hierarchies, we can do it here
      if (getDepth(accountsMap[parentCode]!) >= 7) {
        console.warn('Account hierarchy is greater than 7 levels deep...')
      }
      ;(accountsMap[parentCode]?.children as BoundedAccountListItem[])?.push(
        accountsMap[childCode]!,
      )
    }
  })

  return accountsMap
}

/**
 * Helper that mimicks the backend's backend logic. Can be used to approximate as-of logic in mock date.
 *
 * @param objs objects of type `T`
 * @param asOfDate as of date to evaluate
 * @param keyMapper maps an object of type `T` to a string key (e.g., "code")
 * @returns `objs` filtered down to the latest of each key as of `asOfDate`
 */
export function getAsOf<T extends { effectiveDate: Date }>(
  objs: T[],
  asOfDate: Date,
  keyMapper: (obj: T) => string,
) {
  const groupedByKey = objs.reduce(
    (acc, obj) => {
      const key = keyMapper(obj)
      const timelineForKey = acc[key] ?? []
      timelineForKey.push(obj)
      acc[key] = timelineForKey
      return acc
    },
    {} as { [key: string]: T[] },
  )
  const objsAsOf: T[] = []
  Object.values(groupedByKey).forEach((keyObjs) => {
    keyObjs.sort(
      (a, b) => a.effectiveDate.getTime() - b.effectiveDate.getTime(),
    )
    const keyObjsAsOf = keyObjs.filter(
      (obj) => obj.effectiveDate.getTime() <= asOfDate.getTime(),
    )
    if (keyObjsAsOf.length > 0)
      objsAsOf.push(keyObjsAsOf[keyObjsAsOf.length - 1])
  })
  return objsAsOf
}

function getDefaultUserFieldsListItems(
  userFieldConfigurations: HydratedUserFieldConfigurationForm[],
  accountCode: AccountCode,
) {
  let defaultFieldsConf: UserFieldListItem[] = []
  if (userFieldConfigurations) {
    defaultFieldsConf = userFieldConfigurations.map(
      (configuration: HydratedUserFieldConfigurationForm) => {
        const selection: UserFieldListItem['selection'] = {
          applicationId: accountCode.applicationId,
          accountNumber: accountCode.accountNumber,
          bankNumber: accountCode.bankNumber,
          effectiveDate: toUTCDateString(new Date()),
          userFieldCode: configuration.code,
          applyToChildAccounts: false,
          isUnset: true,
          booleanValue: null,
          dropdownOptionCode: null,
          expiry: null,
          freeformValue: null,
        }
        return {
          configuration: configuration,
          selection: selection,
        }
      },
    )
  }
  return defaultFieldsConf
}

export function getUserFieldsListItemsFromConfigsAndSelections(
  accountCode: AccountCode,
  userFieldsConfigurations?: HydratedUserFieldConfigurationForm[],
  userFieldSelections?: UserFieldSelectionForm[],
): UserFieldListItem[] {
  // use default options to show they exist and are able to be added in edit and view screen
  if (userFieldsConfigurations && userFieldSelections?.length === 0) {
    return getDefaultUserFieldsListItems(userFieldsConfigurations, accountCode)
  }
  if (!userFieldsConfigurations || !userFieldSelections) {
    return []
  }

  const userFieldsConfigurationsMap: Record<
    number,
    HydratedUserFieldConfigurationForm
  > = {}

  userFieldsConfigurations.forEach(
    (configuration: HydratedUserFieldConfigurationForm) => {
      if (configuration.code) {
        userFieldsConfigurationsMap[configuration.code] = configuration
      }
    },
  )

  return userFieldSelections.map((selection) => {
    return {
      configuration: userFieldsConfigurationsMap[selection.userFieldCode!],
      selection,
    } as UserFieldListItem
  })
}

export function apiAccountSelector(
  apiAccount: z.input<typeof AccountApiToFormSchema>,
) {
  return AccountZodSchema.parse(AccountApiToFormSchema.parse(apiAccount))
}

export function apiAccountListSelector(
  apiAccounts: z.input<typeof AccountApiToFormSchema>[],
) {
  return apiAccounts.map(apiAccountSelector)
}

export function apiMappingWithKeySelector(
  apiMapping: HydratedAccountWithKeyAccountMapping,
): HydratedAccountWithKeyAccountMappingForm {
  return {
    child: {
      ...apiMapping.child,
      keyAccountCode: apiMapping.child.keyAccountCode ?? null,
    } as HydratedAccountWithKeyAccountMappingForm['child'],
    parent:
      apiMapping.parent ?
        ({
          ...apiMapping.parent,
          keyAccountCode: apiMapping.parent.keyAccountCode ?? null,
        } as HydratedAccountWithKeyAccountMappingForm['parent'])
      : null,
  }
}
export function apiMappingWithKeyListSelector(
  apiMappings: HydratedAccountWithKeyAccountMapping[],
): HydratedAccountWithKeyAccountMappingForm[] {
  return apiMappings.map(apiMappingWithKeySelector)
}

export function searchMatchingAccounts(
  searchQuery: string,
  accounts: Account[],
): AccountSearchItem[] {
  if (searchQuery === '') {
    return []
  }

  const lowerSearchQuery = searchQuery.toLowerCase()
  const results = []

  for (const account of accounts) {
    if (
      (account.shortName &&
        account.shortName.toLowerCase().includes(lowerSearchQuery)) ||
      (account.accountNumber &&
        account.accountNumber.toLowerCase().includes(lowerSearchQuery)) ||
      (account.applicationId &&
        account.accountNumber &&
        formatAccountCode(account as AccountCode).includes(lowerSearchQuery))
    ) {
      results.push({
        shortName: account.shortName,
        accountNumber: account.accountNumber,
        applicationId: account.applicationId,
        bankNumber: account.bankNumber,
      })
    }
  }

  return results as AccountSearchItem[]
}

export function isAccountKeyAccount(
  keyAccount: AccountCode | null,
  accountListItem: AccountListItem,
) {
  return (
    (keyAccount && accountCodesAreEqual(keyAccount, accountListItem)) ||
    accountListItem.isKeyAccount
  )
}

export interface SettlementProcessingOptionsData {
  settlementOptions: SettlementOptionItem[]
  processingOptions: ProcessingOptionItem[]
}

export const transformToSettlementOptions = (
  accountTypeOverride: any | null | undefined,
): SettlementProcessingOptionItemWithId[] => {
  if (!accountTypeOverride) return []

  return [
    {
      id: '1',
      name: 'Analysis result options',
      field: 'analysisResultOptionsPlanCode',
      override: !!accountTypeOverride.analysisResultOptionsPlanCode,
      plan: accountTypeOverride.analysisResultOptionsPlanCode || 'Default',
      planCode: accountTypeOverride.analysisResultOptionsPlanCode || undefined,
      expiry: accountTypeOverride.analysisResultOptionsPlanCodeExpiry || null,
    },
    {
      id: '2',
      name: 'Settlement cycle',
      field: 'settlementCyclePlanCode',
      override: !!accountTypeOverride.settlementCyclePlanCode,
      plan: accountTypeOverride.settlementCyclePlanCode || 'Default',
      planCode: accountTypeOverride.settlementCyclePlanCode || undefined,
      expiry: accountTypeOverride.settlementCyclePlanCodeExpiry || null,
    },
  ]
}

export const transformToProcessingOptions = (
  accountTypeOverride: any | null | undefined,
): SettlementProcessingOptionItemWithId[] => {
  if (!accountTypeOverride) return []

  return [
    {
      id: '1',
      name: 'Earnings credit',
      field: 'earningsCreditDefinitionCode',
      override: !!accountTypeOverride.earningsCreditDefinitionCode,
      plan: accountTypeOverride.earningsCreditDefinitionCode || 'Default',
      planCode: accountTypeOverride.earningsCreditDefinitionCode || undefined,
      expiry: accountTypeOverride.earningsCreditDefinitionCodeExpiry || null,
    },
    {
      id: '2',
      name: 'Investable balance',
      field: 'investableBalanceDefinitionCode',
      override: !!accountTypeOverride.investableBalanceDefinitionCode,
      plan: accountTypeOverride.investableBalanceDefinitionCode || 'Default',
      planCode:
        accountTypeOverride.investableBalanceDefinitionCode || undefined,
      expiry: accountTypeOverride.investableBalanceDefinitionCodeExpiry || null,
    },
    {
      id: '3',
      name: 'Balance requirement',
      field: 'balanceRequirementDefinitionCode',
      override: !!accountTypeOverride.balanceRequirementDefinitionCode,
      plan: accountTypeOverride.balanceRequirementDefinitionCode || 'Default',
      planCode:
        accountTypeOverride.balanceRequirementDefinitionCode || undefined,
      expiry:
        accountTypeOverride.balanceRequirementDefinitionCodeExpiry || null,
    },
    {
      id: '4',
      name: 'Statement cycle',
      field: 'statementCyclePlanCode',
      override: !!accountTypeOverride.statementCyclePlanCode,
      plan: accountTypeOverride.statementCyclePlanCode || 'Default',
      planCode: accountTypeOverride.statementCyclePlanCode || undefined,
      expiry: accountTypeOverride.statementCyclePlanCodeExpiry || null,
    },
    {
      id: '5',
      name: 'Reserve requirement',
      field: 'reserveRequirementDefinitionCode',
      override: !!accountTypeOverride.reserveRequirementDefinitionCode,
      plan: accountTypeOverride.reserveRequirementDefinitionCode || 'Default',
      planCode:
        accountTypeOverride.reserveRequirementDefinitionCode || undefined,
      expiry:
        accountTypeOverride.reserveRequirementDefinitionCodeExpiry || null,
    },
    {
      id: '6',
      name: 'Interest',
      field: 'interestRequirementDefinitionCode',
      override: !!accountTypeOverride.interestRequirementDefinitionCode,
      plan: accountTypeOverride.interestRequirementDefinitionCode || 'Default',
      planCode:
        accountTypeOverride.interestRequirementDefinitionCode || undefined,
      expiry:
        accountTypeOverride.interestRequirementDefinitionCodeExpiry || null,
    },
  ]
}

export const transformToSettlementProcessingOptions = (
  accountTypeOverride: any | null | undefined,
): SettlementProcessingOptionsData => {
  return {
    settlementOptions: transformToSettlementOptions(accountTypeOverride),
    processingOptions: transformToProcessingOptions(accountTypeOverride),
  }
}