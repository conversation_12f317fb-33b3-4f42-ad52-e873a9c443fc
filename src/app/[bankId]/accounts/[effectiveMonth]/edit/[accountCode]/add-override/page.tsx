'use client'
import { InfoSection, InfoSectionDescription, InfoSectionTitle } from "@/components/InfoSection";
import { Button } from '@/components/Button'
import { data } from '@/lib/unions/Union'
import { useAccountFromParams } from '@/app/[bankId]/accounts/_hooks/useAccountFromParams'
import { useRoute } from '../../routing'
import { useRouter } from "next/navigation";
import { accountToAccountCode } from "@/app/[bankId]/accounts/accountHelpers";

export default function Page() {
  const router = useRouter()
    const route = useRoute()!
    const routeParams = data(route).params
    const accountFromParams = useAccountFromParams(routeParams)
    const account = accountFromParams.account!
    // const accountCode = accountToAccountCode(account)
    // const effectiveDate = accountFromParams.effectiveDate

  return (
  <InfoSection>
        <div className='flex items-center justify-between pb-4'>
          <div>
            <InfoSectionTitle>Add Pricing Overrides</InfoSectionTitle>
            <InfoSectionDescription>
              {`${account.shortName} ${account.applicationId}-${account.accountNumber}`}
            </InfoSectionDescription>
          </div>
        </div>
      </InfoSection>
    )
}
