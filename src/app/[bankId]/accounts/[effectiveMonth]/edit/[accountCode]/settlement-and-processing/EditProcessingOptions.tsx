'use client'

import {
  InfoSectionDescription,
  InfoSubSectionTitle,
} from '@/components/InfoSection'
import { SortedTable } from '@/components/Table/SortedTable'
import { useMemo, useState } from 'react'
import { useStore } from '@tanstack/react-form'
import { formatToServerString, toUTCDateString } from '@/lib/date'
import { useConfigurations } from '@/app/[bankId]/configuration/_hooks/useConfigurations'
import { ToggleSwitch } from '@/components/Toggle'
import { useQuery } from '@tanstack/react-query'
import { accountQueries } from '@/app/[bankId]/accounts/queries'
import { transformToProcessingOptions } from '@/app/[bankId]/accounts/accountHelpers'
import { SelectProcessingOption } from '@/components/ProcessingOptions/SelectProcessingOption'
import { ProcessingOptionExpirationDate } from '@/components/ProcessingOptions/ProcessingOptionExpirationDate'
import {
  ProcessingOptionItem,
  SetProcessingOptionsProps,
  processingOptions,
  ProcessingOptionsColumnDef,
} from '@/app/[bankId]/accounts/types'

export function EditProcessingOptions({
  form,
  effectiveDate,
  accountCode,
}: SetProcessingOptionsProps) {
  // The edit page uses a flat form structure where fields are at the root level
  const [formValues] = useStore(form.store, (state) => [state.values])

  // Parse accountCode to get individual components
  const [applicationId, bankNumber, accountNumber] = accountCode.split('-') as [
    'C' | 'D',
    string,
    string,
  ]

  // Fetch accountTypeOverride data to get default values
  const { data: accountTypeOverride } = useQuery(
    accountQueries('/getAccountTypeOverride', {
      bankNumber,
      applicationId,
      accountNumber,
      effectiveDate,
    }),
  )

  // Memoize the default processing options to prevent recreation on every render
  const defaultProcessingOptions = useMemo(() => {
    return transformToProcessingOptions(accountTypeOverride)
  }, [accountTypeOverride])

  // Calculate toggle states based on current form values and default options
  const toggleStates = useMemo(() => {
    const states: Record<string, boolean> = {}
    processingOptions.forEach((option) => {
      const fieldValue = formValues?.[option.field]
      const defaultOption = defaultProcessingOptions.find(
        (opt) => opt.field === option.field,
      )

      // Check if override should be enabled:
      // 1. If field already has a value, enable override
      // 2. If default value has a non-null plan, enable override
      const hasFieldValue =
        fieldValue !== null && fieldValue !== undefined && fieldValue !== ''
      const hasDefaultPlan =
        defaultOption?.planCode !== null &&
        defaultOption?.planCode !== undefined

      states[option.field] = hasFieldValue || hasDefaultPlan
    })
    return states
  }, [formValues, defaultProcessingOptions])

  // State to track user manual toggle changes (overrides the calculated state)
  const [manualToggleOverrides, setManualToggleOverrides] = useState<
    Record<string, boolean | undefined>
  >({})

  const {
    earningsCreditDefinitions: { data: earningsCreditDefinitions },
    investableBalanceDefinitions: { data: investableBalanceDefinitions },
    requiredBalanceDefinitions: { data: requiredBalanceDefinitions },
    cycleDefinitions: { data: cycleDefinitions },
    statementFormatPlans: { data: statementFormatPlans },
  } = useConfigurations({ effectiveDate })

  const earningsCreditOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    if (earningsCreditDefinitions) {
      earningsCreditDefinitions.forEach((option) => {
        const code = `${option.code}`
        const name = `${option.description}`
        optionsMap.set(code, name)
      })
    }
    return optionsMap
  }, [earningsCreditDefinitions])

  const investableBalanceOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    if (investableBalanceDefinitions) {
      investableBalanceDefinitions.forEach((option) => {
        const code = `${option.code}`
        const name = `${option.name}`
        optionsMap.set(code, name)
      })
    }
    return optionsMap
  }, [investableBalanceDefinitions])

  const requiredBalanceOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    if (requiredBalanceDefinitions) {
      requiredBalanceDefinitions.forEach((option) => {
        const code = `${option.code}`
        const name = `${option.name}`
        optionsMap.set(code, name)
      })
    }
    return optionsMap
  }, [requiredBalanceDefinitions])

  const statementCycleOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    if (cycleDefinitions) {
      cycleDefinitions.forEach((option) => {
        const code = `${option.code}`
        const name = `${option.description}`
        if (option.cycleType === 'STATEMENT') {
          optionsMap.set(code, name)
        }
      })
    }
    return optionsMap
  }, [cycleDefinitions])

  const statementFormatOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    if (statementFormatPlans) {
      statementFormatPlans.forEach((option) => {
        const code = `${option.code}`
        const name = `${option.description}`
        optionsMap.set(code, name)
      })
    }
    return optionsMap
  }, [statementFormatPlans])

  const allOptionsMaps = useMemo<
    Map<ProcessingOptionItem['field'], Map<string, string>>
  >(() => {
    const optionsMap: Map<
      ProcessingOptionItem['field'],
      Map<string, string>
    > = new Map()
    optionsMap.set('earningsCreditDefinitionCode', earningsCreditOptionsMap)
    optionsMap.set(
      'investableBalanceDefinitionCode',
      investableBalanceOptionsMap,
    )
    optionsMap.set(
      'balanceRequirementDefinitionCode',
      requiredBalanceOptionsMap,
    )
    optionsMap.set('statementCyclePlanCode', statementCycleOptionsMap)
    optionsMap.set('statementFormatPlanCode', statementFormatOptionsMap)
    return optionsMap
  }, [
    earningsCreditOptionsMap,
    investableBalanceOptionsMap,
    requiredBalanceOptionsMap,
    statementCycleOptionsMap,
    statementFormatOptionsMap,
  ])

  const columns = useMemo<ProcessingOptionsColumnDef[]>(() => {
    return [
      {
        header: 'Override',
        meta: {
          className: 'w-30 ml-6 mr-6',
        },
        cell: ({ row }) => {
          const field = row.original.field as ProcessingOptionItem['field']
          // Use calculated toggle state, but allow manual override
          const calculatedState = toggleStates[field] || false
          const manualOverride = manualToggleOverrides[field]
          const isToggleEnabled =
            manualOverride !== undefined ? manualOverride : calculatedState

          return (
            <ToggleSwitch
              key={`${field}-toggle`}
              checked={isToggleEnabled}
              onChange={(checked: boolean) => {
                // Update manual toggle override
                setManualToggleOverrides((prev) => ({
                  ...prev,
                  [field]: checked,
                }))

                if (checked) {
                  // When enabling override, set the default plan code if available, otherwise empty
                  const defaultOption = defaultProcessingOptions.find(
                    (opt) => opt.field === field,
                  )
                  const defaultPlanCode = defaultOption?.planCode || ''
                  form.setFieldValue(field, defaultPlanCode)
                } else {
                  // If disabling override, clear the field value and expiry
                  form.setFieldValue(field, null)
                  form.setFieldValue(`${field}Expiry`, null)
                }
              }}
            />
          )
        },
      },
      {
        header: 'Processing setting',
        accessorKey: 'name',
        meta: {
          className: 'ml-4 basis-full',
        },
      },
      {
        header: 'Plan',
        meta: {
          className: 'basis-full',
        },
        cell: ({ row }) => {
          const field = row.original.field as ProcessingOptionItem['field']
          const fieldValue = formValues?.[field]
          const optionsMap = allOptionsMaps.get(field)
          // Use calculated toggle state, but allow manual override
          const calculatedState = toggleStates[field] || false
          const manualOverride = manualToggleOverrides[field]
          const isToggleEnabled =
            manualOverride !== undefined ? manualOverride : calculatedState

          // Get default value from accountTypeOverride
          const defaultOption = defaultProcessingOptions.find(
            (opt) => opt.field === field,
          )

          if (!optionsMap) {
            return <div className='min-w-80'>Loading options...</div>
          }

          return (
            <div className='min-w-80'>
              {isToggleEnabled ?
                <SelectProcessingOption
                  field={field}
                  value={fieldValue || ''}
                  optionsMap={optionsMap}
                  disabled={!isToggleEnabled}
                  defaultValue={defaultOption?.plan || 'Default'}
                  onChange={(value) => {
                    form.setFieldValue(field, value)
                  }}
                />
              : <div>{defaultOption?.plan || 'Default'}</div>}
            </div>
          )
        },
      },
      {
        header: 'Expiration date',
        meta: {
          className: 'ml-4 basis-full',
        },
        cell: ({ row }) => {
          const field = row.original.field
          const expiryValue = formValues?.[`${field}Expiry`]
          // Use calculated toggle state, but allow manual override
          const calculatedState = toggleStates[field] || false
          const manualOverride = manualToggleOverrides[field]
          const isToggleEnabled =
            manualOverride !== undefined ? manualOverride : calculatedState

          return (
            <ProcessingOptionExpirationDate
              isVisible={isToggleEnabled}
              expiry={expiryValue || undefined}
              disabled={!isToggleEnabled}
              onCheckboxChange={(checked: boolean) => {
                // Always allow changes when component is not disabled
                form.setFieldValue(
                  `${field}Expiry`,
                  checked ? null : toUTCDateString(new Date()),
                )
              }}
              onMonthPickerChange={(date: string) => {
                // Always allow changes when component is not disabled
                form.setFieldValue(`${field}Expiry`, formatToServerString(date))
              }}
            />
          )
        },
      },
    ]
  }, [
    allOptionsMaps,
    formValues,
    form,
    toggleStates,
    manualToggleOverrides,
    setManualToggleOverrides,
    defaultProcessingOptions,
  ])

  return (
    <>
      <InfoSubSectionTitle>Processing options</InfoSubSectionTitle>
      <InfoSectionDescription className='-mt-3'>
        These options are inherited from the account type of the key account.
      </InfoSectionDescription>

      <SortedTable
        data={processingOptions}
        columns={columns}
        columnFilters={[]}
      />
    </>
  )
}
