'use client'

import {
  InfoSectionDescription,
  InfoSubSectionTitle,
} from '@/components/InfoSection'
import { SortedTable } from '@/components/Table/SortedTable'
import { ColumnDef } from '@tanstack/react-table'
import { useMemo, useState, useEffect } from 'react'
import { useStore, FormApi } from '@tanstack/react-form'
import { Select } from '@/components/Input/Select'
import { Checkbox } from '@/components/Checkbox'
import { MonthPicker } from '@/components/Filter/MonthPicker'
import { formatToServerString, toUTCDateString } from '@/lib/date'
import { useConfigurations } from '@/app/[bankId]/configuration/_hooks/useConfigurations'
import { AccountTypeOverrideForm } from '@/api/formToApiSchema'
import { ToggleSwitch } from '@/components/Toggle'
import { useQuery } from '@tanstack/react-query'
import { accountQueries } from '@/app/[bankId]/accounts/queries'
import { transformToProcessingOptions } from '@/api/apiToFormSchema'

type ProcessingOptionItem = {
  field:
    | 'earningsCreditDefinitionCode'
    | 'investableBalanceDefinitionCode'
    | 'balanceRequirementDefinitionCode'
    | 'statementCyclePlanCode'
    | 'statementFormatPlanCode'
  name: string
}

type ProcessingOptionsColumnDef = ColumnDef<ProcessingOptionItem>

interface EditProcessingOptionsProps {
  form: FormApi<AccountTypeOverrideForm, undefined>,
  effectiveDate: string,
  accountCode: string
}

interface SelectProcessingOptionProps {
  field: string
  value: string
  optionsMap: Map<string, string>
  disabled?: boolean
  defaultValue?: string
  onChange: (value: string) => void
}

interface EditProcessingOptionExpirationDateProps {
  expiry?: string
  isVisible: boolean
  disabled?: boolean
  onCheckboxChange: (checked: boolean) => void
  onMonthPickerChange: (date: string) => void
}

const processingOptions: ProcessingOptionItem[] = [
  {
    field: 'earningsCreditDefinitionCode',
    name: 'Earnings credit',
  },
  {
    field: 'investableBalanceDefinitionCode',
    name: 'Investable balance',
  },
  {
    field: 'balanceRequirementDefinitionCode',
    name: 'Required balance',
  },
  {
    field: 'statementCyclePlanCode',
    name: 'Statement cycle',
  },
  {
    field: 'statementFormatPlanCode',
    name: 'Statement format',
  },
]

export function EditProcessingOptions({
  form,
  effectiveDate,
  accountCode,
}: EditProcessingOptionsProps) {
  // The edit page uses a flat form structure where fields are at the root level
  const [formValues] = useStore(form.store, (state) => [state.values])

  // Parse accountCode to get individual components
  const [applicationId, bankNumber, accountNumber] = accountCode.split('-') as ['C' | 'D', string, string]

  // Fetch accountTypeOverride data to get default values
  const {
    data: accountTypeOverride,
  } = useQuery(
    accountQueries('/getAccountTypeOverride', {
      bankNumber,
      applicationId,
      accountNumber,
      effectiveDate,
    }),
  )

  // Transform the API response to get default processing options
  const defaultProcessingOptions = transformToProcessingOptions(accountTypeOverride)

  // State to track which toggles are enabled (independent of field values)
  const [toggleStates, setToggleStates] = useState<Record<string, boolean>>({})

  // Update toggle states when default data is loaded or form values change
  useEffect(() => {
    const newToggleStates: Record<string, boolean> = {}
    processingOptions.forEach((option) => {
      const fieldValue = formValues?.[option.field]
      const defaultOption = defaultProcessingOptions.find(opt => opt.field === option.field)

      // Check if override should be enabled by default:
      // 1. If field already has a value, enable override
      // 2. If default value has a non-null plan, enable override
      const hasFieldValue = fieldValue !== null && fieldValue !== undefined && fieldValue !== ''
      const hasDefaultPlan = defaultOption?.planCode !== null && defaultOption?.planCode !== undefined

      newToggleStates[option.field] = hasFieldValue || hasDefaultPlan
    })
    setToggleStates(newToggleStates)
  }, [defaultProcessingOptions, formValues])

  const {
    earningsCreditDefinitions: { data: earningsCreditDefinitions },
    investableBalanceDefinitions: { data: investableBalanceDefinitions },
    requiredBalanceDefinitions: { data: requiredBalanceDefinitions },
    cycleDefinitions: { data: cycleDefinitions },
    statementFormatPlans: { data: statementFormatPlans },
  } = useConfigurations({ effectiveDate })

  const earningsCreditOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    if (earningsCreditDefinitions) {
      earningsCreditDefinitions.forEach((option) => {
        const code = `${option.code}`
        const name = `${option.description}`
        optionsMap.set(code, name)
      })
    }
    return optionsMap
  }, [earningsCreditDefinitions])

  const investableBalanceOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    if (investableBalanceDefinitions) {
      investableBalanceDefinitions.forEach((option) => {
        const code = `${option.code}`
        const name = `${option.name}`
        optionsMap.set(code, name)
      })
    }
    return optionsMap
  }, [investableBalanceDefinitions])

  const requiredBalanceOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    if (requiredBalanceDefinitions) {
      requiredBalanceDefinitions.forEach((option) => {
        const code = `${option.code}`
        const name = `${option.name}`
        optionsMap.set(code, name)
      })
    }
    return optionsMap
  }, [requiredBalanceDefinitions])

  const statementCycleOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    if (cycleDefinitions) {
      cycleDefinitions.forEach((option) => {
        const code = `${option.code}`
        const name = `${option.description}`
        if (option.cycleType === 'STATEMENT') {
          optionsMap.set(code, name)
        }
      })
    }
    return optionsMap
  }, [cycleDefinitions])

  const statementFormatOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    if (statementFormatPlans) {
      statementFormatPlans.forEach((option) => {
        const code = `${option.code}`
        const name = `${option.description}`
        optionsMap.set(code, name)
      })
    }
    return optionsMap
  }, [statementFormatPlans])

  const allOptionsMaps = useMemo<
    Map<ProcessingOptionItem['field'], Map<string, string>>
  >(() => {
    const optionsMap: Map<
      ProcessingOptionItem['field'],
      Map<string, string>
    > = new Map()
    optionsMap.set('earningsCreditDefinitionCode', earningsCreditOptionsMap)
    optionsMap.set(
      'investableBalanceDefinitionCode',
      investableBalanceOptionsMap,
    )
    optionsMap.set(
      'balanceRequirementDefinitionCode',
      requiredBalanceOptionsMap,
    )
    optionsMap.set('statementCyclePlanCode', statementCycleOptionsMap)
    optionsMap.set('statementFormatPlanCode', statementFormatOptionsMap)
    return optionsMap
  }, [
    earningsCreditOptionsMap,
    investableBalanceOptionsMap,
    requiredBalanceOptionsMap,
    statementCycleOptionsMap,
    statementFormatOptionsMap,
  ])

  const columns = useMemo<ProcessingOptionsColumnDef[]>(() => {
    return [
      {
        header: 'Override',
        meta: {
          className: 'w-30 ml-6 mr-6',
        },
        cell: ({ row }) => {
          const field = row.original.field as ProcessingOptionItem['field']
          // Use toggle state instead of field value
          const isToggleEnabled = toggleStates[field] || false

          return (
            <ToggleSwitch
              key={`${field}-toggle`}
              checked={isToggleEnabled}
              onChange={(checked: boolean) => {
                // Update toggle state
                setToggleStates(prev => ({ ...prev, [field]: checked }))

                if (checked) {
                  // When enabling override, set the default plan code if available, otherwise empty
                  const defaultOption = defaultProcessingOptions.find(opt => opt.field === field)
                  const defaultPlanCode = defaultOption?.planCode || ''
                  form.setFieldValue(field as any, defaultPlanCode)
                } else {
                  // If disabling override, clear the field value and expiry
                  form.setFieldValue(field as any, null)
                  form.setFieldValue(`${field}Expiry` as any, null)
                }
              }}
            />
          )
        },
      },
      {
        header: 'Processing setting',
        accessorKey: 'name',
        meta: {
          className: 'ml-4 basis-full',
        },
      },
      {
        header: 'Plan',
        meta: {
          className: 'basis-full',
        },
        cell: ({ row }) => {
          const field = row.original.field as ProcessingOptionItem['field']
          const fieldValue = formValues?.[field]
          const optionsMap = allOptionsMaps.get(field)
          // Use toggle state instead of field value
          const isToggleEnabled = toggleStates[field] || false

          // Get default value from accountTypeOverride
          const defaultOption = defaultProcessingOptions.find(opt => opt.field === field)

          if (!optionsMap) {
            return <div className='min-w-80'>Loading options...</div>
          }

          return (
            <div className='min-w-80'>
                {
                    isToggleEnabled ? <SelectProcessingOption
                        field={field}
                        value={fieldValue || ''}
                        optionsMap={optionsMap}
                        disabled={!isToggleEnabled}
                        defaultValue={defaultOption?.plan ||  'Default'}
                        onChange={(value) => {
                          form.setFieldValue(field as any, value)
                        }}
                         /> : <div>{defaultOption?.plan || 'Default'}</div>
                }
              
            </div>
          )
        },
      },
      {
        header: 'Expiration date',
        meta: {
          className: 'ml-4 basis-full',
        },
        cell: ({ row }) => {
          const field = row.original.field
          const expiryValue = formValues?.[`${field}Expiry`]
          // Use toggle state instead of field value
          const isToggleEnabled = toggleStates[field] || false

          return (
            <EditProcessingOptionExpirationDate
              isVisible={isToggleEnabled}
              expiry={expiryValue || undefined}
              disabled={!isToggleEnabled}
              onCheckboxChange={(checked: boolean) => {
                // Always allow changes when component is not disabled
                form.setFieldValue(
                  `${field}Expiry` as any,
                  checked ? undefined : toUTCDateString(new Date()),
                )
              }}
              onMonthPickerChange={(date: string) => {
                // Always allow changes when component is not disabled
                form.setFieldValue(
                  `${field}Expiry` as any,
                  formatToServerString(date),
                )
              }}
            />
          )
        },
      },
    ]
  }, [allOptionsMaps, formValues, form, toggleStates, setToggleStates, defaultProcessingOptions])

  return (
    <>
      <InfoSubSectionTitle>Processing options</InfoSubSectionTitle>
      <InfoSectionDescription className='-mt-3'>
        These options are inherited from the account type of the key account.
      </InfoSectionDescription>

      <SortedTable
        data={processingOptions}
        columns={columns}
        columnFilters={[]}
      />
    </>
  )
}

function SelectProcessingOption({
  field,
  value,
  optionsMap,
  disabled = false,
  defaultValue,
  onChange,
}: SelectProcessingOptionProps) {
  if (disabled) {
    // Show default value when disabled (similar to view page)
    return (
      <div className='mt-6 min-w-96 px-3 py-2 text-gray-700 bg-gray-50 border border-gray-200 rounded'>
        <div className='flex flex-row gap-2'>
          <div>{defaultValue}</div>
        </div>
      </div>
    )
  }

  return (
    <Select
      className='mt-6 min-w-96'
      name={field}
      value={value}
      options={Array.from(optionsMap.keys())}
      disabled={disabled}
      onChange={onChange}
      renderOption={(option) => {
        const optionName = optionsMap.get(option)
        return `${optionName} ${option}`
      }}
      renderSelected={(option) => {
        const optionName = optionsMap.get(option)
        return (
          <div className='flex flex-row gap-2'>
            <div>{optionName}</div>
            <div>{option}</div>
          </div>
        )
      }}
    />
  )
}

function EditProcessingOptionExpirationDate({
  expiry,
  isVisible,
  disabled = false,
  onCheckboxChange,
  onMonthPickerChange,
}: EditProcessingOptionExpirationDateProps) {
  return (
    isVisible && (
      <div className='flex flex-row gap-3'>
        <Checkbox
          checked={!expiry}
          label='No expiration'
          disabled={disabled}
          onChange={onCheckboxChange}
        />
        {expiry && !disabled && (
          <MonthPicker
            className='my-1 h-9 min-w-32 bg-white ring-1 ring-inset ring-gray-300'
            initialDate={expiry}
            onDateChange={onMonthPickerChange}
          />
        )}
        {expiry && disabled && (
          <div className='my-1 h-9 min-w-32 flex items-center px-3 text-gray-500 bg-gray-100 border border-gray-300 rounded'>
            {expiry}
          </div>
        )}
      </div>
    )
  )
}
