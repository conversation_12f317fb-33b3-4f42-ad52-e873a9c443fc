'use client'

import {
  InfoSectionDescription,
  InfoSubSectionTitle,
} from '@/components/InfoSection'

import { ColumnDef } from '@tanstack/react-table'
import { useMemo, useState, useEffect } from 'react'
import { SortedTable } from '@/components/Table/SortedTable'
import { Select } from '@/components/Input/Select'
import { useStore, FormApi } from '@tanstack/react-form'
import { AccountTypeOverrideForm } from '@/api/formToApiSchema'
import { Checkbox } from '@/components/Checkbox'
import { MonthPicker } from '@/components/Filter/MonthPicker'
import { formatToServerString, toUTCDateString } from '@/lib/date'
import { useConfigurations } from '@/app/[bankId]/configuration/_hooks/useConfigurations'
import { ToggleSwitch } from '@/components/Toggle'
import { useQuery } from '@tanstack/react-query'
import { accountQueries } from '@/app/[bankId]/accounts/queries'
import { transformToSettlementOptions } from '@/api/apiToFormSchema'

type SettlementOptionItem = {
  field: 'analysisResultOptionsPlanCode' | 'settlementCyclePlanCode'
  name: string
}

const settlementOptions: SettlementOptionItem[] = [
  {
    field: 'analysisResultOptionsPlanCode',
    name: 'Analysis result options',
  },
  {
    field: 'settlementCyclePlanCode',
    name: 'Settlement cycle',
  },
]

type SettlementOptionsColumnDef = ColumnDef<SettlementOptionItem>

interface EditSettlementOptionsProps {
  form: FormApi<AccountTypeOverrideForm, undefined>,
  effectiveDate: string,
  accountCode: string
}

interface SelectSettlementOptionProps {
  field: string
  value: string
  optionsMap: Map<string, string>
  disabled?: boolean
  defaultValue?: string
  onChange: (value: string) => void
}

interface EditSettlementOptionExpirationDateProps {
  expiry?: string
  isVisible: boolean
  disabled?: boolean
  onCheckboxChange: (checked: boolean) => void
  onMonthPickerChange: (date: string) => void
}

export default function EditSettlementOptions({
  form,
  effectiveDate,
  accountCode,
}: EditSettlementOptionsProps) {
  // The edit page uses a flat form structure where fields are at the root level
  const [formValues] = useStore(form.store, (state: any) => [state.values])

  // Parse accountCode to get individual components
  const [applicationId, bankNumber, accountNumber] = accountCode.split('-') as ['C' | 'D', string, string]

  // Fetch accountTypeOverride data to get default values
  const {
    data: accountTypeOverride,
  } = useQuery(
    accountQueries('/getAccountTypeOverride', {
      bankNumber,
      applicationId,
      accountNumber,
      effectiveDate,
    }),
  )

  // Transform the API response to get default settlement options
  const defaultSettlementOptions = transformToSettlementOptions(accountTypeOverride)

  // State to track which toggles are enabled (independent of field values)
  const [toggleStates, setToggleStates] = useState<Record<string, boolean>>({})

  // Update toggle states when default data is loaded or form values change
  useEffect(() => {
    const newToggleStates: Record<string, boolean> = {}
    settlementOptions.forEach((option) => {
      const fieldValue = formValues?.[option.field]
      const defaultOption = defaultSettlementOptions.find(opt => opt.field === option.field)

      // Check if override should be enabled by default:
      // 1. If field already has a value, enable override
      // 2. If default value has a non-null plan, enable override
      const hasFieldValue = fieldValue !== null && fieldValue !== undefined && fieldValue !== ''
      const hasDefaultPlan = defaultOption?.planCode !== null && defaultOption?.planCode !== undefined

      newToggleStates[option.field] = hasFieldValue || hasDefaultPlan
    })
    setToggleStates(newToggleStates)
  }, [defaultSettlementOptions, formValues])

  const {
    analysisResultOptions: {
      data: analysisResultOptions,
      isLoading: isLoadingAnalysisOptions,
    },
    cycleDefinitions: {
      data: cycleDefinitions,
      isLoading: isLoadingCycleDefinitions,
    },
  } = useConfigurations({ effectiveDate })

  const analysisResultOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    if (analysisResultOptions) {
      analysisResultOptions.forEach((option) => {
        const code = `${option.code}`
        const name = `${option.name}`
        optionsMap.set(code, name)
      })
    }
    return optionsMap
  }, [analysisResultOptions])

  const settlementCycleOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    if (cycleDefinitions) {
      cycleDefinitions.forEach((option) => {
        const code = `${option.code}`
        const name = `${option.description}`
        if (option.cycleType === 'SETTLEMENT') {
          optionsMap.set(code, name)
        }
      })
    }
    return optionsMap
  }, [cycleDefinitions])

  const allOptionsMaps = useMemo<
    Map<SettlementOptionItem['field'], Map<string, string>>
  >(() => {
    const optionsMap: Map<
      SettlementOptionItem['field'],
      Map<string, string>
    > = new Map()
    optionsMap.set('analysisResultOptionsPlanCode', analysisResultOptionsMap)
    optionsMap.set('settlementCyclePlanCode', settlementCycleOptionsMap)
    return optionsMap
  }, [analysisResultOptionsMap, settlementCycleOptionsMap])

  const columns = useMemo<SettlementOptionsColumnDef[]>(() => {
    return [
      {
        header: 'Override',
        meta: {
          className: 'w-30  ml-6 mr-6',
        },
        cell: ({ row }) => {
          const field = row.original.field as SettlementOptionItem['field']
          // Use toggle state instead of field value
          const isToggleEnabled = toggleStates[field] || false

          return (
            <ToggleSwitch
              key={`${field}-toggle`}
              checked={isToggleEnabled}
              onChange={(checked: boolean) => {
                // Update toggle state
                setToggleStates(prev => ({ ...prev, [field]: checked }))

                if (checked) {
                  // When enabling override, set the default plan code if available, otherwise empty
                  const defaultOption = defaultSettlementOptions.find(opt => opt.field === field)
                  const defaultPlanCode = defaultOption?.planCode || ''
                  form.setFieldValue(field as any, defaultPlanCode)
                } else {
                  // If disabling override, clear the field value and expiry
                  form.setFieldValue(field as any, null)
                  form.setFieldValue(`${field}Expiry` as any, null)
                }
              }}
            />
          )
        },
      },
      {
        header: 'Settlement setting',
        accessorKey: 'name',
        meta: {
          className: 'ml-4 basis-full',
        },
      },
      {
        header: 'Plan',
        meta: {
          className: 'basis-full',
        },
        cell: ({ row }) => {
          const field = row.original.field as SettlementOptionItem['field']
          const fieldValue = formValues?.[field]
          const optionsMap = allOptionsMaps.get(field)
          // Use toggle state instead of field value
          const isToggleEnabled = toggleStates[field] || false

          // Get default value from accountTypeOverride
          const defaultOption = defaultSettlementOptions.find(opt => opt.field === field)

          if (!optionsMap) {
            return <div className='min-w-80'>Loading options...</div>
          }

          return (
            <div className='min-w-80'>
              {
                isToggleEnabled ? <SelectSettlementOption
                  field={field}
                  value={fieldValue || ''}
                  optionsMap={optionsMap}
                  disabled={!isToggleEnabled}
                  defaultValue={defaultOption?.plan || 'Default'}
                  onChange={(value) => {
                    // Always allow changes when component is not disabled
                    form.setFieldValue(field as any, value)
                  }}
                /> : <div>{defaultOption?.plan || 'Default'}</div>
              }
            </div>
          )
        },
      },
      {
        header: 'Expiration date',
        meta: {
          className: 'ml-4 basis-full',
        },
        cell: ({ row }) => {
          const field = row.original.field
          const expiryValue = formValues?.[`${field}Expiry`]
          // Use toggle state instead of field value
          const isToggleEnabled = toggleStates[field] || false

          return (
            <EditSettlementOptionExpirationDate
              isVisible={isToggleEnabled}
              expiry={expiryValue || undefined}
              disabled={!isToggleEnabled}
              onCheckboxChange={(checked: boolean) => {
                // Always allow changes when component is not disabled
                form.setFieldValue(
                  `${field}Expiry` as any,
                  checked ? undefined : toUTCDateString(new Date()),
                )
              }}
              onMonthPickerChange={(date: string) => {
                // Always allow changes when component is not disabled
                form.setFieldValue(
                  `${field}Expiry` as any,
                  formatToServerString(date),
                )
              }}
            />
          )
        },
      },
    ]
  }, [allOptionsMaps, formValues, form, toggleStates, setToggleStates, defaultSettlementOptions])

  // Show loading state if data is not yet available
  if (isLoadingAnalysisOptions || isLoadingCycleDefinitions) {
    return (
      <>
        <InfoSubSectionTitle>Settlement options</InfoSubSectionTitle>
        <InfoSectionDescription className='-mt-3'>
          These options are inherited from the account type of the key account.
        </InfoSectionDescription>
        <div>Loading settlement options...</div>
      </>
    )
  }

  // Show loading state if accountTypeOverride is not available
  // Note: Removed the loading check as it was causing infinite loading
  // The form should always have default values initialized

  return (
    <>
      <InfoSubSectionTitle>Settlement options</InfoSubSectionTitle>
      <InfoSectionDescription className='-mt-3'>
        These options are inherited from the account type of the key account.
      </InfoSectionDescription>

      <SortedTable
        data={settlementOptions}
        columns={columns}
        columnFilters={[]}
      />
    </>
  )
}

function SelectSettlementOption({
  field,
  value,
  optionsMap,
  disabled = false,
  defaultValue,
  onChange,
}: SelectSettlementOptionProps) {
  if (disabled) {
    // Show default value when disabled (similar to view page)
    return (
      <div className='mt-6 min-w-96 px-3 py-2 text-gray-700 bg-gray-50 border border-gray-200 rounded'>
        <div className='flex flex-row gap-2'>
          <div>{defaultValue}</div>
        </div>
      </div>
    )
  }

  return (
    <Select
      className='mt-6 min-w-96'
      name={field}
      value={value}
      options={Array.from(optionsMap.keys())}
      disabled={disabled}
      onChange={onChange}
      renderOption={(option) => {
        const optionName = optionsMap.get(option)
        return `${optionName} ${option}`
      }}
      renderSelected={(option) => {
        const optionName = optionsMap.get(option)
        return (
          <div className='flex flex-row gap-2'>
            <div>{optionName}</div>
            <div>{option}</div>
          </div>
        )
      }}
    />
  )
}

function EditSettlementOptionExpirationDate({
  expiry,
  isVisible,
  disabled = false,
  onCheckboxChange,
  onMonthPickerChange,
}: EditSettlementOptionExpirationDateProps) {
  return (
    isVisible && (
      <div className='flex flex-row gap-3'>
        <Checkbox
          checked={!expiry}
          label='No expiration'
          disabled={disabled}
          onChange={onCheckboxChange}
        />
        {expiry && !disabled && (
          <MonthPicker
            className='my-1 h-9 min-w-32 bg-white ring-1 ring-inset ring-gray-300'
            initialDate={expiry}
            onDateChange={onMonthPickerChange}
          />
        )}
        {expiry && disabled && (
          <div className='my-1 h-9 min-w-32 flex items-center px-3 text-gray-500 bg-gray-100 border border-gray-300 rounded'>
            {expiry}
          </div>
        )}
      </div>
    )
  )
}
