'use client'

import {
  InfoSectionDescription,
  InfoSubSectionTitle,
} from '@/components/InfoSection'

import { ColumnDef } from '@tanstack/react-table'
import { useMemo } from 'react'
import { SortedTable } from '@/components/Table/SortedTable'
import { Select } from '@/components/Input/Select'
import { useStore, FormApi } from '@tanstack/react-form'
import { AccountTypeOverrideForm } from '@/api/formToApiSchema'
import { Checkbox } from '@/components/Checkbox'
import { MonthPicker } from '@/components/Filter/MonthPicker'
import { formatToServerString, toUTCDateString } from '@/lib/date'
import { useConfigurations } from '@/app/[bankId]/configuration/_hooks/useConfigurations'
import { ToggleSwitch } from '@/components/Toggle'

type SettlementOptionItem = {
  field: 'analysisResultOptionsPlanCode' | 'settlementCyclePlanCode'
  name: string
}

const settlementOptions: SettlementOptionItem[] = [
  {
    field: 'analysisResultOptionsPlanCode',
    name: 'Analysis result options',
  },
  {
    field: 'settlementCyclePlanCode',
    name: 'Settlement cycle',
  },
]

type SettlementOptionsColumnDef = ColumnDef<SettlementOptionItem>

interface EditSettlementOptionsProps {
  form: FormApi<AccountTypeOverrideForm, undefined>,
  effectiveDate: string
}

interface SelectSettlementOptionProps {
  field: string
  value: string
  optionsMap: Map<string, string>
  disabled?: boolean
  onChange: (value: string) => void
}

interface EditSettlementOptionExpirationDateProps {
  expiry?: string
  isVisible: boolean
  disabled?: boolean
  onCheckboxChange: (checked: boolean) => void
  onMonthPickerChange: (date: string) => void
}

export default function EditSettlementOptions({
  form,
  effectiveDate,
}: EditSettlementOptionsProps) {
  // The edit page uses a flat form structure where fields are at the root level
  const [formValues] = useStore(form.store, (state: any) => [state.values])

  const {
    analysisResultOptions: {
      data: analysisResultOptions,
      isLoading: isLoadingAnalysisOptions,
    },
    cycleDefinitions: {
      data: cycleDefinitions,
      isLoading: isLoadingCycleDefinitions,
    },
  } = useConfigurations({ effectiveDate })

  const analysisResultOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    if (analysisResultOptions) {
      analysisResultOptions.forEach((option) => {
        const code = `${option.code}`
        const name = `${option.name}`
        optionsMap.set(code, name)
      })
    }
    return optionsMap
  }, [analysisResultOptions])

  const settlementCycleOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    if (cycleDefinitions) {
      cycleDefinitions.forEach((option) => {
        const code = `${option.code}`
        const name = `${option.description}`
        if (option.cycleType === 'SETTLEMENT') {
          optionsMap.set(code, name)
        }
      })
    }
    return optionsMap
  }, [cycleDefinitions])

  const allOptionsMaps = useMemo<
    Map<SettlementOptionItem['field'], Map<string, string>>
  >(() => {
    const optionsMap: Map<
      SettlementOptionItem['field'],
      Map<string, string>
    > = new Map()
    optionsMap.set('analysisResultOptionsPlanCode', analysisResultOptionsMap)
    optionsMap.set('settlementCyclePlanCode', settlementCycleOptionsMap)
    return optionsMap
  }, [analysisResultOptionsMap, settlementCycleOptionsMap])

  const columns = useMemo<SettlementOptionsColumnDef[]>(() => {
    return [
      {
        header: 'Override',
        meta: {
          className: 'w-30  ml-6 mr-6',
        },
        cell: ({ row }) => {
          const field = row.original.field as SettlementOptionItem['field']
          const fieldValue = formValues?.[field]
          // Toggle is checked if the field has a non-null value
          const isOverridden = !!fieldValue

          return (
            <ToggleSwitch
              key={`${field}-${isOverridden}`} // Force re-render when state changes
              checked={isOverridden}
              onChange={(checked: boolean) => {
                if (checked) {
                  // When enabling override, set a default empty value to make field editable
                  // User will then select the actual value
                  form.setFieldValue(field as any, '')
                } else {
                  // If disabling override, clear the field value and expiry
                  form.setFieldValue(field as any, null)
                  form.setFieldValue(`${field}Expiry` as any, null)
                }
              }}
            />
          )
        },
      },
      {
        header: 'Settlement setting',
        accessorKey: 'name',
        meta: {
          className: 'ml-4 basis-full',
        },
      },
      {
        header: 'Plan',
        meta: {
          className: 'basis-full',
        },
        cell: ({ row }) => {
          const field = row.original.field as SettlementOptionItem['field']
          const fieldValue = formValues?.[field]
          const optionsMap = allOptionsMaps.get(field)
          // Check if override is enabled (field is not null)
          const isOverridden = fieldValue !== null && fieldValue !== undefined

          if (!optionsMap) {
            return <div className='min-w-80'>Loading options...</div>
          }

          return (
            <div className='min-w-80 pb-6'>
              <SelectSettlementOption
                field={field}
                value={fieldValue || ''}
                optionsMap={optionsMap}
                disabled={!isOverridden}
                onChange={(value) => {
                  // Always allow changes when component is not disabled
                  form.setFieldValue(field as any, value)
                }}
              />
            </div>
          )
        },
      },
      {
        header: 'Expiration date',
        meta: {
          className: 'ml-4 basis-full',
        },
        cell: ({ row }) => {
          const field = row.original.field
          const fieldValue = formValues?.[field]
          const expiryValue = formValues?.[`${field}Expiry`]
          // Check if override is enabled (field is not null)
          const isOverridden = fieldValue !== null && fieldValue !== undefined

          return (
            <EditSettlementOptionExpirationDate
              isVisible={isOverridden}
              expiry={expiryValue || undefined}
              disabled={!isOverridden}
              onCheckboxChange={(checked: boolean) => {
                // Always allow changes when component is not disabled
                form.setFieldValue(
                  `${field}Expiry` as any,
                  checked ? undefined : toUTCDateString(new Date()),
                )
              }}
              onMonthPickerChange={(date: string) => {
                // Always allow changes when component is not disabled
                form.setFieldValue(
                  `${field}Expiry` as any,
                  formatToServerString(date),
                )
              }}
            />
          )
        },
      },
    ]
  }, [allOptionsMaps, formValues, form])

  // Show loading state if data is not yet available
  if (isLoadingAnalysisOptions || isLoadingCycleDefinitions) {
    return (
      <>
        <InfoSubSectionTitle>Settlement options</InfoSubSectionTitle>
        <InfoSectionDescription className='-mt-3'>
          These options are inherited from the account type of the key account.
        </InfoSectionDescription>
        <div>Loading settlement options...</div>
      </>
    )
  }

  // Show loading state if accountTypeOverride is not available
  // Note: Removed the loading check as it was causing infinite loading
  // The form should always have default values initialized

  return (
    <>
      <InfoSubSectionTitle>Settlement options</InfoSubSectionTitle>
      <InfoSectionDescription className='-mt-3'>
        These options are inherited from the account type of the key account.
      </InfoSectionDescription>

      <SortedTable
        data={settlementOptions}
        columns={columns}
        columnFilters={[]}
      />
    </>
  )
}

function SelectSettlementOption({
  field,
  value,
  optionsMap,
  disabled = false,
  onChange,
}: SelectSettlementOptionProps) {
  if (disabled) {
    // Show default value when disabled (similar to view page)
    return (
      <div className='mt-6 min-w-96 px-3 py-2 text-gray-700 bg-gray-50 border border-gray-200 rounded'>
        <div className='flex flex-row gap-2'>
          <div className='text-gray-500'>Default:</div>
          <div>Inherited from account type</div>
        </div>
      </div>
    )
  }

  return (
    <Select
      className='mt-6 min-w-96'
      name={field}
      value={value}
      options={Array.from(optionsMap.keys())}
      disabled={disabled}
      onChange={onChange}
      renderOption={(option) => {
        const optionName = optionsMap.get(option)
        return `${optionName} ${option}`
      }}
      renderSelected={(option) => {
        const optionName = optionsMap.get(option)
        return (
          <div className='flex flex-row gap-2'>
            <div>{optionName}</div>
            <div>{option}</div>
          </div>
        )
      }}
    />
  )
}

function EditSettlementOptionExpirationDate({
  expiry,
  isVisible,
  disabled = false,
  onCheckboxChange,
  onMonthPickerChange,
}: EditSettlementOptionExpirationDateProps) {
  return (
    isVisible && (
      <div className='flex flex-row gap-3'>
        <Checkbox
          checked={!expiry}
          label='No expiration'
          disabled={disabled}
          onChange={onCheckboxChange}
        />
        {expiry && !disabled && (
          <MonthPicker
            className='my-1 h-9 min-w-32 bg-white ring-1 ring-inset ring-gray-300'
            initialDate={expiry}
            onDateChange={onMonthPickerChange}
          />
        )}
        {expiry && disabled && (
          <div className='my-1 h-9 min-w-32 flex items-center px-3 text-gray-500 bg-gray-100 border border-gray-300 rounded'>
            {expiry}
          </div>
        )}
      </div>
    )
  )
}
