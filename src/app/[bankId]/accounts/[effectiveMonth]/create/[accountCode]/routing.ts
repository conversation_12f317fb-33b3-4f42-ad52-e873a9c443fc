import { defineChildRoute, defineRoute, defineRoutes } from '@/lib/defineRoute'
import { accountsEffectiveDate } from '../../routing'

export const createAccount = defineChildRoute(
  accountsEffectiveDate,
  defineRoute('create', ['accountCode']),
)

export const addServiceOverride = defineChildRoute(
  createAccount,
  defineRoute('service-override'),
)

export const [useRoute, routeTo] = defineRoutes(
  createAccount,
  addServiceOverride,
)
