'use client'

import { useMemo } from 'react'
import { ColumnDef } from '@tanstack/react-table'
import { SortedTable } from '@/components/Table/SortedTable'
import {
  InfoSectionTitle,
  InfoSectionDescription,
} from '@/components/InfoSection'
import { SettlementProcessingOptionItemWithId } from '@/app/[bankId]/accounts/types'
import { CheckIcon } from '@heroicons/react/24/outline'
import { useConfigurations } from '@/app/[bankId]/configuration/_hooks/useConfigurations'

interface ProcessingOptionsProps {
  processingOptions: SettlementProcessingOptionItemWithId[]
  effectiveDate: string
}

export default function ProcessingOptions({
  processingOptions,
  effectiveDate,
}: ProcessingOptionsProps) {
  const processingOptionsData = processingOptions

  // Get configuration data to format plan names consistently with edit components
  const {
    earningsCreditDefinitions: { data: earningsCreditDefinitions },
    investableBalanceDefinitions: { data: investableBalanceDefinitions },
    requiredBalanceDefinitions: { data: requiredBalanceDefinitions },
    cycleDefinitions: { data: cycleDefinitions },
    statementFormatPlans: { data: statementFormatPlans },
  } = useConfigurations({ effectiveDate })

  // Create option maps for plan name formatting (matching EditProcessingOptions)
  const earningsCreditOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    if (earningsCreditDefinitions) {
      earningsCreditDefinitions.forEach((option) => {
        const code = `${option.code}`
        const name = `${option.description}`
        optionsMap.set(code, name)
      })
    }
    return optionsMap
  }, [earningsCreditDefinitions])

  const investableBalanceOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    if (investableBalanceDefinitions) {
      investableBalanceDefinitions.forEach((option) => {
        const code = `${option.code}`
        const name = `${option.name}`
        optionsMap.set(code, name)
      })
    }
    return optionsMap
  }, [investableBalanceDefinitions])

  const requiredBalanceOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    if (requiredBalanceDefinitions) {
      requiredBalanceDefinitions.forEach((option) => {
        const code = `${option.code}`
        const name = `${option.name}`
        optionsMap.set(code, name)
      })
    }
    return optionsMap
  }, [requiredBalanceDefinitions])

  const statementCycleOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    if (cycleDefinitions) {
      cycleDefinitions.forEach((option) => {
        const code = `${option.code}`
        const name = `${option.description}`
        if (option.cycleType === 'STATEMENT') {
          optionsMap.set(code, name)
        }
      })
    }
    return optionsMap
  }, [cycleDefinitions])

  const statementFormatOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    if (statementFormatPlans) {
      statementFormatPlans.forEach((option) => {
        const code = `${option.code}`
        const name = `${option.description}`
        optionsMap.set(code, name)
      })
    }
    return optionsMap
  }, [statementFormatPlans])

  // Helper function to format plan display
  const formatPlanDisplay = (planCode: string | null | undefined, field: string) => {
    if (!planCode) return 'Inherited from account type'

    let optionsMap: Map<string, string> | undefined
    if (field === 'earningsCreditDefinitionCode') {
      optionsMap = earningsCreditOptionsMap
    } else if (field === 'investableBalanceDefinitionCode') {
      optionsMap = investableBalanceOptionsMap
    } else if (field === 'balanceRequirementDefinitionCode') {
      optionsMap = requiredBalanceOptionsMap
    } else if (field === 'statementCyclePlanCode') {
      optionsMap = statementCycleOptionsMap
    } else if (field === 'statementFormatPlanCode') {
      optionsMap = statementFormatOptionsMap
    }

    if (optionsMap && optionsMap.has(planCode)) {
      const planName = optionsMap.get(planCode)
      return `${planName} ${planCode}`
    }

    return planCode
  }

  const columns = useMemo<
    ColumnDef<SettlementProcessingOptionItemWithId>[]
  >(() => {
    return [
      {
        header: 'Override',
        accessorKey: 'override',
        meta: {
          className: 'w-24 ml-6',
        },
        cell: ({ row }) => (
          <div className='flex justify-center'>
            {row.original.override ?
              <CheckIcon className='ml-2 size-6' />
            : null}
          </div>
        ),
      },
      {
        header: 'Processing setting',
        accessorKey: 'name',
        meta: {
          className: 'flex-1 min-w-0 px-6',
        },
      },
      {
        header: 'Plan',
        meta: {
          className: 'w-60 flex-shrink-0 px-6',
        },
        cell: ({ row }) => {
          return formatPlanDisplay(row.original.planCode, row.original.field)
        },
      },
      {
        header: 'Expiration date',
        accessorKey: 'expiry',
        meta: {
          className: 'w-40 flex-shrink-0 px-6',
        },
        cell: ({ row }) => row.original.expiry || 'No expiration',
      },
    ]
  }, [
    earningsCreditOptionsMap,
    investableBalanceOptionsMap,
    requiredBalanceOptionsMap,
    statementCycleOptionsMap,
    statementFormatOptionsMap,
  ])

  return (
    <div className='mt-12 space-y-4'>
      <div>
        <InfoSectionTitle>Processing options</InfoSectionTitle>
      </div>
      <SortedTable data={processingOptionsData} columns={columns} />
    </div>
  )
}
