'use client'

import { useMemo } from 'react'
import { ColumnDef } from '@tanstack/react-table'
import { SortedTable } from '@/components/Table/SortedTable'
import {
  InfoSectionTitle,
  InfoSectionDescription,
} from '@/components/InfoSection'
import { SettlementOptionItem } from '@/api/apiToFormSchema'
import { CheckIcon } from '@heroicons/react/24/outline'

interface SettlementOptionsProps {
  settlementOptions: SettlementOptionItem[]
}

export default function SettlementOptions({
  settlementOptions,
}: SettlementOptionsProps) {
  const settlementOptionsData = settlementOptions

  const columns = useMemo<ColumnDef<SettlementOptionItem>[]>(() => {
    return [
      {
        header: 'Override',
        accessorKey: 'override',
        meta: {
          className: 'w-24 ml-6',
        },
        cell: ({ row }) => (
          <div className='flex justify-center'>
            {row.original.override ?
              <CheckIcon className='ml-2 size-6' />
            : null}
          </div>
        ),
      },
      {
        header: 'Settlement setting',
        accessorKey: 'name',
        meta: {
          className: 'flex-1 min-w-0 px-6',
        },
      },
      {
        header: 'Plan',
        accessorKey: 'plan',
        meta: {
          className: 'w-60 flex-shrink-0 px-6',
        },
      },
      {
        header: 'Expiration date',
        accessorKey: 'expiry',
        meta: {
          className: 'w-40 flex-shrink-0 px-6',
        },
        cell: ({ row }) => row.original.expiry || 'No expiration',
      },
    ]
  }, [])

  return (
    <div className='mt-12 space-y-4'>
      <div>
        <InfoSectionTitle>Settlement options</InfoSectionTitle>
      </div>
      <SortedTable data={settlementOptionsData} columns={columns} />
    </div>
  )
}
