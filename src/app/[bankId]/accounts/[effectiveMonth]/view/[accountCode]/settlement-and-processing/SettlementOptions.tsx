'use client'

import { useMemo } from 'react'
import { ColumnDef } from '@tanstack/react-table'
import { SortedTable } from '@/components/Table/SortedTable'
import {
  InfoSectionTitle,
  InfoSectionDescription,
} from '@/components/InfoSection'
import { SettlementProcessingOptionItemWithId } from '@/app/[bankId]/accounts/types'
import { CheckIcon } from '@heroicons/react/24/outline'
import { useConfigurations } from '@/app/[bankId]/configuration/_hooks/useConfigurations'

interface SettlementOptionsProps {
  settlementOptions: SettlementProcessingOptionItemWithId[]
  effectiveDate: string
}

export default function SettlementOptions({
  settlementOptions,
  effectiveDate,
}: SettlementOptionsProps) {
  const settlementOptionsData = settlementOptions

  // Get configuration data to format plan names consistently with edit components
  const {
    analysisResultOptions: { data: analysisResultOptions },
    cycleDefinitions: { data: cycleDefinitions },
  } = useConfigurations({ effectiveDate })

  // Create option maps for plan name formatting
  const analysisResultOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    if (analysisResultOptions) {
      analysisResultOptions.forEach((option) => {
        const code = `${option.code}`
        const name = `${option.name}`
        optionsMap.set(code, name)
      })
    }
    return optionsMap
  }, [analysisResultOptions])

  const settlementCycleOptionsMap = useMemo<Map<string, string>>(() => {
    const optionsMap: Map<string, string> = new Map()
    if (cycleDefinitions) {
      cycleDefinitions.forEach((option) => {
        const code = `${option.code}`
        const name = `${option.description}`
        if (option.cycleType === 'SETTLEMENT') {
          optionsMap.set(code, name)
        }
      })
    }
    return optionsMap
  }, [cycleDefinitions])

  // Helper function to format plan display
  const formatPlanDisplay = (planCode: string | null | undefined, field: string) => {
    if (!planCode) return 'Inherited from account type'

    let optionsMap: Map<string, string> | undefined
    if (field === 'analysisResultOptionsPlanCode') {
      optionsMap = analysisResultOptionsMap
    } else if (field === 'settlementCyclePlanCode') {
      optionsMap = settlementCycleOptionsMap
    }

    if (optionsMap && optionsMap.has(planCode)) {
      const planName = optionsMap.get(planCode)
      return `${planName} ${planCode}`
    }

    return planCode
  }

  const columns = useMemo<
    ColumnDef<SettlementProcessingOptionItemWithId>[]
  >(() => {
    return [
      {
        header: 'Override',
        accessorKey: 'override',
        meta: {
          className: 'w-24 ml-6',
        },
        cell: ({ row }) => (
          <div className='flex justify-center'>
            {row.original.override ?
              <CheckIcon className='ml-2 size-6' />
            : null}
          </div>
        ),
      },
      {
        header: 'Settlement setting',
        accessorKey: 'name',
        meta: {
          className: 'flex-1 min-w-0 px-6',
        },
      },
      {
        header: 'Plan',
        meta: {
          className: 'w-60 flex-shrink-0 px-6',
        },
        cell: ({ row }) => {
          return formatPlanDisplay(row.original.planCode, row.original.field)
        },
      },
      {
        header: 'Expiration date',
        accessorKey: 'expiry',
        meta: {
          className: 'w-40 flex-shrink-0 px-6',
        },
        cell: ({ row }) => row.original.expiry || 'No expiration',
      },
    ]
  }, [analysisResultOptionsMap, settlementCycleOptionsMap])

  return (
    <div className='mt-12 space-y-4'>
      <div>
        <InfoSectionTitle>Settlement options</InfoSectionTitle>
      </div>
      <SortedTable data={settlementOptionsData} columns={columns} />
    </div>
  )
}
