import { ServiceOverrideRow } from '@/app/[bankId]/accounts/types'
import { ServiceDetails, ServicePriceForm } from '@/api/formToApiSchema'
import { serviceTypeLabel } from '@/strings/enums'
import { toUIFormat } from '@/lib/date'

export function transformServiceDetailsToRows(
  serviceDetails: ServiceDetails[],
): ServiceOverrideRow[] {
  if (!Array.isArray(serviceDetails)) {
    console.error('Expected array of ServiceDetails, got:', serviceDetails)
    return []
  }

  const rows: ServiceOverrideRow[] = []

  serviceDetails.forEach((serviceDetail) => {
    const { service, servicePrice, servicesInServiceSet } = serviceDetail

    // Create main service row
    const mainRow: ServiceOverrideRow = {
      serviceCode: service.code,
      serviceDescription: service.description || service.code,
      serviceType: serviceTypeLabel(service.serviceType),
      priceType: null,
      priceValue: null,
      disposition: null,
      expirationDate: undefined,
      currency: 'USD',
      startDate: toUIFormat(service.effectiveDate),
      children: [],
    }

    // Add service prices as children
    if (servicePrice && Array.isArray(servicePrice)) {
      servicePrice.forEach((priceItem) => {
        const price = priceItem.servicePrice
        if (price) {
          const priceRow: ServiceOverrideRow = {
            serviceCode: service.code,
            serviceDescription: `${service.description || service.code} - Price Override`,
            serviceType: serviceTypeLabel(service.serviceType),
            priceType: price.priceType ?? null,
            priceValue: price.priceValue ? String(price.priceValue) : null,
            disposition: price.disposition ?? null,
            expirationDate:
              price.expirationDate ?
                toUIFormat(price.expirationDate)
              : undefined,
            currency: price.currency || 'USD',
            startDate: toUIFormat(price.effectiveDate),
          }
          mainRow.children!.push(priceRow)
        }
      })
    }

    // Add services in service set as children (if it's a service set)
    if (servicesInServiceSet && Array.isArray(servicesInServiceSet)) {
      servicesInServiceSet.forEach((subService) => {
        const subServiceRow: ServiceOverrideRow = {
          serviceCode: subService.code,
          serviceDescription: subService.description || subService.code,
          serviceType: serviceTypeLabel(subService.serviceType),
          priceType: null,
          priceValue: null,
          disposition: null,
          expirationDate: undefined,
          currency: 'USD',
          startDate: toUIFormat(subService.effectiveDate),
        }
        mainRow.children!.push(subServiceRow)
      })
    }

    rows.push(mainRow)
  })

  return rows
}

export function formatPrice(
  price: number | null | undefined,
  priceType: ServicePriceForm['priceType'] | undefined,
): string {
  if (!price) return '--'

  if (priceType === 'PERCENTAGE' || priceType === 'INDEXED') {
    return `${price}%`
  }

  return `${price.toFixed(2)}`
}
