'use client'

import { ExpandedTable } from '@/components/Table/ExpandedTable'
import { useAccountFromParams } from '@/app/[bankId]/accounts/_hooks/useAccountFromParams'
import { useRoute } from '../../routing'
import { data } from '@/lib/unions/Union'
import { useMemo } from 'react'
import { ChevronDownIcon, ChevronRightIcon } from '@heroicons/react/24/outline'
import { Button } from '@headlessui/react'
import { useQuery } from '@tanstack/react-query'
import { toAccountCodeString } from '@/app/[bankId]/accounts/accountHelpers'
import { ServiceOverrideColumnDef } from '@/app/[bankId]/accounts/types'
import { transformServiceDetailsToRows } from '../utils'
import { accountQueries } from '@/app/[bankId]/accounts/queries'
import { toUIFormat } from '@/lib/date'

export default function Page() {
  const route = useRoute()!
  const routeParams = data(route).params
  const effectiveDate = toUIFormat(routeParams.effectiveMonth)
  const [applicationId, bankNumber, accountNumber] =
    routeParams.accountCode.split('-') as ['C' | 'D', string, string]
  const accountCode = { applicationId, bankNumber, accountNumber }

  // Fetch service overrides data from API
  const {
    data: serviceOverridesResponse,
    isLoading,
    error,
  } = useQuery({
    ...accountQueries('/getServiceOverridesByAccount', {
      code: toAccountCodeString({ bankNumber, applicationId, accountNumber }),
      effectiveDate,
    }),
  })

  const hierarchicalData = useMemo(() => {
    if (!serviceOverridesResponse) return []
    return transformServiceDetailsToRows(serviceOverridesResponse)
  }, [serviceOverridesResponse])

  const columns = useMemo<ServiceOverrideColumnDef[]>(
    () => [
      {
        id: 'name',
        accessorKey: 'name',
        header: 'Name',
        meta: {
          className: 'basis-1/3',
        },
        cell: ({ row, getValue }) => (
          <div
            style={{
              paddingLeft: `${row.depth * 2}rem`,
            }}
          >
            <div className='flex flex-row gap-2'>
              {row.getCanExpand() && (
                <Button
                  className={'h-6 w-6'}
                  onClick={row.getToggleExpandedHandler()}
                >
                  {row.getIsExpanded() ?
                    <ChevronDownIcon />
                  : <ChevronRightIcon />}
                </Button>
              )}
              <div className='flex flex-row text-nowrap'>
                {getValue<string>()}
              </div>
            </div>
          </div>
        ),
      },
      {
        id: 'code',
        accessorKey: 'code',
        header: 'Code',
        meta: {
          className: 'min-w-[80px]',
        },
      },
      {
        id: 'serviceType',
        accessorKey: 'serviceType',
        header: 'Service type',
        meta: {
          className: 'min-w-[120px]',
        },
      },
      {
        id: 'priceType',
        accessorKey: 'priceType',
        header: 'Price type',
        meta: {
          className: 'min-w-[120px]',
        },
      },
      {
        id: 'price',
        accessorKey: 'price',
        header: 'Price',
        meta: {
          className: 'min-w-[100px]',
        },
      },
      {
        id: 'disposition',
        accessorKey: 'disposition',
        header: 'Disposition',
        meta: {
          className: 'min-w-[100px]',
        },
      },
      {
        id: 'startDate',
        accessorKey: 'startDate',
        header: 'Start date',
        meta: {
          className: 'min-w-[100px]',
        },
      },
      {
        id: 'expirationDate',
        accessorKey: 'expirationDate',
        header: 'Expiration date',
        meta: {
          className: 'min-w-[120px]',
        },
      },
    ],
    [],
  )

  if (isLoading) {
    return <div>Loading service overrides...</div>
  }

  if (error) {
    return (
      <div className='text-red-600'>
        Error loading service overrides: {String(error)}
      </div>
    )
  }

  if (!accountCode || !effectiveDate) {
    return <div>Account code and effective date are required</div>
  }

  return (
    <ExpandedTable
      data={hierarchicalData}
      columns={columns}
      getSubRows={(row) => row.children}
    />
  )
}
