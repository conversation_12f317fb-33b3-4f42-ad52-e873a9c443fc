import { defineChildRoute, defineRoute, defineRoutes } from '@/lib/defineRoute'
import { accountsEffectiveDate } from '../../routing'

export const viewAccount = defineChildRoute(
  accountsEffectiveDate,
  defineRoute('view', ['accountCode']),
)

export const viewAccountDetails = defineChildRoute(
  viewAccount,
  defineRoute('details'),
)

export const viewAccountStatements = defineChildRoute(
  viewAccount,
  defineRoute('statements'),
)

export const viewAccountRelationships = defineChildRoute(
  viewAccount,
  defineRoute('relationships'),
)

export const viewSettlementAndProcessing = defineChildRoute(
  viewAccount,
  defineRoute('settlement-and-processing'),
)

export const viewServiceOverrides = defineChildRoute(
  viewAccount,
  defineRoute('service-overrides'),
)

export const viewCurrentAndRelatedOverrides = defineChildRoute(
  viewServiceOverrides,
  defineRoute('current-and-related'),
)

export const accountOnlyOverrides = defineChildRoute(
  viewServiceOverrides,
  defineRoute('account-only'),
)

export const servicePricing = defineChildRoute(
  viewAccount,
  defineRoute('service-pricing'),
)

export const statementPackage = defineChildRoute(
  viewAccount,
  defineRoute('statement-package'),
)

export const [useRoute, routeTo] = defineRoutes(
  viewAccount,
  viewAccountDetails,
  viewAccountStatements,
  viewAccountRelationships,
  viewSettlementAndProcessing,
  viewServiceOverrides,
  viewCurrentAndRelatedOverrides,
  accountOnlyOverrides,
  servicePricing,
  statementPackage,
)
